// abstractactioneditor.sip generated by MetaSIP
//
// This file is part of the QtDesigner Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDesignerActionEditorInterface : public QWidget
{
%TypeHeaderCode
#include <abstractactioneditor.h>
%End

public:
    QDesignerActionEditorInterface(QWidget *parent /TransferThis/, Qt::WindowFlags flags = {});
    virtual ~QDesignerActionEditorInterface();
    virtual QDesignerFormEditorInterface *core() const;
    virtual void manageAction(QAction *action) = 0;
    virtual void unmanageAction(QAction *action) = 0;

public slots:
    virtual void setFormWindow(QDesignerFormWindowInterface *formWindow /KeepReference/) = 0;
};
