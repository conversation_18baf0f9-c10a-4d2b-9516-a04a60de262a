// qaudioengine.sip generated by MetaSIP
//
// This file is part of the QtSpatialAudio Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_5_0 -)

class QAudioEngine : public QObject
{
%TypeHeaderCode
#include <qaudioengine.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QAmbientSound, &sipType_QAmbientSound, -1, 1},
        {sipName_QAudioEngine, &sipType_QAudioEngine, -1, 2},
        {sipName_QAudioListener, &sipType_QAudioListener, -1, 3},
        {sipName_QAudioRoom, &sipType_QAudioRoom, -1, 4},
        {sipName_QSpatialSound, &sipType_QSpatialSound, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    enum OutputMode
    {
        Surround,
        Stereo,
        Headphone,
    };

    QAudioEngine();
    explicit QAudioEngine(QObject *parent /TransferThis/);
    QAudioEngine(int sampleRate, QObject *parent /TransferThis/ = 0);
    virtual ~QAudioEngine();
    void setOutputMode(QAudioEngine::OutputMode mode);
    QAudioEngine::OutputMode outputMode() const;
    int sampleRate() const;
    void setOutputDevice(const QAudioDevice &device);
    QAudioDevice outputDevice() const;
    void setMasterVolume(float volume);
    float masterVolume() const;
    void setPaused(bool paused);
    bool paused() const;
    void setRoomEffectsEnabled(bool enabled);
    bool roomEffectsEnabled() const;
    static const float DistanceScaleCentimeter;
    static const float DistanceScaleMeter;
    void setDistanceScale(float scale);
    float distanceScale() const;

signals:
    void outputModeChanged();
    void outputDeviceChanged();
    void masterVolumeChanged();
    void pausedChanged();
    void distanceScaleChanged();

public slots:
    void start();
    void stop();
    void pause();
    void resume();
};

%End
