// qsgrendererinterface.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSGRendererInterface /NoDefaultCtors/
{
%TypeHeaderCode
#include <qsgrendererinterface.h>
%End

public:
    enum GraphicsApi
    {
        Unknown,
        Software,
        OpenGL,
        OpenVG,
        OpenGLRhi,
        Direct3D11Rhi,
        VulkanRhi,
        MetalRhi,
        NullRhi,
        Direct3D11,
        Vulkan,
        Metal,
%If (Qt_6_6_0 -)
        Direct3D12,
%End
        Null,
    };

    enum Resource
    {
        DeviceResource,
        CommandQueueResource,
        CommandListResource,
        PainterResource,
        RhiResource,
        PhysicalDeviceResource,
        OpenGLContextResource,
        DeviceContextResource,
        CommandEncoderResource,
        VulkanInstanceResource,
        RenderPassResource,
        RhiSwapchainResource,
        RhiRedirectCommandBuffer,
        RhiRedirectRenderTarget,
%If (Qt_6_4_0 -)
        RedirectPaintDevice,
%End
%If (Qt_6_6_0 -)
        GraphicsQueueFamilyIndexResource,
%End
%If (Qt_6_6_0 -)
        GraphicsQueueIndexResource,
%End
    };

    enum ShaderType
    {
        UnknownShadingLanguage,
        GLSL,
        HLSL,
        RhiShader,
    };

    enum ShaderCompilationType /BaseType=Flag/
    {
        RuntimeCompilation,
        OfflineCompilation,
    };

    typedef QFlags<QSGRendererInterface::ShaderCompilationType> ShaderCompilationTypes;

    enum ShaderSourceType /BaseType=Flag/
    {
        ShaderSourceString,
        ShaderSourceFile,
        ShaderByteCode,
    };

    typedef QFlags<QSGRendererInterface::ShaderSourceType> ShaderSourceTypes;
    virtual ~QSGRendererInterface();
    virtual QSGRendererInterface::GraphicsApi graphicsApi() const = 0;
    virtual void *getResource(QQuickWindow *window, QSGRendererInterface::Resource resource) const;
    virtual void *getResource(QQuickWindow *window, const char *resource) const;
    virtual QSGRendererInterface::ShaderType shaderType() const = 0;
    virtual QSGRendererInterface::ShaderCompilationTypes shaderCompilationType() const = 0;
    virtual QSGRendererInterface::ShaderSourceTypes shaderSourceType() const = 0;
    static bool isApiRhiBased(QSGRendererInterface::GraphicsApi api);

    enum RenderMode
    {
        RenderMode2D,
        RenderMode2DNoDepthBuffer,
        RenderMode3D,
    };
};
