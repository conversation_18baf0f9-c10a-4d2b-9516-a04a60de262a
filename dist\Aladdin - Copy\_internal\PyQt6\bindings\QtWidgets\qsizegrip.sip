// qsizegrip.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSizeGrip : public QWidget
{
%TypeHeaderCode
#include <qsizegrip.h>
%End

public:
    explicit QSizeGrip(QWidget *parent /TransferThis/);
    virtual ~QSizeGrip();
    virtual QSize sizeHint() const;
    virtual void setVisible(bool);

protected:
    virtual void paintEvent(QPaintEvent *);
    virtual void mousePressEvent(QMouseEvent *);
    virtual void mouseReleaseEvent(QMouseEvent *mouseEvent);
    virtual void mouseMoveEvent(QMouseEvent *);
    virtual bool eventFilter(QObject *, QEvent *);
    virtual bool event(QEvent *);
    virtual void moveEvent(QMoveEvent *moveEvent);
    virtual void showEvent(QShowEvent *showEvent);
    virtual void hideEvent(QHideEvent *hideEvent);
};
