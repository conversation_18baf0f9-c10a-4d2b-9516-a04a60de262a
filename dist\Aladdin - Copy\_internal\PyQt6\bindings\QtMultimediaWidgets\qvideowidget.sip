// qvideowidget.sip generated by MetaSIP
//
// This file is part of the QtMultimediaWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QVideoWidget : public QWidget
{
%TypeHeaderCode
#include <qvideowidget.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QGraphicsVideoItem, &sipType_QGraphicsVideoItem, -1, 1},
        {sipName_QVideoWidget, &sipType_QVideoWidget, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    explicit QVideoWidget(QWidget *parent /TransferThis/ = 0);
    virtual ~QVideoWidget();
    QVideoSink *videoSink() const;
    Qt::AspectRatioMode aspectRatioMode() const;
    bool isFullScreen() const;
    virtual QSize sizeHint() const;

public slots:
    void setFullScreen(bool fullScreen);
    void setAspectRatioMode(Qt::AspectRatioMode mode);

signals:
    void fullScreenChanged(bool fullScreen);
    void aspectRatioModeChanged(Qt::AspectRatioMode mode);

protected:
    virtual bool event(QEvent *event);
    virtual void showEvent(QShowEvent *event);
    virtual void hideEvent(QHideEvent *event);
    virtual void resizeEvent(QResizeEvent *event);
    virtual void moveEvent(QMoveEvent *event);
};

%End
