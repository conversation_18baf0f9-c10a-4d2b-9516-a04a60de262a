// qeventloop.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QEventLoop : public QObject
{
%TypeHeaderCode
#include <qeventloop.h>
%End

public:
    explicit QEventLoop(QObject *parent /TransferThis/ = 0);
    virtual ~QEventLoop();

    enum ProcessEventsFlag /BaseType=Flag/
    {
        AllEvents,
        ExcludeUserInputEvents,
        ExcludeSocketNotifiers,
        WaitForMoreEvents,
    };

    typedef QFlags<QEventLoop::ProcessEventsFlag> ProcessEventsFlags;
    bool processEvents(QEventLoop::ProcessEventsFlags flags = QEventLoop::AllEvents) /ReleaseGIL/;
    void processEvents(QEventLoop::ProcessEventsFlags flags, int maximumTime) /ReleaseGIL/;
%If (Qt_6_7_0 -)
    void processEvents(QEventLoop::ProcessEventsFlags flags, QDeadlineTimer deadline) /ReleaseGIL/;
%End
    int exec(QEventLoop::ProcessEventsFlags flags = QEventLoop::AllEvents) /PostHook=__pyQtPostEventLoopHook__,PreHook=__pyQtPreEventLoopHook__,ReleaseGIL/;
    void exit(int returnCode = 0);
    bool isRunning() const;
    void wakeUp();

public slots:
    void quit();

public:
    virtual bool event(QEvent *event);
};

class QEventLoopLocker
{
%TypeHeaderCode
#include <qeventloop.h>
%End

public:
    QEventLoopLocker() /ReleaseGIL/;
    explicit QEventLoopLocker(QEventLoop *loop) /ReleaseGIL/;
    explicit QEventLoopLocker(QThread *thread) /ReleaseGIL/;
    ~QEventLoopLocker();
%If (Qt_6_7_0 -)
    void swap(QEventLoopLocker &other);
%End

private:
    QEventLoopLocker(const QEventLoopLocker &);
};
