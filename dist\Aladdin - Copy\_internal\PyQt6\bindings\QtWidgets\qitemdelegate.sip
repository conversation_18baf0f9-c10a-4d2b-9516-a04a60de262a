// qitemdelegate.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QItemDelegate : public QAbstractItemDelegate
{
%TypeHeaderCode
#include <qitemdelegate.h>
%End

public:
    explicit QItemDelegate(QObject *parent /TransferThis/ = 0);
    virtual ~QItemDelegate();
    virtual void paint(Q<PERSON>ain<PERSON> *painter, const QStyleOptionViewItem &option /NoCopy/, const QModelIndex &index) const;
    virtual QSize sizeHint(const QStyleOptionViewItem &option /NoCopy/, const QModelIndex &index) const;
    virtual QWidget *createEditor(QWidget *parent /TransferThis/, const QStyleOptionViewItem &option /NoCopy/, const QModelIndex &index) const /Factory/;
    virtual void setEditorData(QWidget *editor, const QModelIndex &index) const;
    virtual void setModelData(QWidget *editor, QAbstractItemModel *model /KeepReference/, const QModelIndex &index) const;
    virtual void updateEditorGeometry(QWidget *editor, const QStyleOptionViewItem &option /NoCopy/, const QModelIndex &index) const;
    QItemEditorFactory *itemEditorFactory() const;
    void setItemEditorFactory(QItemEditorFactory *factory /KeepReference/);
    bool hasClipping() const;
    void setClipping(bool clip);

protected:
    void drawBackground(QPainter *painter, const QStyleOptionViewItem &option, const QModelIndex &index) const;
    virtual void drawCheck(QPainter *painter, const QStyleOptionViewItem &option /NoCopy/, const QRect &rect, Qt::CheckState state) const;
    virtual void drawDecoration(QPainter *painter, const QStyleOptionViewItem &option /NoCopy/, const QRect &rect, const QPixmap &pixmap) const;
    virtual void drawDisplay(QPainter *painter, const QStyleOptionViewItem &option /NoCopy/, const QRect &rect, const QString &text) const;
    virtual void drawFocus(QPainter *painter, const QStyleOptionViewItem &option /NoCopy/, const QRect &rect) const;
    virtual bool eventFilter(QObject *object, QEvent *event);
    virtual bool editorEvent(QEvent *event, QAbstractItemModel *model, const QStyleOptionViewItem &option /NoCopy/, const QModelIndex &index);
};
