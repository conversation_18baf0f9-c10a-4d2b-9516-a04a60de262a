// qwindowcapture.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_6_0 -)

class QWindowCapture : public QObject
{
%TypeHeaderCode
#include <qwindowcapture.h>
%End

public:
    enum Error
    {
        NoError,
        InternalError,
        CapturingNotSupported,
        CaptureFailed,
        NotFound,
    };

    explicit QWindowCapture(QObject *parent /TransferThis/ = 0);
    virtual ~QWindowCapture();
    static QList<QCapturableWindow> capturableWindows();
    void setWindow(QCapturableWindow window);
    QCapturableWindow window() const;
    bool isActive() const;
    QWindowCapture::Error error() const;
    QString errorString() const;

public slots:
    void setActive(bool active);
    void start();
    void stop();

signals:
    void activeChanged(bool);
    void windowChanged(QCapturableWindow window);
    void errorChanged();
    void errorOccurred(QWindowCapture::Error error, const QString &errorString);
};

%End
