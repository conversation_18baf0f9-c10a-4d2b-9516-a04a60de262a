// qabstractitemmodel.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QModelIndex
{
%TypeHeaderCode
#include <qabstractitemmodel.h>
%End

public:
    QModelIndex();
    int row() const;
    int column() const;
    QVariant data(int role = Qt::DisplayRole) const;
    Qt::ItemFlags flags() const;
    SIP_PYOBJECT internalPointer() const;
%MethodCode
        sipRes = reinterpret_cast<PyObject *>(sipCpp->internalPointer());
        
        if (!sipRes)
            sipRes = Py_None;
        
        Py_INCREF(sipRes);
%End

    SIP_PYOBJECT internalId() const /TypeHint="int"/;
%MethodCode
        // Python needs to treat the result as an unsigned value (which may not happen
        // on 64 bit systems).  Instead we get the real value as it is stored (as a
        // void *) and let Python convert that.
        sipRes = PyLong_FromVoidPtr(sipCpp->internalPointer());
%End

    const QAbstractItemModel *model() const;
    bool isValid() const;
    QModelIndex parent() const;
    QModelIndex sibling(int arow, int acolumn) const;
    QModelIndex siblingAtColumn(int column) const;
    QModelIndex siblingAtRow(int row) const;
%If (- Qt_6_8_0)
    bool operator==(const QModelIndex &other) const;
%End
%If (- Qt_6_8_0)
    bool operator<(const QModelIndex &other) const;
%End
%If (- Qt_6_8_0)
    bool operator!=(const QModelIndex &other) const;
%End
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End
};

class QPersistentModelIndex
{
%TypeHeaderCode
#include <qabstractitemmodel.h>
%End

public:
    QPersistentModelIndex();
    QPersistentModelIndex(const QModelIndex &index);
    QPersistentModelIndex(const QPersistentModelIndex &other);
    ~QPersistentModelIndex();
    int row() const;
    int column() const;
    QVariant data(int role = Qt::DisplayRole) const;
    Qt::ItemFlags flags() const;
    QModelIndex parent() const;
    QModelIndex sibling(int row, int column) const;
    const QAbstractItemModel *model() const;
    bool isValid() const;
    void swap(QPersistentModelIndex &other /Constrained/);
    operator QModelIndex() const;
%If (- Qt_6_8_0)
    bool operator<(const QPersistentModelIndex &other) const;
%End
%If (- Qt_6_8_0)
    bool operator==(const QPersistentModelIndex &other) const;
%End
%If (- Qt_6_8_0)
    bool operator==(const QModelIndex &other) const;
%End
%If (- Qt_6_8_0)
    bool operator!=(const QPersistentModelIndex &other) const;
%End
%If (- Qt_6_8_0)
    bool operator!=(const QModelIndex &other) const;
%End
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End
};

typedef QList<QModelIndex> QModelIndexList;

class QAbstractItemModel : public QObject
{
%TypeHeaderCode
#include <qabstractitemmodel.h>
%End

public:
    enum LayoutChangeHint
    {
        NoLayoutChangeHint,
        VerticalSortHint,
        HorizontalSortHint,
    };

    explicit QAbstractItemModel(QObject *parent /TransferThis/ = 0);
    virtual ~QAbstractItemModel();
    bool hasIndex(int row, int column, const QModelIndex &parent = QModelIndex()) const;
    virtual QModelIndex index(int row, int column, const QModelIndex &parent = QModelIndex()) const = 0;
    virtual QModelIndex parent(const QModelIndex &child) const = 0;
    QObject *parent() const;
    virtual QModelIndex sibling(int row, int column, const QModelIndex &idx) const;
    virtual int rowCount(const QModelIndex &parent = QModelIndex()) const = 0;
    virtual int columnCount(const QModelIndex &parent = QModelIndex()) const = 0;
    virtual bool hasChildren(const QModelIndex &parent = QModelIndex()) const;
    virtual QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const = 0;
    virtual bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole);
    virtual QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const;
    virtual bool setHeaderData(int section, Qt::Orientation orientation, const QVariant &value, int role = Qt::EditRole);
    virtual QMap<int, QVariant> itemData(const QModelIndex &index) const;
    virtual bool setItemData(const QModelIndex &index, const QMap<int, QVariant> &roles);
    virtual QStringList mimeTypes() const;
    virtual QMimeData *mimeData(const QModelIndexList &indexes) const /TransferBack/;
    virtual bool dropMimeData(const QMimeData *data, Qt::DropAction action, int row, int column, const QModelIndex &parent);
    virtual Qt::DropActions supportedDropActions() const;
    virtual bool insertRows(int row, int count, const QModelIndex &parent = QModelIndex());
    virtual bool insertColumns(int column, int count, const QModelIndex &parent = QModelIndex());
    virtual bool removeRows(int row, int count, const QModelIndex &parent = QModelIndex());
    virtual bool removeColumns(int column, int count, const QModelIndex &parent = QModelIndex());
    virtual void fetchMore(const QModelIndex &parent);
    virtual bool canFetchMore(const QModelIndex &parent) const;
    virtual Qt::ItemFlags flags(const QModelIndex &index) const;
    virtual void sort(int column, Qt::SortOrder order = Qt::AscendingOrder);
    virtual QModelIndex buddy(const QModelIndex &index) const;
    virtual QModelIndexList match(const QModelIndex &start, int role, const QVariant &value, int hits = 1, Qt::MatchFlags flags = Qt::MatchFlags(Qt::MatchStartsWith | Qt::MatchWrap)) const;
    virtual QSize span(const QModelIndex &index) const;

signals:
    void dataChanged(const QModelIndex &topLeft, const QModelIndex &bottomRight, const QList<int> &roles = QList<int>());
    void headerDataChanged(Qt::Orientation orientation, int first, int last);
    void layoutAboutToBeChanged(const QList<QPersistentModelIndex> &parents = QList<QPersistentModelIndex>(), QAbstractItemModel::LayoutChangeHint hint = QAbstractItemModel::NoLayoutChangeHint);
    void layoutChanged(const QList<QPersistentModelIndex> &parents = QList<QPersistentModelIndex>(), QAbstractItemModel::LayoutChangeHint hint = QAbstractItemModel::NoLayoutChangeHint);
    void rowsAboutToBeInserted(const QModelIndex &parent, int first, int last);
    void rowsInserted(const QModelIndex &parent, int first, int last);
    void rowsAboutToBeRemoved(const QModelIndex &parent, int first, int last);
    void rowsRemoved(const QModelIndex &parent, int first, int last);
    void columnsAboutToBeInserted(const QModelIndex &parent, int first, int last);
    void columnsInserted(const QModelIndex &parent, int first, int last);
    void columnsAboutToBeRemoved(const QModelIndex &parent, int first, int last);
    void columnsRemoved(const QModelIndex &parent, int first, int last);
    void modelAboutToBeReset();
    void modelReset();

public slots:
    virtual bool submit();
    virtual void revert();

protected:
    void encodeData(const QModelIndexList &indexes, QDataStream &stream) const;
    bool decodeData(int row, int column, const QModelIndex &parent, QDataStream &stream);
    void beginInsertRows(const QModelIndex &parent, int first, int last);
    void endInsertRows();
    void beginRemoveRows(const QModelIndex &parent, int first, int last);
    void endRemoveRows();
    void beginInsertColumns(const QModelIndex &parent, int first, int last);
    void endInsertColumns();
    void beginRemoveColumns(const QModelIndex &parent, int first, int last);
    void endRemoveColumns();
    QModelIndexList persistentIndexList() const;
    void changePersistentIndex(const QModelIndex &from, const QModelIndex &to);
    void changePersistentIndexList(const QModelIndexList &from, const QModelIndexList &to);

public:
    bool insertRow(int row, const QModelIndex &parent = QModelIndex());
    bool insertColumn(int column, const QModelIndex &parent = QModelIndex());
    bool removeRow(int row, const QModelIndex &parent = QModelIndex());
    bool removeColumn(int column, const QModelIndex &parent = QModelIndex());
    virtual Qt::DropActions supportedDragActions() const;
    virtual QHash<int, QByteArray> roleNames() const;

protected:
    QModelIndex createIndex(int row, int column, SIP_PYOBJECT object = 0) const [QModelIndex (int row, int column, const void *object = 0)];
%MethodCode
        // The Qt API is broken (and won't be fixed as it would break binary
        // compatibility) regarding the internal id of a model index on different
        // architectures (32 vs 64 bits).  We choose to work around the breakage as it
        // is fairly subtle and continues to catch people out.  Instead of letting Qt
        // convert betweed an integer id and a pointer id (the internal format used by
        // Qt) we let Python do it.
        
        void *ptr;
        
        if (a2)
        {
            // Try and convert it to a Python long and fallback to the object's
            // address if it fails.
            ptr = PyLong_AsVoidPtr(a2);
        
            if (PyErr_Occurred())
            {
                PyErr_Clear();
                ptr = a2;
            }
        }
        else
        {
            ptr = 0;
        }
        
        #if defined(SIP_PROTECTED_IS_PUBLIC)
        sipRes = new QModelIndex(sipCpp->createIndex(a0, a1, ptr));
        #else
        sipRes = new QModelIndex(sipCpp->sipProtect_createIndex(a0, a1, ptr));
        #endif
%End

signals:
    void rowsAboutToBeMoved(const QModelIndex &sourceParent, int sourceStart, int sourceEnd, const QModelIndex &destinationParent, int destinationRow);
    void rowsMoved(const QModelIndex &parent, int start, int end, const QModelIndex &destination, int row);
    void columnsAboutToBeMoved(const QModelIndex &sourceParent, int sourceStart, int sourceEnd, const QModelIndex &destinationParent, int destinationColumn);
    void columnsMoved(const QModelIndex &parent, int start, int end, const QModelIndex &destination, int column);

protected:
    bool beginMoveRows(const QModelIndex &sourceParent, int sourceFirst, int sourceLast, const QModelIndex &destinationParent, int destinationRow);
    void endMoveRows();
    bool beginMoveColumns(const QModelIndex &sourceParent, int sourceFirst, int sourceLast, const QModelIndex &destinationParent, int destinationColumn);
    void endMoveColumns();
    void beginResetModel() /ReleaseGIL/;
    void endResetModel() /ReleaseGIL/;

protected slots:
    virtual void resetInternalData();

public:
    virtual bool canDropMimeData(const QMimeData *data, Qt::DropAction action, int row, int column, const QModelIndex &parent) const;
    virtual bool moveRows(const QModelIndex &sourceParent, int sourceRow, int count, const QModelIndex &destinationParent, int destinationChild);
    virtual bool moveColumns(const QModelIndex &sourceParent, int sourceColumn, int count, const QModelIndex &destinationParent, int destinationChild);
    bool moveRow(const QModelIndex &sourceParent, int sourceRow, const QModelIndex &destinationParent, int destinationChild);
    bool moveColumn(const QModelIndex &sourceParent, int sourceColumn, const QModelIndex &destinationParent, int destinationChild);

    enum class CheckIndexOption /BaseType=Flag/
    {
        NoOption,
        IndexIsValid,
        DoNotUseParent,
        ParentIsInvalid,
    };

    typedef QFlags<QAbstractItemModel::CheckIndexOption> CheckIndexOptions;
    bool checkIndex(const QModelIndex &index, QAbstractItemModel::CheckIndexOptions options = QAbstractItemModel::CheckIndexOption::NoOption) const;
    virtual bool clearItemData(const QModelIndex &index);
    virtual void multiData(const QModelIndex &index, QModelRoleDataSpan roleDataSpan) const;
};

class QAbstractTableModel : public QAbstractItemModel
{
%TypeHeaderCode
#include <qabstractitemmodel.h>
%End

public:
    explicit QAbstractTableModel(QObject *parent /TransferThis/ = 0);
    virtual ~QAbstractTableModel();
    virtual QModelIndex index(int row, int column, const QModelIndex &parent = QModelIndex()) const;
    virtual bool dropMimeData(const QMimeData *data, Qt::DropAction action, int row, int column, const QModelIndex &parent);
    virtual Qt::ItemFlags flags(const QModelIndex &index) const;
    QObject *parent() const;
    virtual QModelIndex sibling(int row, int column, const QModelIndex &idx) const;

private:
    virtual bool hasChildren(const QModelIndex &parent) const;
    virtual QModelIndex parent(const QModelIndex &child) const;
};

class QAbstractListModel : public QAbstractItemModel
{
%TypeHeaderCode
#include <qabstractitemmodel.h>
%End

public:
    explicit QAbstractListModel(QObject *parent /TransferThis/ = 0);
    virtual ~QAbstractListModel();
    virtual QModelIndex index(int row, int column = 0, const QModelIndex &parent = QModelIndex()) const;
    virtual bool dropMimeData(const QMimeData *data, Qt::DropAction action, int row, int column, const QModelIndex &parent);
    virtual Qt::ItemFlags flags(const QModelIndex &index) const;
    QObject *parent() const;
    virtual QModelIndex sibling(int row, int column, const QModelIndex &idx) const;

private:
    virtual int columnCount(const QModelIndex &parent) const;
    virtual bool hasChildren(const QModelIndex &parent) const;
    virtual QModelIndex parent(const QModelIndex &child) const;
};

class QModelRoleData
{
%TypeHeaderCode
#include <qabstractitemmodel.h>
%End

public:
    explicit QModelRoleData(int role);
    int role() const;
    const QVariant &data() const;
    void setData(const QVariant &data);
    void clearData();
};

class QModelRoleDataSpan
{
%TypeHeaderCode
// The capsule destructor for a QModelRoleData array.
static void qpycore_gc_modelroledata(PyObject *mrd_obj);
%End

%TypeCode
// The capsule destructor for a QModelRoleData array.
static void qpycore_gc_modelroledata(PyObject *mrd_obj)
{
    delete[] reinterpret_cast<QList<QModelRoleData>* >(PyCapsule_GetPointer(mrd_obj, NULL));
}
%End

public:
    QModelRoleDataSpan();
    QModelRoleDataSpan(QModelRoleData &modelRoleData);
    QModelRoleDataSpan(QList<QModelRoleData> modelRoleData);
%MethodCode
        PyObject *a0cap = PyCapsule_New(a0, NULL, qpycore_gc_modelroledata);
        
        if (a0cap)
        {
            sipSetUserObject((sipSimpleWrapper *)sipSelf, a0cap);
            
            sipCpp = new QModelRoleDataSpan(*a0);
        }
%End

    qsizetype size() const;
    qsizetype length() const /__len__/;
    QModelRoleData *data() const;
    QModelRoleData *begin() const;
    QModelRoleData *end() const;
    QModelRoleData &operator[](qsizetype index) const;
    QVariant *dataForRole(int role) const;
};

%If (Qt_6_8_0 -)
bool operator>=(const QPersistentModelIndex &lhs, const QModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator>=(const QModelIndex &lhs, const QPersistentModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator>=(const QPersistentModelIndex &lhs, const QPersistentModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator>=(const QModelIndex &lhs, const QModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator<=(const QPersistentModelIndex &lhs, const QModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator<=(const QModelIndex &lhs, const QPersistentModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator<=(const QPersistentModelIndex &lhs, const QPersistentModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator<=(const QModelIndex &lhs, const QModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator>(const QPersistentModelIndex &lhs, const QModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator>(const QModelIndex &lhs, const QPersistentModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator>(const QPersistentModelIndex &lhs, const QPersistentModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator>(const QModelIndex &lhs, const QModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator<(const QPersistentModelIndex &lhs, const QModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator<(const QModelIndex &lhs, const QPersistentModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator<(const QPersistentModelIndex &lhs, const QPersistentModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator<(const QModelIndex &lhs, const QModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator!=(const QPersistentModelIndex &lhs, const QModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator!=(const QModelIndex &lhs, const QPersistentModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator!=(const QPersistentModelIndex &lhs, const QPersistentModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator!=(const QModelIndex &lhs, const QModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator==(const QPersistentModelIndex &lhs, const QModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator==(const QModelIndex &lhs, const QPersistentModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator==(const QPersistentModelIndex &lhs, const QPersistentModelIndex &rhs);
%End
%If (Qt_6_8_0 -)
bool operator==(const QModelIndex &lhs, const QModelIndex &rhs);
%End
