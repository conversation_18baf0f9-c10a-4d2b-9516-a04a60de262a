// qdeadlinetimer.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDeadlineTimer
{
%TypeHeaderCode
#include <qdeadlinetimer.h>
%End

public:
%If (- Qt_6_6_0)

    enum ForeverConstant
    {
        Forever,
    };

%End
%If (Qt_6_6_0 -)

    enum class ForeverConstant
    {
        Forever,
    };

%End
    // This was replaced in Qt v6.6 with two overloads but we need the optional keyword argument.
    QDeadlineTimer(Qt::TimerType type = Qt::CoarseTimer);
    QDeadlineTimer(QDeadlineTimer::ForeverConstant, Qt::TimerType type = Qt::CoarseTimer);
    QDeadlineTimer(qint64 msecs, Qt::TimerType type = Qt::CoarseTimer);
    void swap(QDeadlineTimer &other /Constrained/);
    bool isForever() const;
    bool hasExpired() const;
    Qt::TimerType timerType() const;
    void setTimerType(Qt::TimerType type);
    qint64 remainingTime() const;
    qint64 remainingTimeNSecs() const;
    void setRemainingTime(qint64 msecs, Qt::TimerType type = Qt::CoarseTimer);
    void setPreciseRemainingTime(qint64 secs, qint64 nsecs = 0, Qt::TimerType type = Qt::CoarseTimer);
    qint64 deadline() const;
    qint64 deadlineNSecs() const;
    void setDeadline(qint64 msecs, Qt::TimerType type = Qt::CoarseTimer);
    void setPreciseDeadline(qint64 secs, qint64 nsecs = 0, Qt::TimerType type = Qt::CoarseTimer);
    static QDeadlineTimer addNSecs(QDeadlineTimer dt, qint64 nsecs);
    static QDeadlineTimer current(Qt::TimerType type = Qt::CoarseTimer);
    QDeadlineTimer &operator+=(qint64 msecs);
    QDeadlineTimer &operator-=(qint64 msecs);
};

%If (Qt_6_8_0 -)
bool operator==(const QDeadlineTimer &lhs, const QDeadlineTimer &rhs);
%End
%If (- Qt_6_8_0)
bool operator==(QDeadlineTimer d1, QDeadlineTimer d2);
%End
%If (Qt_6_8_0 -)
bool operator!=(const QDeadlineTimer &lhs, const QDeadlineTimer &rhs);
%End
%If (- Qt_6_8_0)
bool operator!=(QDeadlineTimer d1, QDeadlineTimer d2);
%End
%If (Qt_6_8_0 -)
bool operator<(const QDeadlineTimer &lhs, const QDeadlineTimer &rhs);
%End
%If (- Qt_6_8_0)
bool operator<(QDeadlineTimer d1, QDeadlineTimer d2);
%End
%If (Qt_6_8_0 -)
bool operator<=(const QDeadlineTimer &lhs, const QDeadlineTimer &rhs);
%End
%If (- Qt_6_8_0)
bool operator<=(QDeadlineTimer d1, QDeadlineTimer d2);
%End
%If (Qt_6_8_0 -)
bool operator>(const QDeadlineTimer &lhs, const QDeadlineTimer &rhs);
%End
%If (- Qt_6_8_0)
bool operator>(QDeadlineTimer d1, QDeadlineTimer d2);
%End
%If (Qt_6_8_0 -)
bool operator>=(const QDeadlineTimer &lhs, const QDeadlineTimer &rhs);
%End
%If (- Qt_6_8_0)
bool operator>=(QDeadlineTimer d1, QDeadlineTimer d2);
%End
QDeadlineTimer operator+(QDeadlineTimer dt, qint64 msecs);
QDeadlineTimer operator+(qint64 msecs, QDeadlineTimer dt);
QDeadlineTimer operator-(QDeadlineTimer dt, qint64 msecs);
qint64 operator-(QDeadlineTimer dt1, QDeadlineTimer dt2);
