// qaudioformat.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LIC<PERSON><PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QAudioFormat
{
%TypeHeaderCode
#include <qaudioformat.h>
%End

public:
    enum AudioChannelPosition
    {
        UnknownPosition,
        FrontLeft,
        FrontRight,
        FrontCenter,
        LFE,
        BackLeft,
        BackRight,
        FrontLeftOfCenter,
        FrontRightOfCenter,
        BackCenter,
        LFE2,
        SideLeft,
        SideRight,
        TopFrontLeft,
        TopFrontRight,
        TopFrontCenter,
        TopCenter,
        TopBackLeft,
        TopBackRight,
        TopSideLeft,
        TopSideRight,
        TopBackCenter,
        BottomFrontCenter,
        BottomFrontLeft,
        BottomFrontRight,
    };

    enum ChannelConfig
    {
        ChannelConfigUnknown,
        ChannelConfigMono,
        ChannelConfigStereo,
        ChannelConfig2Dot1,
        ChannelConfigSurround5Dot0,
        ChannelConfigSurround5Dot1,
        ChannelConfigSurround7Dot0,
        ChannelConfigSurround7Dot1,
%If (Qt_6_4_0 -)
        ChannelConfig3Dot0,
%End
%If (Qt_6_4_0 -)
        ChannelConfig3Dot1,
%End
    };

    enum SampleFormat
    {
        Unknown,
        UInt8,
        Int16,
        Int32,
        Float,
    };

    bool isValid() const;
    void setSampleRate(int sampleRate);
    int sampleRate() const;
    void setChannelCount(int channelCount);
    int channelCount() const;
    qint32 bytesForDuration(qint64 duration) const;
    qint64 durationForBytes(qint32 byteCount) const;
    qint32 bytesForFrames(qint32 frameCount) const;
    qint32 framesForBytes(qint32 byteCount) const;
    qint32 framesForDuration(qint64 duration) const;
    qint64 durationForFrames(qint32 frameCount) const;
    int bytesPerFrame() const;
    void setChannelConfig(QAudioFormat::ChannelConfig config);
    QAudioFormat::ChannelConfig channelConfig() const;
    int channelOffset(QAudioFormat::AudioChannelPosition channel) const;
    void setSampleFormat(QAudioFormat::SampleFormat f);
    QAudioFormat::SampleFormat sampleFormat() const;
    int bytesPerSample() const;
    float normalizedSampleValue(const void *sample) const;
%If (Qt_6_4_0 -)
    static QAudioFormat::ChannelConfig defaultChannelConfigForChannelCount(int channelCount);
%End
};

%End
%If (Qt_6_2_0 -)
bool operator==(const QAudioFormat &a, const QAudioFormat &b);
%End
%If (Qt_6_2_0 -)
bool operator!=(const QAudioFormat &a, const QAudioFormat &b);
%End
