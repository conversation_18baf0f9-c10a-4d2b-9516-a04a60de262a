// qpageranges.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPageRanges
{
%TypeHeaderCode
#include <qpageranges.h>
%End

public:
    QPageRanges();
    QPageRanges(const QPageRanges &other);
    ~QPageRanges();
    void swap(QPageRanges &other /Constrained/);

    struct Range
    {
%TypeHeaderCode
#include <qpageranges.h>
%End

        int from;
        int to;
        bool contains(int pageNumber) const;
    };

    void addPage(int pageNumber);
    void addRange(int from, int to);
    QList<QPageRanges::Range> toRangeList() const;
    void clear();
    QString toString() const;
    static QPageRanges fromString(const QString &ranges);
    bool contains(int pageNumber) const;
    bool isEmpty() const;
    int firstPage() const;
    int lastPage() const;
};

bool operator==(QPageRanges::Range lhs, QPageRanges::Range rhs);
bool operator==(const QPageRanges &lhs, const QPageRanges &rhs);
bool operator!=(QPageRanges::Range lhs, QPageRanges::Range rhs);
bool operator!=(const QPageRanges &lhs, const QPageRanges &rhs);
bool operator<(QPageRanges::Range lhs, QPageRanges::Range rhs);
QDataStream &operator<<(QDataStream &, const QPageRanges &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QPageRanges & /Constrained/) /ReleaseGIL/;
