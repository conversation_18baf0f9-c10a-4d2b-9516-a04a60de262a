// qgraphicsscene.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QGraphicsScene : public QObject
{
%TypeHeaderCode
#include <qgraphicsscene.h>
%End

public:
    enum ItemIndexMethod
    {
        BspTreeIndex,
        NoIndex,
    };

    QGraphicsScene(QObject *parent /TransferThis/ = 0);
    QGraphicsScene(const QRectF &sceneRect, QObject *parent /TransferThis/ = 0);
    QGraphicsScene(qreal x, qreal y, qreal width, qreal height, QObject *parent /TransferThis/ = 0);
    virtual ~QGraphicsScene();
    QRectF sceneRect() const;
    qreal width() const;
    qreal height() const;
    void setSceneRect(const QRectF &rect);
    void setSceneRect(qreal x, qreal y, qreal w, qreal h);
    void render(QPainter *painter, const QRectF &target = QRectF(), const QRectF &source = QRectF(), Qt::AspectRatioMode mode = Qt::KeepAspectRatio);
    QGraphicsScene::ItemIndexMethod itemIndexMethod() const;
    void setItemIndexMethod(QGraphicsScene::ItemIndexMethod method);
    QRectF itemsBoundingRect() const;
    QList<QGraphicsItem *> items(Qt::SortOrder order = Qt::DescendingOrder) const;
    QList<QGraphicsItem *> items(const QPointF &pos, Qt::ItemSelectionMode mode = Qt::IntersectsItemShape, Qt::SortOrder order = Qt::DescendingOrder, const QTransform &deviceTransform = QTransform()) const;
    QList<QGraphicsItem *> items(const QRectF &rect, Qt::ItemSelectionMode mode = Qt::IntersectsItemShape, Qt::SortOrder order = Qt::DescendingOrder, const QTransform &deviceTransform = QTransform()) const;
    QList<QGraphicsItem *> items(const QPolygonF &polygon, Qt::ItemSelectionMode mode = Qt::IntersectsItemShape, Qt::SortOrder order = Qt::DescendingOrder, const QTransform &deviceTransform = QTransform()) const;
    QList<QGraphicsItem *> items(const QPainterPath &path, Qt::ItemSelectionMode mode = Qt::IntersectsItemShape, Qt::SortOrder order = Qt::DescendingOrder, const QTransform &deviceTransform = QTransform()) const;
    QList<QGraphicsItem *> items(qreal x, qreal y, qreal w, qreal h, Qt::ItemSelectionMode mode, Qt::SortOrder order, const QTransform &deviceTransform = QTransform()) const;
    QList<QGraphicsItem *> collidingItems(const QGraphicsItem *item, Qt::ItemSelectionMode mode = Qt::IntersectsItemShape) const;
    QList<QGraphicsItem *> selectedItems() const;
    void setSelectionArea(const QPainterPath &path, const QTransform &deviceTransform);
    void setSelectionArea(const QPainterPath &path, Qt::ItemSelectionOperation selectionOperation = Qt::ReplaceSelection, Qt::ItemSelectionMode mode = Qt::IntersectsItemShape, const QTransform &deviceTransform = QTransform());
    void clearSelection();
    QGraphicsItemGroup *createItemGroup(const QList<QGraphicsItem *> &items /Transfer/);
    void destroyItemGroup(QGraphicsItemGroup *group /Transfer/);
    void addItem(QGraphicsItem *item /Transfer/);
    QGraphicsEllipseItem *addEllipse(const QRectF &rect, const QPen &pen = QPen(), const QBrush &brush = QBrush());
    QGraphicsEllipseItem *addEllipse(qreal x, qreal y, qreal w, qreal h, const QPen &pen = QPen(), const QBrush &brush = QBrush());
    QGraphicsLineItem *addLine(const QLineF &line, const QPen &pen = QPen());
    QGraphicsLineItem *addLine(qreal x1, qreal y1, qreal x2, qreal y2, const QPen &pen = QPen());
    QGraphicsPathItem *addPath(const QPainterPath &path, const QPen &pen = QPen(), const QBrush &brush = QBrush());
    QGraphicsPixmapItem *addPixmap(const QPixmap &pixmap);
    QGraphicsPolygonItem *addPolygon(const QPolygonF &polygon, const QPen &pen = QPen(), const QBrush &brush = QBrush());
    QGraphicsRectItem *addRect(const QRectF &rect, const QPen &pen = QPen(), const QBrush &brush = QBrush());
    QGraphicsRectItem *addRect(qreal x, qreal y, qreal w, qreal h, const QPen &pen = QPen(), const QBrush &brush = QBrush());
    QGraphicsSimpleTextItem *addSimpleText(const QString &text, const QFont &font = QFont());
    QGraphicsTextItem *addText(const QString &text, const QFont &font = QFont());
    void removeItem(QGraphicsItem *item /TransferBack/);
    QGraphicsItem *focusItem() const;
    void setFocusItem(QGraphicsItem *item, Qt::FocusReason focusReason = Qt::OtherFocusReason);
    bool hasFocus() const;
    void setFocus(Qt::FocusReason focusReason = Qt::OtherFocusReason);
    void clearFocus();
    QGraphicsItem *mouseGrabberItem() const;
    QBrush backgroundBrush() const;
    void setBackgroundBrush(const QBrush &brush);
    QBrush foregroundBrush() const;
    void setForegroundBrush(const QBrush &brush);
    virtual QVariant inputMethodQuery(Qt::InputMethodQuery query) const;
    QList<QGraphicsView *> views() const;

public slots:
    void advance();
    void update(const QRectF &rect = QRectF());
    void invalidate(const QRectF &rect = QRectF(), QGraphicsScene::SceneLayers layers = QGraphicsScene::AllLayers);
    void clear();

signals:
    void changed(const QList<QRectF> &region);
    void sceneRectChanged(const QRectF &rect);
    void selectionChanged();

protected:
    virtual bool event(QEvent *event);
    virtual void contextMenuEvent(QGraphicsSceneContextMenuEvent *event);
    virtual void dragEnterEvent(QGraphicsSceneDragDropEvent *event);
    virtual void dragMoveEvent(QGraphicsSceneDragDropEvent *event);
    virtual void dragLeaveEvent(QGraphicsSceneDragDropEvent *event);
    virtual void dropEvent(QGraphicsSceneDragDropEvent *event);
    virtual void focusInEvent(QFocusEvent *event);
    virtual void focusOutEvent(QFocusEvent *event);
    virtual void helpEvent(QGraphicsSceneHelpEvent *event);
    virtual void keyPressEvent(QKeyEvent *event);
    virtual void keyReleaseEvent(QKeyEvent *event);
    virtual void mousePressEvent(QGraphicsSceneMouseEvent *event);
    virtual void mouseMoveEvent(QGraphicsSceneMouseEvent *event);
    virtual void mouseReleaseEvent(QGraphicsSceneMouseEvent *event);
    virtual void mouseDoubleClickEvent(QGraphicsSceneMouseEvent *event);
    virtual void wheelEvent(QGraphicsSceneWheelEvent *event);
    virtual void inputMethodEvent(QInputMethodEvent *event);
    virtual void drawBackground(QPainter *painter, const QRectF &rect);
    virtual void drawForeground(QPainter *painter, const QRectF &rect);

public:
    enum SceneLayer /BaseType=Flag/
    {
        ItemLayer,
        BackgroundLayer,
        ForegroundLayer,
        AllLayers,
    };

    typedef QFlags<QGraphicsScene::SceneLayer> SceneLayers;
    int bspTreeDepth() const;
    void setBspTreeDepth(int depth);
    QPainterPath selectionArea() const;
    void update(qreal x, qreal y, qreal w, qreal h);
    QGraphicsProxyWidget *addWidget(QWidget *widget /Transfer/, Qt::WindowFlags flags = Qt::WindowFlags());
    QStyle *style() const;
    void setStyle(QStyle *style /Transfer/);
    QFont font() const;
    void setFont(const QFont &font);
    QPalette palette() const;
    void setPalette(const QPalette &palette);
    QGraphicsWidget *activeWindow() const;
    void setActiveWindow(QGraphicsWidget *widget);

protected:
    virtual bool eventFilter(QObject *watched, QEvent *event);
    virtual bool focusNextPrevChild(bool next);

public:
    void setStickyFocus(bool enabled);
    bool stickyFocus() const;
    QGraphicsItem *itemAt(const QPointF &pos, const QTransform &deviceTransform) const;
    QGraphicsItem *itemAt(qreal x, qreal y, const QTransform &deviceTransform) const;
    bool isActive() const;
    QGraphicsItem *activePanel() const;
    void setActivePanel(QGraphicsItem *item);
    bool sendEvent(QGraphicsItem *item, QEvent *event);
    void invalidate(qreal x, qreal y, qreal w, qreal h, QGraphicsScene::SceneLayers layers = QGraphicsScene::AllLayers);
    qreal minimumRenderSize() const;
    void setMinimumRenderSize(qreal minSize);

signals:
    void focusItemChanged(QGraphicsItem *newFocus, QGraphicsItem *oldFocus, Qt::FocusReason reason);

public:
    bool focusOnTouch() const;
    void setFocusOnTouch(bool enabled);
};
