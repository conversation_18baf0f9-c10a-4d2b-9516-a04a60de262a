// qtransform.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qtransform.h>
%End

class QTransform
{
%TypeHeaderCode
#include <qtransform.h>
%End

%PickleCode
    sipRes = Py_BuildValue("ddddddddd", sipCpp->m11(), sipCpp->m12(), sipCpp->m13(), sipCpp->m21(), sipCpp->m22(), sipCpp->m23(), sipCpp->m31(), sipCpp->m32(), sipCpp->m33());
%End

public:
    enum TransformationType
    {
        TxNone,
        TxTranslate,
        TxScale,
        TxRotate,
        TxShear,
        TxProject,
    };

    QTransform();
    QTransform(qreal h11, qreal h12, qreal h13, qreal h21, qreal h22, qreal h23, qreal h31, qreal h32, qreal h33);
    QTransform(qreal h11, qreal h12, qreal h13, qreal h21, qreal h22, qreal h23);
    QTransform(const QTransform &other);
    QTransform::TransformationType type() const;
    void setMatrix(qreal m11, qreal m12, qreal m13, qreal m21, qreal m22, qreal m23, qreal m31, qreal m32, qreal m33);
    QTransform inverted(bool *invertible = 0) const;
    QTransform adjoint() const;
    QTransform transposed() const;
    QTransform &translate(qreal dx, qreal dy);
    QTransform &scale(qreal sx, qreal sy);
    QTransform &shear(qreal sh, qreal sv);
    QTransform &rotate(qreal angle, Qt::Axis axis = Qt::ZAxis);
%If (Qt_6_5_0 -)
    QTransform &rotate(qreal a, Qt::Axis axis, qreal distanceToPlane);
%End
    QTransform &rotateRadians(qreal angle, Qt::Axis axis = Qt::ZAxis);
%If (Qt_6_5_0 -)
    QTransform &rotateRadians(qreal a, Qt::Axis axis, qreal distanceToPlane);
%End
    static bool squareToQuad(const QPolygonF &square, QTransform &result);
    static bool quadToSquare(const QPolygonF &quad, QTransform &result);
    static bool quadToQuad(const QPolygonF &one, const QPolygonF &two, QTransform &result);
    bool operator==(const QTransform &) const;
    bool operator!=(const QTransform &) const;
    QTransform &operator*=(const QTransform &) /__imatmul__/;
    QTransform operator*(const QTransform &o) const /__matmul__/;
    void reset();
    void map(int x /Constrained/, int y /Constrained/, int *tx, int *ty) const;
    void map(qreal x, qreal y, qreal *tx, qreal *ty) const;
    QPoint map(const QPoint &p) const;
    QPointF map(const QPointF &p) const;
    QLine map(const QLine &l) const;
    QLineF map(const QLineF &l) const;
    QPolygonF map(const QPolygonF &a) const;
    QPolygon map(const QPolygon &a) const;
    QRegion map(const QRegion &r) const;
    QPainterPath map(const QPainterPath &p) const;
    QPolygon mapToPolygon(const QRect &r) const;
    QRect mapRect(const QRect &) const;
    QRectF mapRect(const QRectF &) const;
    bool isAffine() const;
    bool isIdentity() const;
    bool isInvertible() const;
    bool isScaling() const;
    bool isRotating() const;
    bool isTranslating() const;
    qreal determinant() const;
    qreal m11() const;
    qreal m12() const;
    qreal m13() const;
    qreal m21() const;
    qreal m22() const;
    qreal m23() const;
    qreal m31() const;
    qreal m32() const;
    qreal m33() const;
    qreal dx() const;
    qreal dy() const;
    static QTransform fromTranslate(qreal dx, qreal dy);
    static QTransform fromScale(qreal dx, qreal dy);
    QTransform &operator*=(qreal num);
    QTransform &operator/=(qreal div);
    QTransform &operator+=(qreal num);
    QTransform &operator-=(qreal num);
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End
};

QDataStream &operator<<(QDataStream &, const QTransform &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QTransform & /Constrained/) /ReleaseGIL/;
QPoint operator*(const QPoint &p, const QTransform &m);
QPointF operator*(const QPointF &p, const QTransform &m);
QLineF operator*(const QLineF &l, const QTransform &m);
QLine operator*(const QLine &l, const QTransform &m);
QPolygon operator*(const QPolygon &a, const QTransform &m);
QPolygonF operator*(const QPolygonF &a, const QTransform &m);
QRegion operator*(const QRegion &r, const QTransform &m);
QTransform operator*(const QTransform &a, qreal n);
QTransform operator/(const QTransform &a, qreal n);
QTransform operator+(const QTransform &a, qreal n);
QTransform operator-(const QTransform &a, qreal n);
bool qFuzzyCompare(const QTransform &t1, const QTransform &t2);
