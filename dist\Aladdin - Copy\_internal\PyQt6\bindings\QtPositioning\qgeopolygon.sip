// qgeopolygon.sip generated by MetaSIP
//
// This file is part of the QtPositioning Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QGeoPolygon : public QGeoShape
{
%TypeHeaderCode
#include <qgeopolygon.h>
%End

public:
    QGeoPolygon();
    QGeoPolygon(const QList<QGeoCoordinate> &path);
    QGeoPolygon(const QGeoPolygon &other);
    QGeoPolygon(const QGeoShape &other);
    ~QGeoPolygon();
    void translate(double degreesLatitude, double degreesLongitude);
    QGeoPolygon translated(double degreesLatitude, double degreesLongitude) const;
    double length(qsizetype indexFrom = 0, qsizetype indexTo = -1) const;
    qsizetype size() const;
    void addCoordinate(const QGeoCoordinate &coordinate);
    void insertCoordinate(qsizetype index, const QGeoCoordinate &coordinate);
    void replaceCoordinate(qsizetype index, const QGeoCoordinate &coordinate);
    QGeoCoordinate coordinateAt(qsizetype index) const;
    bool containsCoordinate(const QGeoCoordinate &coordinate) const;
    void removeCoordinate(const QGeoCoordinate &coordinate);
    void removeCoordinate(qsizetype index);
    QString toString() const;
    void addHole(const QList<QGeoCoordinate> &holePath);
    void addHole(const QVariant &holePath);
    const QVariantList hole(qsizetype index) const;
    const QList<QGeoCoordinate> holePath(qsizetype index) const;
    void removeHole(qsizetype index);
    qsizetype holesCount() const;
    void setPerimeter(const QList<QGeoCoordinate> &path);
    const QList<QGeoCoordinate> &perimeter() const;
};

%End
%If (Qt_6_5_0 -)
QDataStream &operator<<(QDataStream &stream, const QGeoPolygon &polygon) /ReleaseGIL/;
%End
%If (Qt_6_5_0 -)
QDataStream &operator>>(QDataStream &stream, QGeoPolygon &polygon /Constrained/) /ReleaseGIL/;
%End
