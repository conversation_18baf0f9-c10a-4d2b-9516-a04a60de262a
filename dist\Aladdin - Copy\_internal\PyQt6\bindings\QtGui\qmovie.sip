// qmovie.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMovie : public QObject
{
%TypeHeaderCode
#include <qmovie.h>
%End

public:
    enum MovieState
    {
        NotRunning,
        Paused,
        Running,
    };

    enum CacheMode
    {
        CacheNone,
        Cache<PERSON>ll,
    };

    explicit QMovie(QObject *parent /TransferThis/ = 0);
    QMovie(QIODevice *device, const QByteArray &format = QByteArray(), QObject *parent /TransferThis/ = 0);
    QMovie(const QString &fileName, const QByteArray &format = QByteArray(), QObject *parent /TransferThis/ = 0);
    virtual ~QMovie();
    static QList<QByteArray> supportedFormats();
    void setDevice(QIODevice *device);
    QIODevice *device() const;
    void setFileName(const QString &fileName);
    QString fileName() const;
    void setFormat(const QByteArray &format);
    QByteArray format() const;
    void setBackgroundColor(const QColor &color);
    QColor backgroundColor() const;
    QMovie::MovieState state() const;
    QRect frameRect() const;
    QImage currentImage() const;
    QPixmap currentPixmap() const;
    bool isValid() const;
    bool jumpToFrame(int frameNumber);
    int loopCount() const;
    int frameCount() const;
    int nextFrameDelay() const;
    int currentFrameNumber() const;
    void setSpeed(int percentSpeed);
    int speed() const;
    QSize scaledSize();
    void setScaledSize(const QSize &size);
    QMovie::CacheMode cacheMode() const;
    void setCacheMode(QMovie::CacheMode mode);

signals:
    void started();
    void resized(const QSize &size);
    void updated(const QRect &rect);
    void stateChanged(QMovie::MovieState state);
    void error(QImageReader::ImageReaderError error);
    void finished();
    void frameChanged(int frameNumber);

public slots:
    void start();
    bool jumpToNextFrame();
    void setPaused(bool paused);
    void stop();

public:
    QImageReader::ImageReaderError lastError() const;
    QString lastErrorString() const;
};
