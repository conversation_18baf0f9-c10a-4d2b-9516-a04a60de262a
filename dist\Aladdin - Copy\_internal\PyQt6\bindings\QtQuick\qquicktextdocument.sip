// qquicktextdocument.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQuickTextDocument : public QObject
{
%TypeHeaderCode
#include <qquicktextdocument.h>
%End

public:
%If (Qt_6_7_0 -)

    enum class Status
    {
        Null,
        Loading,
        Loaded,
        Saving,
        Saved,
        ReadError,
        WriteError,
        NonLocalFileError,
    };

%End
    QQuickTextDocument(QQuickItem *parent /TransferThis/);
    QTextDocument *textDocument() const;
%If (Qt_6_7_0 -)
    QUrl source() const;
%End
%If (Qt_6_7_0 -)
    void setSource(const QUrl &url);
%End
%If (Qt_6_7_0 -)
    bool isModified() const;
%End
%If (Qt_6_7_0 -)
    void setModified(bool modified);
%End
%If (Qt_6_7_0 -)
    void setTextDocument(QTextDocument *document);
%End
%If (Qt_6_7_0 -)
    void save() /ReleaseGIL/;
%End
%If (Qt_6_7_0 -)
    void saveAs(const QUrl &url) /ReleaseGIL/;
%End
%If (Qt_6_7_0 -)
    QQuickTextDocument::Status status() const;
%End
%If (Qt_6_7_0 -)
    QString errorString() const;
%End

signals:
%If (Qt_6_7_0 -)
    void textDocumentChanged();
%End
%If (Qt_6_7_0 -)
    void sourceChanged();
%End
%If (Qt_6_7_0 -)
    void modifiedChanged();
%End
%If (Qt_6_7_0 -)
    void statusChanged();
%End
%If (Qt_6_7_0 -)
    void errorStringChanged();
%End
};
