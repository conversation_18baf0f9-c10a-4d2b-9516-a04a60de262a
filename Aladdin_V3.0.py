import sys
import json
import os
import re
import requests
from requests.adapters import <PERSON><PERSON><PERSON>dapter
from urllib3.util.retry import Retry
import threading
import time
import uuid
from urllib.parse import urlparse, parse_qs, urlencode, quote_plus
import html
import random
import glob
import tempfile

# Try to import BeautifulSoup for HTML parsing
try:
    from bs4 import BeautifulSoup
except ImportError:
    BeautifulSoup = None

# Resource path helper for PyInstaller
def resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")

    return os.path.join(base_path, relative_path)


def compute_value(x):
    # ...code from the lambda...
    return x * 2  # example logic

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QLineEdit, QPushButton, QFrame, QStackedWidget, QComboBox, QTextEdit,
    QDialog, QProgressBar, QScrollArea, QGraphicsDropShadowEffect, QTableWidget,
    QTableWidgetItem, QHeaderView, QFormLayout, QMessageBox, QGroupBox,
    QCheckBox, QTabWidget, QRadioButton, QStatusBar
)
from PyQt6.QtGui import (
    QFont, QColor, QPalette, QPixmap, QPainter, QBrush, QLinearGradient,
    QPainterPath, QImage, QPen
)
from PyQt6.QtCore import (
    Qt, QTimer, pyqtSignal, QObject, QPropertyAnimation, QRect,
    QEasingCurve, QRectF
)

# --- Application Configuration ---
# Version information
VERSION = "3.0"
APP_NAME = "Aladdin"
FULL_APP_NAME = f"{APP_NAME} v{VERSION}"

# Performance settings - Optimized cache implementation
from collections import OrderedDict

# Thread-safe LRU cache for icons
class LRUCache:
    """Thread-safe LRU (Least Recently Used) cache implementation"""
    def __init__(self, max_size=100):
        self.max_size = max_size
        self.cache = OrderedDict()
        self.lock = threading.RLock()

    def get(self, key, default=None):
        """Get an item from the cache, moving it to the end (most recently used)"""
        with self.lock:
            if key not in self.cache:
                return default
            # Move to end (mark as recently used)
            value = self.cache.pop(key)
            self.cache[key] = value
            return value

    def put(self, key, value):
        """Add an item to the cache, evicting least recently used items if needed"""
        with self.lock:
            if key in self.cache:
                # Remove existing item
                self.cache.pop(key)
            elif len(self.cache) >= self.max_size:
                # Remove oldest item (first item in OrderedDict)
                self.cache.popitem(last=False)
            # Add new item at the end
            self.cache[key] = value

    def __contains__(self, key):
        """Check if key exists in cache"""
        with self.lock:
            return key in self.cache

    def __len__(self):
        """Get current cache size"""
        with self.lock:
            return len(self.cache)

    def clear(self):
        """Clear the cache"""
        with self.lock:
            self.cache.clear()

# Initialize caches with improved implementation
MAX_ICON_CACHE_SIZE = 200
ICON_CACHE = LRUCache(MAX_ICON_CACHE_SIZE)

MAX_SEARCH_CACHE_SIZE = 50
SEARCH_CACHE = LRUCache(MAX_SEARCH_CACHE_SIZE)

# Image loading cache with timestamps for expiration
MAX_IMAGE_CACHE_SIZE = 100
IMAGE_CACHE = LRUCache(MAX_IMAGE_CACHE_SIZE)
IMAGE_CACHE_TTL = 3600  # 1 hour in seconds

# Request cache
MAX_CACHE_SIZE = 100
REQUEST_CACHE = LRUCache(MAX_CACHE_SIZE)

# --- Color Palette (from Kivy app) ---
# Using direct hex strings for QColor
COLOR_GUNMETAL = "#212930"
COLOR_BLUE_VIOLET = "#903ED5"
COLOR_AIR_SUPERIORITY_BLUE = "#5FA3D3"
COLOR_VISTA_BLUE = "#8F9DF7"
COLOR_MOONSTONE = "#20ACBB"
COLOR_CERULEAN = "#2A7FA7"
COLOR_MIDNIGHT_GREEN = "#17475B"
COLOR_PICTON_BLUE = "#3CB0F1"
COLOR_GUNMETAL_2 = "#0A2B37"
COLOR_VIVID_SKY_BLUE = "#29D4F1"

COLOR_GRADIENT_DARK = "#141E26"
COLOR_GRADIENT_LIGHT = "#2A3F4D"
COLOR_ACCENT_GLOW = "#A64DF7"
COLOR_CARD_BG = "#1A2530" # Made opaque for better QWidget handling, alpha can be in QSS
COLOR_HOVER_BG = "#2D3F4F"
COLOR_TEXT_LIGHT = "#E0E0E0"
COLOR_TEXT_DARK = "#111111"
COLOR_PLACEHOLDER_TEXT = "#777777"

# Default API URLs
DEFAULT_RECEIVE_URL = "https://script.googleusercontent.com/macros/echo?user_content_key=AehSKLgLz3932SJQQvFKmVMIwMvNKh5GELkfT6zU2ga5pUQpRGxy8-D_LiDXFiShAqxzEdsR5-01Lu2uBExXa3cJhrIxUbFJTlbbkM-_5ht45qt7mI_U-yxJ3JT7a2nVJyAj0tISPTjBII5sZk098nmwbZpkWddUJKrAuGekqNWdJlJCEbjL4W4nMwT8gDxUpoHXWfhHfARD_8YZXmoa4uQuXXpJAdVa5qD2AXu6dZAkVUczgmxTP7XlmDpz5JPWJn9TsapXpOiWajU6LRWpm_nclKQPSkshpA&lib=M02OMw1gJ_-0JszNSNyNDZb9qhhlEOC_Q"
DEFAULT_UPLOAD_URL = "https://script.google.com/macros/s/AKfycbxCQ0Va9OgvkufHn53G_Ej_frDhETzX8q5FMIO2zOVoBu-HHFPDWDGAukbrBpfG_iS5/exec"

# --- GameDataCache Singleton (Inherits QObject) ---
class GameDataCache(QObject): # Inherit from QObject
    _instance = None
    _lock = threading.Lock()
    data_updated = pyqtSignal(dict) # PyQt signal

    @classmethod
    def get_instance(cls):
        # Singleton pattern remains the same
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    # Create the instance without passing arguments here
                    cls._instance = cls()
        return cls._instance

    def __init__(self, parent=None): # Add parent=None for QObject convention
        # Prevent re-initialization if instance already exists
        if hasattr(GameDataCache, '_instance') and GameDataCache._instance is not None and GameDataCache._instance != self:
             return

        # Initialize QObject *before* accessing self
        super().__init__(parent) # Call QObject's initializer

        # Initialize instance attributes only if this is the first time
        if not hasattr(self, '_initialized'):
            self.data = {}
            self.data_lock = threading.Lock()
            self._initialized = True # Mark as initialized
            print("GameDataCache: Singleton created and QObject initialized.")

    def update(self, new_data):
        """Update the data cache with new data"""
        if not isinstance(new_data, dict):
            print(f"GameDataCache: Update received non-dict data: {type(new_data)}")
            return

        # Only acquire lock and emit signal if there's actual data to update
        if not new_data:
            return

        with self.data_lock:
            self.data.update(new_data)
            # Create a shallow copy for the signal to avoid deep copying large data
            data_copy = dict(self.data)

        # Emit signal outside the lock to prevent potential deadlocks
        self.data_updated.emit(data_copy)
        print(f"GameDataCache: Updated with {len(new_data)} keys.")

    def clear(self):
        """Clear all data from the cache"""
        with self.data_lock:
            self.data.clear()
            data_copy = {}

        # Emit signal outside the lock
        self.data_updated.emit(data_copy)
        print("GameDataCache: Cleared.")

    def get_data(self):
        """Get a copy of all data"""
        with self.data_lock:
            # Use a shallow copy for better performance
            return dict(self.data)

    def get_app_details(self, app_name):
        """Get details for a specific app"""
        with self.data_lock:
            # Return a reference to avoid copying large data
            # This is safe as long as callers don't modify the returned data
            return self.data.get(app_name)

    def get_all_app_names(self):
        """Get a sorted list of all app names"""
        with self.data_lock:
            # Convert to list only once and then sort
            return sorted(self.data.keys())

    def get_event_tokens_for_app(self, app_name):
        """Get event tokens for a specific app"""
        with self.data_lock:
            app_data = self.data.get(app_name, {})
            # Return a reference to avoid copying
            return app_data.get("events", {})

# Get the singleton instance *after* the class definition
game_data_cache = GameDataCache.get_instance()

# --- HistoryDataCache Singleton (Inherits QObject) ---
class HistoryDataCache(QObject):
    _instance = None
    _lock = threading.Lock()
    data_updated = pyqtSignal(dict) # PyQt signal

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance

    def __init__(self, parent=None):
        # Prevent re-initialization if instance already exists
        if hasattr(HistoryDataCache, '_instance') and HistoryDataCache._instance is not None and HistoryDataCache._instance != self:
            return

        # Initialize QObject
        super().__init__(parent)

        # Initialize instance attributes only if this is the first time
        if not hasattr(self, '_initialized'):
            # Structure:
            # {
            #   "attributions": [
            #     {"game_name": "Game1", "package_id": "com.game1", "gps_adid": "uuid", "timestamp": "2023-01-01 12:00:00"}
            #   ],
            #   "events": [
            #     {"game_name": "Game1", "event_name": "purchase", "gps_adid": "uuid", "timestamp": "2023-01-01 12:00:00"}
            #   ]
            # }
            self.data = {"attributions": [], "events": []}
            self.data_lock = threading.Lock()
            self._initialized = True
            print("HistoryDataCache: Singleton created and QObject initialized.")

    # Use a helper method to emit signals to avoid code duplication
    def _emit_data_updated(self):
        """Helper method to emit data_updated signal with a shallow copy of data"""
        try:
            # Create a shallow copy that's sufficient for the signal
            # Use a timeout to prevent deadlocks
            lock_acquired = self.data_lock.acquire(timeout=1)
            if not lock_acquired:
                print("WARNING: Could not acquire lock for emitting data updated signal - skipping")
                return

            try:
                data_copy = {
                    "attributions": list(self.data["attributions"]),
                    "events": list(self.data["events"])
                }
            finally:
                self.data_lock.release()

            # Emit the signal outside the lock
            self.data_updated.emit(data_copy)
        except Exception as e:
            print(f"ERROR in _emit_data_updated: {e}")

    def add_attribution(self, game_name, package_id, gps_adid):
        """Add an attribution record to the history"""
        try:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Create a copy of the data to avoid long lock times
            new_attribution = {
                "game_name": game_name,
                "package_id": package_id,
                "gps_adid": gps_adid,
                "timestamp": timestamp
            }

            # Use a timeout to prevent deadlocks
            lock_acquired = self.data_lock.acquire(timeout=2)
            if not lock_acquired:
                print(f"WARNING: Could not acquire lock for adding attribution - skipping")
                return False

            try:
                # Add the attribution to the data
                self.data["attributions"].append(new_attribution)

                # Limit the number of attributions to prevent memory issues
                if len(self.data["attributions"]) > 1000:
                    # Keep only the most recent 1000 attributions
                    self.data["attributions"] = self.data["attributions"][-1000:]
            finally:
                self.data_lock.release()

            # Use a timer to emit the signal to avoid blocking the UI thread
            # Use threading.Timer instead of QTimer to avoid Qt threading issues
            timer = threading.Timer(0.001, self._emit_data_updated)
            timer.daemon = True
            timer.start()

            print(f"HistoryDataCache: Added attribution for {game_name} with GPS ADID {gps_adid}")
            return True
        except Exception as e:
            print(f"ERROR in add_attribution: {e}")
            return False

    def add_event(self, game_name, event_name, gps_adid):
        """Add an event completion record to the history"""
        try:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Create a copy of the data to avoid long lock times
            new_event = {
                "game_name": game_name,
                "event_name": event_name,
                "gps_adid": gps_adid,
                "timestamp": timestamp
            }

            # Use a timeout to prevent deadlocks
            lock_acquired = self.data_lock.acquire(timeout=2)
            if not lock_acquired:
                print(f"WARNING: Could not acquire lock for adding event - skipping")
                return False

            try:
                # Add the event to the data
                self.data["events"].append(new_event)

                # Limit the number of events to prevent memory issues
                if len(self.data["events"]) > 1000:
                    # Keep only the most recent 1000 events
                    self.data["events"] = self.data["events"][-1000:]
            finally:
                self.data_lock.release()

            # Use a timer to emit the signal to avoid blocking the UI thread
            # Use threading.Timer instead of QTimer to avoid Qt threading issues
            timer = threading.Timer(0.001, self._emit_data_updated)
            timer.daemon = True
            timer.start()

            print(f"HistoryDataCache: Added event {event_name} for {game_name} with GPS ADID {gps_adid}")
            return True
        except Exception as e:
            print(f"ERROR in add_event: {e}")
            return False

    def get_attributions(self):
        """Get all attribution records with timeout protection"""
        try:
            # Use a timeout to prevent deadlocks
            lock_acquired = self.data_lock.acquire(timeout=1)
            if not lock_acquired:
                print("WARNING: Could not acquire lock for getting attributions - returning empty list")
                return []

            try:
                # Use list() for a shallow copy instead of .copy() for better performance
                return list(self.data["attributions"])
            finally:
                self.data_lock.release()
        except Exception as e:
            print(f"ERROR in get_attributions: {e}")
            return []

    def get_events(self):
        """Get all event completion records with timeout protection"""
        try:
            # Use a timeout to prevent deadlocks
            lock_acquired = self.data_lock.acquire(timeout=1)
            if not lock_acquired:
                print("WARNING: Could not acquire lock for getting events - returning empty list")
                return []

            try:
                # Use list() for a shallow copy
                return list(self.data["events"])
            finally:
                self.data_lock.release()
        except Exception as e:
            print(f"ERROR in get_events: {e}")
            return []

    def clear(self):
        """Clear all history data with timeout protection"""
        try:
            # Use a timeout to prevent deadlocks
            lock_acquired = self.data_lock.acquire(timeout=2)
            if not lock_acquired:
                print("WARNING: Could not acquire lock for clearing history data - skipping")
                return False

            try:
                self.data = {"attributions": [], "events": []}
            finally:
                self.data_lock.release()

            # Emit signal outside the lock using a timer to avoid blocking the UI thread
            # Use threading.Timer instead of QTimer to avoid Qt threading issues
            timer = threading.Timer(0.001, self._emit_data_updated)
            timer.daemon = True
            timer.start()

            print("HistoryDataCache: Cleared all history data")
            return True
        except Exception as e:
            print(f"ERROR in clear: {e}")
            return False

    def save_to_file(self, file_path):
        """Save history data to a JSON file with timeout protection"""
        try:
            # Create a copy of the data with timeout protection
            lock_acquired = self.data_lock.acquire(timeout=2)
            if not lock_acquired:
                print(f"WARNING: Could not acquire lock for saving history data - skipping")
                return False

            try:
                # Create a deep copy to avoid any reference issues
                import copy
                data_to_save = copy.deepcopy(self.data)
            finally:
                self.data_lock.release()

            # Write to file outside the lock to minimize lock time
            with open(file_path, 'w') as f:
                json.dump(data_to_save, f, indent=4)

            print(f"HistoryDataCache: Saved to {file_path}")
            return True
        except Exception as e:
            print(f"HistoryDataCache: Error saving to file: {e}")
            return False

    def load_from_file(self, file_path):
        """Load history data from a JSON file with timeout protection"""
        try:
            if not os.path.exists(file_path):
                print(f"HistoryDataCache: File not found: {file_path}")
                return False

            # Load data outside the lock
            with open(file_path, 'r') as f:
                loaded_data = json.load(f)

            # Validate the structure
            if not isinstance(loaded_data, dict) or "attributions" not in loaded_data or "events" not in loaded_data:
                print("HistoryDataCache: Invalid data structure in file")
                return False

            # Limit the size of the loaded data to prevent memory issues
            if "attributions" in loaded_data and len(loaded_data["attributions"]) > 1000:
                loaded_data["attributions"] = loaded_data["attributions"][-1000:]
                print("HistoryDataCache: Trimmed attributions to 1000 most recent entries")

            if "events" in loaded_data and len(loaded_data["events"]) > 1000:
                loaded_data["events"] = loaded_data["events"][-1000:]
                print("HistoryDataCache: Trimmed events to 1000 most recent entries")

            # Update data with lock and timeout protection
            lock_acquired = self.data_lock.acquire(timeout=2)
            if not lock_acquired:
                print(f"WARNING: Could not acquire lock for loading history data - skipping")
                return False

            try:
                self.data = loaded_data
            finally:
                self.data_lock.release()

            # Emit signal outside the lock using a timer to avoid blocking the UI thread
            # Use threading.Timer instead of QTimer to avoid Qt threading issues
            timer = threading.Timer(0.001, self._emit_data_updated)
            timer.daemon = True
            timer.start()

            print(f"HistoryDataCache: Loaded from {file_path}")
            return True
        except Exception as e:
            print(f"HistoryDataCache: Error loading from file: {e}")
            return False

# Get the singleton instance for history data cache
history_data_cache = HistoryDataCache.get_instance()

# --- Create a global requests session with optimized connection pooling and retry logic ---
def create_requests_session(retries=3, backoff_factor=0.3, status_forcelist=(500, 502, 504), app_settings=None):
    """Create a requests session with optimized connection pooling and retry logic"""
    session = requests.Session()

    # Create a more aggressive retry strategy for better reliability
    retry = Retry(
        total=retries,
        read=retries,
        connect=retries,
        backoff_factor=backoff_factor,
        status_forcelist=status_forcelist,
        allowed_methods=frozenset(['GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'OPTIONS']),
        raise_on_status=False  # Don't raise exceptions on status - let the caller handle it
    )

    # Increase connection pool size for better performance with concurrent requests
    adapter = HTTPAdapter(
        max_retries=retry,
        pool_connections=20,  # Increased from 10
        pool_maxsize=20,      # Increased from 10
        pool_block=False      # Don't block when pool is exhausted, just create new connection
    )

    # Mount the adapter for both HTTP and HTTPS
    session.mount('http://', adapter)
    session.mount('https://', adapter)

    # Set shorter default timeouts to prevent hanging requests
    session.timeout = (3, 10)  # (connect timeout, read timeout)

    # Add a hook to abort requests that take too long
    def timeout_hook(response, **_):
        # If the request took more than 15 seconds, log a warning
        elapsed = response.elapsed.total_seconds()
        if elapsed > 15:
            print(f"WARNING: Request to {response.url} took {elapsed:.2f} seconds")
        return response

    session.hooks["response"] = [timeout_hook]

    # Apply proxy settings if available
    if app_settings and app_settings.get('enable_proxy', False):
        proxy_url = app_settings.get('proxy_url', '')
        proxy_port = app_settings.get('proxy_port', '')
        if proxy_url and proxy_port:
            proxy_string = f"{proxy_url}:{proxy_port}"
            session.proxies = {
                'http': proxy_string,
                'https': proxy_string
            }
            print(f"Global session configured with proxy: {proxy_string}")

    return session

# Global session for reuse - will be updated with proxy settings later if needed
requests_session = create_requests_session()

# Function to update the global session with new settings
def update_requests_session(app_settings=None):
    """Update the global requests session with new settings"""
    global requests_session
    requests_session = create_requests_session(app_settings=app_settings)
    print("Global requests session updated with new settings")

def cached_request(url, method='GET', headers=None, params=None, data=None, json=None, timeout=30, cache_ttl=300):
    """
    Make a cached request with optimized performance and thread safety.

    Only caches GET requests with successful responses. Uses a thread-safe
    LRU cache implementation for efficient caching and eviction.

    Args:
        url: The URL to request
        method: HTTP method (only GET requests are cached)
        headers: Request headers
        params: URL parameters
        data: Form data
        json: JSON data
        timeout: Request timeout
        cache_ttl: Cache time-to-live in seconds (default: 5 minutes)

    Returns:
        requests.Response object
    """
    # Only cache GET requests
    if method.upper() != 'GET':
        return requests_session.request(
            method, url, headers=headers, params=params,
            data=data, json=json, timeout=timeout
        )

    # Quick validation
    if not url:
        return None

    # Create an efficient cache key - only include essential components
    # Use a more efficient approach than string concatenation
    param_key = "" if not params else str(sorted(params.items()))
    header_key = "" if not headers else str(sorted([(k, v) for k, v in headers.items()
                                                  if k in ('User-Agent', 'Accept', 'Accept-Language')]))
    cache_key = hash(f"{url}:{param_key}:{header_key}")

    # Check if we have a valid cached response
    cached_item = REQUEST_CACHE.get(cache_key)
    if cached_item:
        # Check if the cache is still valid
        if time.time() - cached_item['timestamp'] < cache_ttl:
            return cached_item['response']

    try:
        # Make the actual request
        response = requests_session.request(
            method, url, headers=headers, params=params,
            data=data, json=json, timeout=timeout
        )

        # Only cache successful responses
        if 200 <= response.status_code < 300:
            # Store in cache
            REQUEST_CACHE.put(cache_key, {
                'response': response,
                'timestamp': time.time()
            })

        return response

    except requests.RequestException as e:
        # Log the error but don't raise - return None instead
        print(f"Request error for {url}: {str(e)}")
        return None

# --- Play Store Search Functions ---
def search_play_store(query, max_results=15):
    """
    Search the Google Play Store for apps matching the query.

    Args:
        query (str): The search query
        max_results (int): Maximum number of results to return

    Returns:
        list: List of dictionaries containing app details
    """
    # Check if we have this query in cache
    cache_key = f"{query}_{max_results}"
    cached_results = SEARCH_CACHE.get(cache_key)
    if cached_results:
        print(f"Using cached search results for '{query}'")
        return cached_results

    # Use HTML scraping
    if not BeautifulSoup:
        return {'error': 'BeautifulSoup library is not installed. Please install it with: pip install beautifulsoup4'}

    print(f"Using HTML scraping to search for '{query}'")
    results = []
    search_url = f"https://play.google.com/store/search?q={quote_plus(query)}&c=apps"

    try:
        # Use an Android user agent to get mobile results
        headers = {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 10; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.162 Mobile Safari/537.36',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }

        # Use cached request for better performance
        response = cached_request(search_url, headers=headers, timeout=15, cache_ttl=600)  # Cache for 10 minutes
        if not response or response.status_code >= 400:
            return {'error': f'Error searching Play Store: HTTP {response.status_code if response else "No response"}'}

        soup = BeautifulSoup(response.text, 'html.parser')

        # Find app containers - try multiple selectors for better compatibility
        app_elements = (
            soup.select('div[data-uitype="500"]') or
            soup.select('.ImZGtf') or
            soup.select('a[href*="/store/apps/details"]') or
            soup.select('div.b8cIId') or
            soup.select('div.ULeU3b') or
            soup.select('div.VfPpkd-aGsRMb')
        )

        count = 0
        processed_ids = set()  # To avoid duplicates

        for app in app_elements:
            if count >= max_results:
                break

            # Try to find the link to the app
            app_link = app.select_one('a[href*="/store/apps/details"]')
            if not app_link:
                # Try to find the link in parent elements
                parent = app.parent
                for _ in range(3):  # Check up to 3 levels up
                    if parent:
                        app_link = parent.select_one('a[href*="/store/apps/details"]')
                        if app_link:
                            break
                        parent = parent.parent

            if not app_link:
                continue

            href = app_link.get('href', '')
            if not href or 'id=' not in href:
                continue

            # Extract package name
            package_match = re.search(r'id=([^&]+)', href)
            if not package_match:
                continue

            package_name = package_match.group(1)

            # Skip if we've already processed this package
            if package_name in processed_ids:
                continue

            processed_ids.add(package_name)

            # Build the full Play Store URL
            play_store_url = f"https://play.google.com/store/apps/details?id={package_name}"

            # Try to extract app name
            app_name_elem = (
                app.select_one('.DdYX5') or
                app.select_one('.wXUyZd') or
                app.select_one('span.sT93pb') or
                app.select_one('.Epkrse') or
                app.select_one('.nnK0zc')
            )
            app_name = app_name_elem.get_text().strip() if app_name_elem else "Unknown App"

            # Try to extract developer name
            dev_elem = (
                app.select_one('.LbQbAe') or
                app.select_one('.wMUdtb') or
                app.select_one('span.wMUdtb') or
                app.select_one('.b8cIId') or
                app.select_one('.KoLSrc')
            )
            developer = dev_elem.get_text().strip() if dev_elem else "Unknown Developer"

            # Try to extract icon URL
            icon_elem = (
                app.select_one('img.T75of') or
                app.select_one('img.KgxE8b') or
                app.select_one('img.T75of.sHb2Xb') or
                app.select_one('img.kJ9uy')
            )
            icon_url = icon_elem.get('src', '') if icon_elem else ""

            # Try to extract rating
            rating_elem = (
                app.select_one('.pf5lIe div[role="img"]') or
                app.select_one('.pf5lIe') or
                app.select_one('.w2kbF')
            )
            rating = None
            if rating_elem:
                rating_text = rating_elem.get('aria-label', '')
                rating_match = re.search(r'([\d.]+)', rating_text)
                if rating_match:
                    try:
                        rating = float(rating_match.group(1))
                    except ValueError:
                        pass

            # Try to extract downloads
            downloads_elem = (
                app.select_one('.wVqUob') or
                app.select_one('.ClM7O')
            )
            downloads = downloads_elem.get_text().strip() if downloads_elem else "Unknown"

            # Combine the data
            app_data = {
                'package_name': package_name,
                'app_name': app_name,
                'developer': developer,
                'play_store_url': play_store_url,
                'icon_url': icon_url,
                'rating': rating,
                'downloads': downloads,
                'description': '',  # Will be filled in later if needed
                'category': 'Unknown'
            }

            # Add to results
            results.append(app_data)
            count += 1

        # Cache the results
        if results:
            SEARCH_CACHE.put(cache_key, results)

    except Exception as e:
        print(f"Error searching Play Store: {str(e)}")
        return {'error': f"Error searching Play Store: {str(e)}"}

    return results

# SerpAPI function has been removed to optimize performance

def get_app_details_from_play_store(package_name):
    """
    Get detailed information about an app from its Play Store page.

    Args:
        package_name (str): The package name (com.example.app)

    Returns:
        dict: Dictionary containing app details
    """
    if not BeautifulSoup:
        return {'error': 'BeautifulSoup library is not installed'}

    play_store_url = f"https://play.google.com/store/apps/details?id={package_name}"

    try:
        # Use an Android user agent with cache control for faster response
        headers = {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 10; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.162 Mobile Safari/537.36',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }

        # Use cached request for better performance
        response = cached_request(play_store_url, headers=headers, timeout=15, cache_ttl=1800)  # Cache for 30 minutes
        if response.status_code >= 400:
            return {'error': f'Error fetching app details: HTTP {response.status_code}'}

        soup = BeautifulSoup(response.text, 'html.parser')

        # Extract app name - try multiple selectors for better compatibility
        app_name = "Unknown App"
        app_name_elem = (
            soup.select_one('h1[itemprop="name"]') or
            soup.select_one('.AHFaub') or
            soup.select_one('h1.AHFaub') or
            soup.select_one('.Fd93Bb') or
            soup.select_one('.rlnrK')
        )
        if app_name_elem:
            app_name = app_name_elem.get_text().strip()

        # Extract developer name
        developer = "Unknown Developer"
        dev_elem = (
            soup.select_one('a[href*="/store/apps/dev"]') or
            soup.select_one('a.hrTbp') or
            soup.select_one('.Vbfug') or
            soup.select_one('.T32cc')
        )
        if dev_elem:
            developer = dev_elem.get_text().strip()

        # Extract icon URL - high resolution if possible
        icon_url = ""
        icon_elem = (
            soup.select_one('img.T75of') or
            soup.select_one('img[itemprop="image"]') or
            soup.select_one('.xSyT2c') or
            soup.select_one('.Mqn5Lb')
        )
        if icon_elem:
            icon_url = icon_elem.get('src', '')
            # Try to get higher resolution by modifying URL
            if icon_url and '=s' in icon_url:
                icon_url = re.sub(r'=s\d+', '=s512', icon_url)

        # Extract description
        description = ""
        desc_elem = (
            soup.select_one('div[itemprop="description"]') or
            soup.select_one('.DWPxHb') or
            soup.select_one('.bARER') or
            soup.select_one('[jsname="sngebd"]')
        )
        if desc_elem:
            description = desc_elem.get_text().strip()
            # Truncate if too long but keep a substantial amount
            if len(description) > 1000:
                description = description[:997] + "..."

        # Extract category
        category = "Unknown"
        category_elem = (
            soup.select_one('a[itemprop="genre"]') or
            soup.select_one('.T4LgNb') or
            soup.select_one('[itemprop="applicationCategory"]') or
            soup.select_one('.qQKdcc')
        )
        if category_elem:
            category = category_elem.get_text().strip()

        # Extract rating
        rating = None
        rating_elem = (
            soup.select_one('.BHMmbe') or
            soup.select_one('.jILTFe') or
            soup.select_one('[itemprop="ratingValue"]') or
            soup.select_one('.TT9eCd')
        )
        if rating_elem:
            try:
                rating_text = rating_elem.get_text().strip()
                rating = float(rating_text)
            except (ValueError, AttributeError):
                pass

        # Extract downloads count
        downloads = "Unknown"
        downloads_elem = (
            soup.select_one('.IQ1z0d .ClM7O') or
            soup.select_one('.ClM7O') or
            soup.select_one('[itemprop="numDownloads"]') or
            soup.select_one('.wVqUob')
        )
        if downloads_elem:
            downloads = downloads_elem.get_text().strip()

        # Extract screenshots
        screenshots = []
        screenshot_elems = (
            soup.select('.SgoUSc img') or
            soup.select('.T75of.sHb2Xb') or
            soup.select('.Q4vdJd img')
        )
        for img in screenshot_elems[:5]:  # Limit to 5 screenshots
            src = img.get('src', '') or img.get('data-src', '')
            if src and 'play-lh.googleusercontent.com' in src:
                # Try to get higher resolution
                if '=w' in src:
                    src = re.sub(r'=w\d+-h\d+', '=w1080-h1920', src)
                screenshots.append(src)

        # Extract last updated date
        last_updated = "Unknown"
        updated_elem = (
            soup.select_one('.IQ1z0d .htlgb') or
            soup.select_one('[itemprop="datePublished"]') or
            soup.select_one('.xg1aie')
        )
        if updated_elem:
            last_updated = updated_elem.get_text().strip()

        # Extract version
        version = "Unknown"
        version_elem = (
            soup.select_one('[itemprop="softwareVersion"]') or
            soup.select_one('.IQ1z0d .htlgb:nth-child(4)') or
            soup.select_one('.reAt0')
        )
        if version_elem:
            version_text = version_elem.get_text().strip()
            if re.search(r'\d+\.\d+', version_text):  # Make sure it looks like a version number
                version = version_text

        return {
            'package_name': package_name,
            'app_name': app_name,
            'developer': developer,
            'play_store_url': play_store_url,
            'icon_url': icon_url,
            'description': description,
            'category': category,
            'rating': rating,
            'downloads': downloads,
            'screenshots': screenshots,
            'last_updated': last_updated,
            'version': version
        }

    except Exception as e:
        print(f"Error fetching app details: {str(e)}")
        return {'error': f"Error fetching app details: {str(e)}"}

# --- Thread Pool for Worker Management ---
class ThreadPool:
    """
    Thread pool for managing worker threads.

    Limits the number of concurrent threads to prevent resource exhaustion.
    Includes safeguards against deadlocks and thread leaks.
    """
    _instance = None
    _lock = threading.RLock()

    @classmethod
    def get_instance(cls):
        """Get the singleton instance of the thread pool."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance

    def __init__(self):
        """Initialize the thread pool."""
        self.active_threads = set()
        self.max_threads = 5  # Reduced from 10 to prevent excessive threading
        self.queue = []  # Queue for pending tasks
        self.lock = threading.RLock()
        self.thread_count = 0  # Total threads created for debugging
        self.watchdog_timer = None  # Initialize timer reference

        # Set up a watchdog timer to monitor thread pool health
        self._setup_watchdog()

    def _setup_watchdog(self):
        """Set up a watchdog timer to monitor thread pool health using threading.Timer"""
        # Use threading.Timer instead of QTimer to avoid Qt threading issues
        self.watchdog_timer = threading.Timer(30.0, self._check_thread_pool_health)
        self.watchdog_timer.daemon = True
        self.watchdog_timer.start()

    def _check_thread_pool_health(self):
        """Check the health of the thread pool and clean up any stale threads"""
        try:
            with self.lock:
                # Only log if there are issues to avoid spam
                active_count = len(self.active_threads)
                queue_count = len(self.queue)

                if active_count > 5 or queue_count > 10:
                    print(f"Thread pool health check: {active_count} active, {queue_count} queued")

                # Check for any threads that are no longer alive
                stale_threads = set()
                for thread in self.active_threads:
                    if not thread.is_alive():
                        stale_threads.add(thread)

                # Remove stale threads
                if stale_threads:
                    print(f"Removing {len(stale_threads)} stale threads from pool")
                    self.active_threads -= stale_threads

                    # Start new threads from the queue if possible
                    self._process_queue()

            # Schedule the next health check
            self.watchdog_timer = threading.Timer(30.0, self._check_thread_pool_health)
            self.watchdog_timer.daemon = True
            self.watchdog_timer.start()
        except Exception as e:
            print(f"Error in thread pool health check: {e}")

    def _process_queue(self):
        """Process the queue of pending workers"""
        # Must be called with lock held
        while self.queue and len(self.active_threads) < self.max_threads:
            next_worker = self.queue.pop(0)
            self._start_worker_thread(next_worker)

    def submit(self, worker):
        """
        Submit a worker to the thread pool.

        Args:
            worker: The Worker instance to run
        """
        with self.lock:
            # If we have room, start the thread immediately
            if len(self.active_threads) < self.max_threads:
                self._start_worker_thread(worker)
            else:
                # Otherwise, add to queue
                self.queue.append(worker)

                # Log if queue is getting large and limit queue size
                if len(self.queue) > 20:
                    print(f"WARNING: Thread pool queue size: {len(self.queue)} - dropping oldest tasks")
                    # Drop oldest tasks to prevent memory issues
                    self.queue = self.queue[-15:]  # Keep only the 15 most recent tasks
                elif len(self.queue) > 10:
                    print(f"WARNING: Thread pool queue size: {len(self.queue)}")

    def _start_worker_thread(self, worker):
        """
        Start a worker thread and track it.

        Args:
            worker: The Worker instance to run
        """
        thread = threading.Thread(target=self._run_worker, args=(worker,))
        thread.daemon = True
        thread.name = f"Worker-{self.thread_count}"
        self.thread_count += 1

        with self.lock:
            self.active_threads.add(thread)

        thread.start()

        # Log thread creation for debugging
        if self.thread_count % 10 == 0:  # Log every 10 threads
            print(f"Thread pool: created thread #{self.thread_count}, active: {len(self.active_threads)}")

    def _run_worker(self, worker):
        """
        Run a worker and manage thread cleanup.

        Args:
            worker: The Worker instance to run
        """
        start_time = time.time()
        thread_name = threading.current_thread().name

        try:
            worker.run()
        except Exception as e:
            print(f"Unhandled exception in worker thread {thread_name}: {e}")
        finally:
            # Calculate execution time
            execution_time = time.time() - start_time
            if execution_time > 5:  # Only log if it took more than 5 seconds
                print(f"Thread {thread_name} execution time: {execution_time:.2f} seconds")

            # Clean up and start next queued task if available
            with self.lock:
                # Remove this thread from active threads
                current_thread = threading.current_thread()
                self.active_threads.discard(current_thread)

                # Process the queue
                self._process_queue()

# Get the singleton thread pool instance
thread_pool = ThreadPool.get_instance()

# --- Worker for Threading (Optimized) ---
class Worker(QObject):
    """
    Optimized worker class for running tasks in separate threads.

    Provides signals for tracking progress, handling errors, and returning results.
    Includes safeguards against memory leaks, thread safety issues, and timeouts.
    """
    finished = pyqtSignal()
    error = pyqtSignal(str)
    progress = pyqtSignal(str)
    result = pyqtSignal(object)

    def __init__(self, target, *args, **kwargs):
        """
        Initialize the worker with a target function and its arguments.

        Args:
            target: The function to execute in the thread
            *args: Positional arguments for the target function
            **kwargs: Keyword arguments for the target function
        """
        super().__init__()
        self._target = target
        self._args = args
        self._kwargs = kwargs
        self._is_running = False
        self._is_cancelled = False
        self._lock = threading.RLock()  # Reentrant lock for thread safety
        self._weak_refs = False  # Flag to indicate if we're using weak references
        self._timeout = kwargs.pop('timeout', 30)  # Default timeout of 30 seconds
        self._start_time = None  # Will be set when the worker starts running
        self._watchdog_timer = None  # Timer for watchdog functionality

    def use_weak_references(self):
        """
        Convert arguments to weak references where possible to prevent memory leaks.
        Call this before running the worker if you're concerned about circular references.
        """
        import weakref

        with self._lock:
            if self._weak_refs:
                return  # Already using weak references

            # Convert args to weak references where possible
            new_args = []
            for arg in self._args:
                if isinstance(arg, QObject) and not isinstance(arg, (str, int, float, bool)):
                    try:
                        new_args.append(weakref.proxy(arg))
                    except TypeError:
                        new_args.append(arg)
                else:
                    new_args.append(arg)
            self._args = tuple(new_args)

            # Convert kwargs to weak references where possible
            new_kwargs = {}
            for key, value in self._kwargs.items():
                if isinstance(value, QObject) and not isinstance(value, (str, int, float, bool)):
                    try:
                        new_kwargs[key] = weakref.proxy(value)
                    except TypeError:
                        new_kwargs[key] = value
                else:
                    new_kwargs[key] = value
            self._kwargs = new_kwargs

            self._weak_refs = True

    def _setup_watchdog(self):
        """Set up a watchdog timer to prevent hanging threads using threading.Timer"""
        # Use threading.Timer instead of QTimer to avoid Qt threading issues
        self._watchdog_timer = threading.Timer(self._timeout, self._handle_timeout)
        self._watchdog_timer.daemon = True
        self._watchdog_timer.start()

    def _handle_timeout(self):
        """Handle a watchdog timer timeout"""
        print(f"Worker TIMEOUT: Operation took longer than {self._timeout} seconds")

        # Mark as cancelled
        with self._lock:
            if not self._is_running:
                return

            self._is_cancelled = True

        # Emit error and finished signals
        if self.thread() is not None:
            self.error.emit(f"Operation timed out after {self._timeout} seconds")
            self.finished.emit()

    def _cancel_watchdog(self):
        """Cancel the watchdog timer if it exists"""
        if self._watchdog_timer is not None:
            self._watchdog_timer.cancel()
            self._watchdog_timer = None

    def run(self):
        """Execute the target function in a thread-safe manner with timeout protection."""
        with self._lock:
            if self._is_running or self._is_cancelled:
                return
            self._is_running = True
            self._start_time = time.time()

        # Set up watchdog timer to prevent hanging threads
        self._setup_watchdog()

        try:
            # Execute the target function
            res = self._target(*self._args, **self._kwargs)

            # Cancel the watchdog timer since we completed successfully
            self._cancel_watchdog()

            # Check if cancelled before emitting result
            with self._lock:
                if self._is_cancelled:
                    return

            # Only emit result if not None and worker is still valid
            if res is not None and self.thread() is not None:
                self.result.emit(res)

        except Exception as e:
            # Cancel the watchdog timer since we're handling the error
            self._cancel_watchdog()

            # Check if cancelled before handling error
            with self._lock:
                if self._is_cancelled:
                    return

            # Optimized error handling - only get full traceback for complex errors
            if isinstance(e, (ValueError, TypeError, AttributeError, KeyError, IndexError)):
                err_msg = f"{type(e).__name__}: {str(e)}"
            else:
                import traceback
                err_msg = f"{type(e).__name__}: {str(e)}\n{traceback.format_exc()}"

            # Only emit if worker is still valid
            if self.thread() is not None:
                self.error.emit(err_msg)
            print(f"Worker error: {err_msg}")

        finally:
            # Cancel the watchdog timer if it's still active
            self._cancel_watchdog()

            # Calculate execution time for logging
            execution_time = time.time() - self._start_time
            if execution_time > 5:  # Only log if it took more than 5 seconds
                print(f"Worker execution time: {execution_time:.2f} seconds")

            with self._lock:
                self._is_running = False

                # Only emit finished if not cancelled and thread is still valid
                if not self._is_cancelled and self.thread() is not None:
                    self.finished.emit()

            # Clear references to help garbage collection
            self._target = None
            self._args = None
            self._kwargs = None

    def cancel(self):
        """Cancel the worker if it hasn't completed yet."""
        with self._lock:
            self._is_cancelled = True

        # Cancel the watchdog timer if it exists
        self._cancel_watchdog()

        # Log the cancellation
        print("Worker cancelled")

    def submit_to_pool(self):
        """Submit this worker to the thread pool for execution."""
        thread_pool.submit(self)

# --- UI Helper Functions ---
def apply_shadow(widget, blur_radius=10, x_offset=2, y_offset=2, color=QColor(0,0,0,80)):
    shadow = QGraphicsDropShadowEffect()
    shadow.setBlurRadius(blur_radius)
    shadow.setXOffset(x_offset)
    shadow.setYOffset(y_offset)
    shadow.setColor(color)
    widget.setGraphicsEffect(shadow)

# Helper function for creating placeholder pixmaps
def get_placeholder_pixmap(size, color, letter):
    """
    Create a placeholder pixmap with a letter.

    Args:
        size (int): Size of the pixmap
        color (str): Color hex code for the background
        letter (str): Letter to display

    Returns:
        QPixmap: The placeholder pixmap
    """
    placeholder = QPixmap(size, size)
    placeholder.fill(QColor(color))

    painter = QPainter(placeholder)
    painter.setPen(Qt.GlobalColor.white)
    painter.setFont(QFont("Arial", int(size/3), QFont.Weight.Bold))
    painter.drawText(placeholder.rect(), Qt.AlignmentFlag.AlignCenter, letter)
    painter.end()

    return placeholder

def load_image_from_url(url, timeout=5, cache_ttl=3600):
    """
    Load an image from a URL with highly optimized caching and error handling.

    Args:
        url (str): The URL to load the image from
        timeout (int): Request timeout in seconds (default reduced to 5 seconds)
        cache_ttl (int): Cache time-to-live in seconds

    Returns:
        QPixmap: The loaded image as a QPixmap, or None if loading failed
    """
    # Fast path: Quick validation
    if not url or not isinstance(url, str) or not url.startswith('http'):
        return None

    # Fast path: Check if we have this image in cache
    cached_item = IMAGE_CACHE.get(url)
    if cached_item:
        # Check if the cache is still valid
        if time.time() - cached_item['timestamp'] < cache_ttl:
            return cached_item['pixmap']

    try:
        # Use a shorter timeout for better responsiveness
        response = None

        # Try to use cached_request first for better performance
        try:
            response = cached_request(url, timeout=timeout)
        except Exception as req_err:
            print(f"Cached request failed for {url}: {str(req_err)}")
            # Fall back to direct request with shorter timeout
            try:
                response = requests.get(url, timeout=max(2, timeout/2))
            except Exception as direct_err:
                print(f"Direct request also failed: {str(direct_err)}")
                return None

        # Check response validity
        if not response or response.status_code != 200 or not response.content:
            return None

        # Fast path: For small images, use direct loading
        content_length = len(response.content)
        if content_length < 50000:  # Less than 50KB
            # Create a QImage from the response data
            image = QImage()
            if image.loadFromData(response.content):
                # Convert to QPixmap
                pixmap = QPixmap.fromImage(image)
                if not pixmap.isNull():
                    # Store in cache using our thread-safe LRU implementation
                    IMAGE_CACHE.put(url, {
                        'pixmap': pixmap,
                        'timestamp': time.time()
                    })
                    return pixmap
        else:
            # For larger images, use a more memory-efficient approach
            # Create a temporary file to avoid loading the entire image into memory
            with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as temp_file:
                temp_file.write(response.content)
                temp_file_path = temp_file.name

            try:
                # Load from file which is more memory efficient for large images
                pixmap = QPixmap(temp_file_path)
                if not pixmap.isNull():
                    # Store in cache
                    IMAGE_CACHE.put(url, {
                        'pixmap': pixmap,
                        'timestamp': time.time()
                    })

                    # Clean up the temporary file
                    try:
                        os.unlink(temp_file_path)
                    except:
                        pass

                    return pixmap

                # Clean up the temporary file
                try:
                    os.unlink(temp_file_path)
                except:
                    pass
            except Exception as img_err:
                print(f"Error loading image from temp file: {str(img_err)}")
                # Clean up the temporary file
                try:
                    os.unlink(temp_file_path)
                except:
                    pass

                # Fall back to direct loading
                image = QImage()
                if image.loadFromData(response.content):
                    pixmap = QPixmap.fromImage(image)
                    if not pixmap.isNull():
                        IMAGE_CACHE.put(url, {
                            'pixmap': pixmap,
                            'timestamp': time.time()
                        })
                        return pixmap

        return None
    except Exception as e:
        # Minimal error logging to avoid console spam
        print(f"Image load error ({url[:30]}...): {str(e)[:100]}")
        return None

# --- Custom Widgets (Integrated) ---

# QR Code Scanner class has been removed

class PlayStoreSearchDialog(QDialog):
    """Dialog for searching the Play Store and selecting a game"""

    def __init__(self, parent=None, game_name=""):
        super().__init__(parent)
        self.setWindowTitle("Play Store Search")
        self.setMinimumWidth(dp(800))  # Wider dialog for more details
        self.setMinimumHeight(dp(600))  # Taller dialog for more results

        self.game_name = game_name
        self.search_results = []
        self.selected_result = None
        self.is_searching = False

        self._build_ui()

        # Start search automatically if game name is provided
        if game_name:
            self.search_input.setText(game_name)
            self.search_play_store()

    def _build_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(dp(20), dp(20), dp(20), dp(20))
        main_layout.setSpacing(dp(15))

        # Title
        title_label = QLabel(f"Search Play Store for: {self.game_name}")
        title_label.setStyleSheet(f"""
            font-size: {dp(20)}px;
            font-weight: bold;
            color: {COLOR_TEXT_LIGHT};
            margin-bottom: {dp(10)}px;
        """)
        main_layout.addWidget(title_label)

        # Search section
        search_layout = QHBoxLayout()
        search_layout.setSpacing(dp(10))

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Enter game name to search")
        self.search_input.setText(self.game_name)
        self.search_input.setStyleSheet(f"""
            font-size: {dp(16)}px;
            padding: {dp(8)}px;
            border-radius: {dp(5)}px;
        """)
        # Connect Enter key to search
        self.search_input.returnPressed.connect(self.search_play_store)
        search_layout.addWidget(self.search_input, 1)

        self.search_button = QPushButton("🔍 Search")
        self.search_button.clicked.connect(self.search_play_store)
        self.search_button.setStyleSheet(f"""
            background-color: {COLOR_BLUE_VIOLET};
            color: white;
            font-weight: bold;
            font-size: {dp(14)}px;
            padding: {dp(8)}px {dp(15)}px;
            border-radius: {dp(5)}px;
        """)
        search_layout.addWidget(self.search_button)

        main_layout.addLayout(search_layout)

        # Results section
        results_group = QGroupBox("Search Results")
        results_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: {dp(16)}px;
                font-weight: bold;
                border: 1px solid {COLOR_GRADIENT_LIGHT};
                border-radius: {dp(5)}px;
                margin-top: {dp(15)}px;
                padding-top: {dp(15)}px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: {dp(10)}px;
                padding: 0 {dp(5)}px;
            }}
        """)
        results_layout = QVBoxLayout(results_group)

        self.results_container = QWidget()
        self.results_layout = QVBoxLayout(self.results_container)
        self.results_layout.setContentsMargins(0, 0, 0, 0)
        self.results_layout.setSpacing(dp(15))  # More spacing between results

        # Scroll area for results
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(self.results_container)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: none;
                background-color: transparent;
            }}
            QScrollBar:vertical {{
                background: {COLOR_GUNMETAL};
                width: {dp(12)}px;
                border-radius: {dp(6)}px;
            }}
            QScrollBar::handle:vertical {{
                background: {COLOR_GRADIENT_LIGHT};
                min-height: {dp(30)}px;
                border-radius: {dp(5)}px;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                height: 0px;
            }}
        """)
        results_layout.addWidget(scroll_area)

        main_layout.addWidget(results_group, 1)

        # Status label
        self.status_label = QLabel("Enter a game name and click Search")
        self.status_label.setWordWrap(True)
        self.status_label.setStyleSheet(f"""
            font-size: {dp(14)}px;
            color: {COLOR_TEXT_LIGHT};
            margin: {dp(10)}px 0;
        """)
        main_layout.addWidget(self.status_label)

        # Buttons
        buttons_layout = QHBoxLayout()

        # Skip button (new)
        self.skip_button = QPushButton("Skip This Game")
        self.skip_button.clicked.connect(self.skip_game)
        self.skip_button.setStyleSheet(f"""
            background-color: {COLOR_GUNMETAL_2};
            color: {COLOR_TEXT_LIGHT};
            font-size: {dp(14)}px;
            padding: {dp(8)}px {dp(15)}px;
            border-radius: {dp(5)}px;
            border: 1px solid {COLOR_GRADIENT_LIGHT};
        """)
        buttons_layout.addWidget(self.skip_button)

        buttons_layout.addStretch()

        self.select_button = QPushButton("Select")
        self.select_button.clicked.connect(self.accept)
        self.select_button.setEnabled(False)
        self.select_button.setStyleSheet(f"""
            background-color: {COLOR_BLUE_VIOLET};
            color: white;
            font-weight: bold;
            font-size: {dp(14)}px;
            padding: {dp(8)}px {dp(15)}px;
            border-radius: {dp(5)}px;
        """)
        buttons_layout.addWidget(self.select_button)

        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(self.reject)
        cancel_button.setStyleSheet(f"""
            background-color: {COLOR_GUNMETAL_2};
            color: {COLOR_TEXT_LIGHT};
            font-size: {dp(14)}px;
            padding: {dp(8)}px {dp(15)}px;
            border-radius: {dp(5)}px;
            border: 1px solid {COLOR_GRADIENT_LIGHT};
        """)
        buttons_layout.addWidget(cancel_button)

        main_layout.addLayout(buttons_layout)

    def skip_game(self):
        """Skip this game and return None as the selected result"""
        self.selected_result = "SKIP"  # Special marker to indicate skipping
        self.accept()

    def search_play_store(self):
        """Search the Play Store for the entered game name"""
        query = self.search_input.text().strip()
        if not query:
            self.status_label.setText("Please enter a game name to search")
            return

        if self.is_searching:
            return

        self.is_searching = True
        self.search_button.setEnabled(False)

        # Show status
        self.status_label.setText(f"Searching for '{query}'...")

        # Clear previous results
        self.clear_results()

        # Create a worker for the search and submit to thread pool
        worker = Worker(lambda: search_play_store(query, max_results=15))
        worker.result.connect(self.handle_search_results)
        worker.error.connect(self.handle_search_error)
        worker.finished.connect(lambda: self.search_complete())

        # Submit to thread pool for managed execution
        worker.submit_to_pool()

    def search_complete(self):
        """Called when the search is complete"""
        self.is_searching = False
        self.search_button.setEnabled(True)

    def handle_search_results(self, results):
        """Handle the search results"""
        if isinstance(results, dict) and 'error' in results:
            self.status_label.setText(f"Error: {results['error']}")
            return

        if not results:
            self.status_label.setText("No results found. Try a different search term.")
            return

        self.search_results = results
        self.status_label.setText(f"Found {len(results)} results. Select one to continue or click 'Skip This Game' to pass.")

        # Display the results
        for i, result in enumerate(results):
            self.add_result_item(i, result)

    def handle_search_error(self, error):
        """Handle search errors"""
        self.status_label.setText(f"Error: {error}")

    def clear_results(self):
        """Clear all search results"""
        self.search_results = []
        self.selected_result = None
        self.select_button.setEnabled(False)

        # Clear the results layout
        while self.results_layout.count():
            item = self.results_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

    def add_result_item(self, index, result):
        """Add a result item to the results layout"""
        # Create a frame for the result
        result_frame = QFrame()
        result_frame.setFrameShape(QFrame.Shape.StyledPanel)
        result_frame.setObjectName(f"result_frame_{index}")
        result_frame.setStyleSheet(f"""
            QFrame#result_frame_{index} {{
                background-color: {COLOR_GUNMETAL_2};
                border-radius: {dp(8)}px;
                border: 1px solid {COLOR_GRADIENT_LIGHT};
                padding: {dp(5)}px;
            }}
            QFrame#result_frame_{index}:hover {{
                border: 2px solid {COLOR_VIVID_SKY_BLUE};
                background-color: {COLOR_GUNMETAL};
            }}
        """)

        # Layout for the result
        result_layout = QHBoxLayout(result_frame)
        result_layout.setContentsMargins(dp(15), dp(15), dp(15), dp(15))
        result_layout.setSpacing(dp(20))

        # Icon - LARGER SIZE
        icon_label = QLabel()
        icon_label.setFixedSize(dp(80), dp(80))  # Larger icon
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet(f"""
            border-radius: {dp(10)}px;
            background-color: {COLOR_GUNMETAL};
            padding: {dp(2)}px;
        """)

        # Create a placeholder icon first
        placeholder = QPixmap(dp(80), dp(80))
        placeholder.fill(QColor(COLOR_VISTA_BLUE))

        painter = QPainter(placeholder)
        painter.setPen(Qt.GlobalColor.white)
        painter.setFont(QFont("Arial", dp(30), QFont.Weight.Bold))  # Larger font

        # Get first letter of app name for placeholder
        first_letter = result['app_name'][0].upper() if result['app_name'] else "?"
        painter.drawText(placeholder.rect(), Qt.AlignmentFlag.AlignCenter, first_letter)
        painter.end()

        icon_label.setPixmap(placeholder)

        # Load the actual icon if available
        if result['icon_url']:
            self.load_icon_for_label(result['icon_url'], icon_label)

        result_layout.addWidget(icon_label)

        # App details - MORE DETAILS
        details_layout = QVBoxLayout()
        details_layout.setSpacing(dp(8))

        # App name - LARGER
        app_name_label = QLabel(result['app_name'])
        app_name_label.setStyleSheet(f"""
            font-size: {dp(18)}px;
            font-weight: bold;
            color: {COLOR_TEXT_LIGHT};
        """)
        details_layout.addWidget(app_name_label)

        # Developer
        developer_label = QLabel(f"Developer: {result['developer']}")
        developer_label.setStyleSheet(f"""
            font-size: {dp(14)}px;
            color: {COLOR_TEXT_LIGHT};
        """)
        details_layout.addWidget(developer_label)

        # Package name
        package_label = QLabel(f"Package: {result['package_name']}")
        package_label.setStyleSheet(f"""
            font-size: {dp(14)}px;
            color: {COLOR_TEXT_LIGHT};
        """)
        details_layout.addWidget(package_label)

        # Rating, downloads, and other details (if available)
        stats_layout = QHBoxLayout()

        # Rating with stars
        if 'rating' in result and result['rating']:
            try:
                rating_value = float(result['rating'])
                rating_label = QLabel(f"Rating: {rating_value:.1f}★")
                rating_label.setStyleSheet(f"""
                    font-size: {dp(14)}px;
                    color: {COLOR_VIVID_SKY_BLUE};
                    font-weight: bold;
                """)
                stats_layout.addWidget(rating_label)
            except (ValueError, TypeError):
                pass

        # Downloads count
        if 'downloads' in result and result['downloads'] and result['downloads'] != 'Unknown':
            downloads_label = QLabel(f"Downloads: {result['downloads']}")
            downloads_label.setStyleSheet(f"""
                font-size: {dp(14)}px;
                color: {COLOR_TEXT_LIGHT};
            """)
            stats_layout.addWidget(downloads_label)

        # Price information
        if 'price' in result:
            price_text = result['price']
            if price_text == 'Free' or not price_text:
                price_label = QLabel("Free")
                price_label.setStyleSheet(f"""
                    font-size: {dp(14)}px;
                    color: #4CAF50;
                    font-weight: bold;
                """)
            else:
                price_label = QLabel(f"Price: {price_text}")
                price_label.setStyleSheet(f"""
                    font-size: {dp(14)}px;
                    color: {COLOR_VISTA_BLUE};
                """)
            stats_layout.addWidget(price_label)

        if stats_layout.count() > 0:
            stats_layout.addStretch()
            details_layout.addLayout(stats_layout)

        # Category (if available)
        if 'category' in result and result['category'] and result['category'] != 'Unknown':
            category_label = QLabel(f"Category: {result['category']}")
            category_label.setStyleSheet(f"""
                font-size: {dp(14)}px;
                color: {COLOR_MOONSTONE};
                font-style: italic;
            """)
            details_layout.addWidget(category_label)

        # Description (if available)
        if 'description' in result and result['description']:
            # Truncate description to first 100 characters
            short_desc = result['description'][:100] + "..." if len(result['description']) > 100 else result['description']
            desc_label = QLabel(short_desc)
            desc_label.setWordWrap(True)
            desc_label.setStyleSheet(f"""
                font-size: {dp(12)}px;
                color: {COLOR_TEXT_LIGHT};
                font-style: italic;
                margin-top: {dp(5)}px;
            """)
            details_layout.addWidget(desc_label)

        result_layout.addLayout(details_layout, 1)

        # Select button - IMPROVED
        select_radio = QRadioButton("Select")
        select_radio.setStyleSheet(f"""
            QRadioButton {{
                color: {COLOR_TEXT_LIGHT};
                font-size: {dp(16)}px;
                font-weight: bold;
            }}
            QRadioButton::indicator {{
                width: {dp(24)}px;
                height: {dp(24)}px;
            }}
        """)
        select_radio.toggled.connect(lambda checked, idx=index: self.on_result_selected(idx, checked))
        result_layout.addWidget(select_radio)

        # Add to results layout
        self.results_layout.addWidget(result_frame)

    def on_result_selected(self, index, checked):
        """Handle result selection"""
        if checked and 0 <= index < len(self.search_results):
            self.selected_result = self.search_results[index]
            self.select_button.setEnabled(True)
        elif not checked and self.selected_result == self.search_results[index]:
            self.selected_result = None
            self.select_button.setEnabled(False)

    def load_icon_for_label(self, icon_url, label):
        """Load an icon from a URL and set it to a label - optimized version"""
        if not icon_url:
            return

        # Check if we have this icon in cache
        if icon_url in ICON_CACHE:
            # Use cached pixmap
            pixmap = ICON_CACHE[icon_url]

            # Scale the pixmap to fit the label
            scaled_pixmap = pixmap.scaled(
                label.width(), label.height(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.FastTransformation  # Use faster transformation
            )

            # Update the label
            label.setPixmap(scaled_pixmap)
            return

        # Create a worker to load the icon using our optimized function
        worker = Worker(lambda: self._load_and_process_icon(icon_url, label.width(), label.height()))
        worker.result.connect(lambda pixmap: self._set_icon_to_label(label, pixmap))

        # Use weak references to prevent memory leaks
        worker.use_weak_references()

        # Submit to thread pool for managed execution
        worker.submit_to_pool()

    def _load_and_process_icon(self, icon_url, width, height):
        """Worker function to load and process an icon"""
        try:
            # Try to get a higher resolution icon by modifying the URL
            high_res_url = icon_url
            if '=w' in high_res_url:
                # Replace width and height parameters for higher resolution
                high_res_url = re.sub(r'=w\d+-h\d+', '=w512-h512', high_res_url)

            # Use our optimized image loading function
            pixmap = load_image_from_url(high_res_url, timeout=5)

            if pixmap and not pixmap.isNull():
                # Cache the original pixmap
                if len(ICON_CACHE) >= MAX_ICON_CACHE_SIZE:
                    # Remove oldest item if cache is full
                    ICON_CACHE.pop(next(iter(ICON_CACHE)))
                ICON_CACHE[icon_url] = pixmap

                # Scale the pixmap to fit the label
                scaled_pixmap = pixmap.scaled(
                    width, height,
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.FastTransformation  # Use faster transformation
                )

                return scaled_pixmap
        except Exception as e:
            print(f"Error loading icon: {e}")

        return None

    def _set_icon_to_label(self, label, pixmap):
        """Set the pixmap to the label if it's valid"""
        if pixmap and not pixmap.isNull():
            label.setPixmap(pixmap)

    def get_selected_result(self):
        """Get the selected search result"""
        return self.selected_result

# AnimatedBar class has been removed to improve performance and stability

class ImageBackgroundBox(QFrame):
    """
    A QFrame with a background image from the @images/response_backgrounds directory.
    Highly optimized for performance and memory usage.
    """
    # Class-level variables for shared resources
    current_image_index = 0
    background_images = []
    _blur_cache = LRUCache(10)  # Reduced cache size to save memory
    _stylesheet_cache = LRUCache(10)  # Reduced cache size
    _images_loaded = False  # Flag to track if images have been loaded
    _loading_in_progress = False  # Flag to prevent multiple concurrent loads

    # Simplified gradients list for better performance
    _gradients = [
        (COLOR_GRADIENT_DARK, COLOR_MIDNIGHT_GREEN),
        (COLOR_MIDNIGHT_GREEN, COLOR_GUNMETAL_2),
    ]

    @classmethod
    def load_background_images(cls):
        """Load all background images from the images/response_backgrounds directory."""
        # Look for image files in the directory - try different path formats with resource_path
        possible_paths = [
            resource_path(os.path.join("images", "response_backgrounds")),
            resource_path(os.path.join("@images", "response_backgrounds")),
            resource_path("images/response_backgrounds"),
            resource_path("@images/response_backgrounds")
        ]

        image_path = None
        for path in possible_paths:
            if os.path.exists(path):
                image_path = path
                break

        if not image_path:
            return

        # Find all image files with common extensions - more efficient approach
        image_files = []
        for ext in ['*.jpg', '*.jpeg', '*.png', '*.webp']:
            image_files.extend(glob.glob(os.path.join(image_path, ext)))

        # Store the list of image files
        cls.background_images = image_files

    @classmethod
    def clear_cache(cls):
        """Clear the image cache to free memory"""
        cls._blur_cache.clear()
        cls._stylesheet_cache.clear()

    @classmethod
    def get_stylesheet(cls, object_name, response_log=True, fallback=False, gradient_colors=None):
        """Get cached stylesheet or create a new one"""
        # Create a cache key
        key = f"{object_name}_{response_log}_{fallback}_{gradient_colors}"

        # Check cache first using our thread-safe LRU implementation
        cached_stylesheet = cls._stylesheet_cache.get(key)
        if cached_stylesheet:
            return cached_stylesheet

        # Create the stylesheet
        if fallback and gradient_colors:
            start_color, end_color = gradient_colors
            stylesheet = f"""
                QFrame#{object_name} {{
                    background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1,
                                                    stop:0 {start_color},
                                                    stop:1 {end_color});
                    border-radius: {dp(10)}px;
                    padding: {dp(1)}px;
                    border: 1px solid {QColor(COLOR_VISTA_BLUE).darker(150).name()};
                }}
                /* Ensure child widgets inside have transparent background */
                QFrame#{object_name} > QLabel,
                QFrame#{object_name} > QTextEdit {{
                    background-color: transparent;
                }}
            """
        else:
            stylesheet = f"""
                QFrame#{object_name} {{
                    border-radius: {dp(10)}px;
                    padding: {dp(1)}px;
                    border: 1px solid {QColor(COLOR_VISTA_BLUE).darker(150).name()};
                }}
                /* Ensure child widgets inside have transparent background */
                QFrame#{object_name} > QLabel {{
                    background-color: transparent;
                }}
            """

        # Add response log styling if needed
        if response_log:
            stylesheet += f"""
                QTextEdit#ResponseLog {{ /* Specific style for text edit inside */
                    background-color: {QColor(COLOR_GUNMETAL_2).darker(250).name()}f5; /* Much darker background with very high opacity (96%) */
                    border-radius: {dp(8)}px; /* Slightly smaller radius */
                    border: 1px solid {QColor(COLOR_VISTA_BLUE).darker(150).name()};
                    padding: {dp(8)}px; /* Add padding for text */
                    margin: {dp(5)}px; /* Add margin around the text area */
                    color: {COLOR_TEXT_LIGHT}; /* Ensure text is light colored for better contrast */
                }}
            """

        # Cache the stylesheet using our thread-safe LRU implementation
        cls._stylesheet_cache.put(key, stylesheet)

        return stylesheet

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("ImageBackgroundBox")

        # Instance variables
        self.background_image = None
        self.cached_blurred_pixmap = None
        self.cached_size = None
        self._is_visible = True

        # Load background images if not already loaded
        if not ImageBackgroundBox.background_images:
            ImageBackgroundBox.load_background_images()

        # Set initial background
        self.set_random_background()

    def showEvent(self, event):
        """Handle show events"""
        super().showEvent(event)
        self._is_visible = True

    def hideEvent(self, event):
        """Handle hide events"""
        super().hideEvent(event)
        self._is_visible = False

    def set_random_background(self):
        """Sets a random background image from the available images."""
        # If no images are available, fall back to gradient background
        if not ImageBackgroundBox.background_images:
            self.set_fallback_gradient()
            return

        # Rotate through the available images
        ImageBackgroundBox.current_image_index = (
            ImageBackgroundBox.current_image_index + 1
        ) % len(ImageBackgroundBox.background_images)
        self.background_image = ImageBackgroundBox.background_images[
            ImageBackgroundBox.current_image_index
        ]

        # Reset cached pixmaps
        self.cached_blurred_pixmap = None
        self.cached_size = None

        # Apply the stylesheet
        self.setStyleSheet(ImageBackgroundBox.get_stylesheet(self.objectName()))

        # Force a repaint to show the new background
        if self._is_visible:
            self.update()

    def set_fallback_gradient(self):
        """Fallback to a gradient background if no images are available."""
        # Choose a random gradient
        gradient_index = random.randint(0, len(ImageBackgroundBox._gradients) - 1)
        gradient_colors = ImageBackgroundBox._gradients[gradient_index]

        # Apply gradient using cached stylesheet
        self.setStyleSheet(
            ImageBackgroundBox.get_stylesheet(
                self.objectName(),
                fallback=True,
                gradient_colors=gradient_colors
            )
        )

    def resizeEvent(self, event):
        """Handle resize events to invalidate cached pixmaps"""
        super().resizeEvent(event)
        # Reset cached pixmaps when size changes
        self.cached_blurred_pixmap = None
        self.cached_size = None

    @classmethod
    def get_or_create_blurred_pixmap(cls, image_path, size):
        """Get a cached blurred pixmap or create a new one - optimized version"""
        # Create a cache key based on image path and size
        cache_key = f"{image_path}_{size.width()}x{size.height()}"

        # Check if we have this in cache using our thread-safe LRU implementation
        cached_pixmap = cls._blur_cache.get(cache_key)
        if cached_pixmap:
            return cached_pixmap

        # Not in cache, need to create it
        if not os.path.exists(image_path):
            # Try with resource_path as a fallback
            resource_image_path = resource_path(image_path)
            if os.path.exists(resource_image_path):
                image_path = resource_image_path
            else:
                return None

        try:
            # Load and process the image
            image = QImage(image_path)
            if image.isNull():
                return None

            # Scale the image - use faster scaling for better performance
            scaled_size = size * 1.5  # Zoom in by 50%
            scaled_image = image.scaled(
                scaled_size,
                Qt.AspectRatioMode.KeepAspectRatioByExpanding,
                Qt.TransformationMode.FastTransformation
            )

            # Create a pixmap directly from the scaled image
            pixmap = QPixmap.fromImage(scaled_image)

            # Apply a dark overlay to simulate blur effect
            overlay_painter = QPainter(pixmap)
            overlay_painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_SourceAtop)
            overlay_painter.fillRect(pixmap.rect(), QColor(0, 0, 0, 180))
            overlay_painter.end()

            # Store in cache using our thread-safe LRU implementation
            cls._blur_cache.put(cache_key, pixmap)

            return pixmap

        except Exception as e:
            print(f"Error creating blurred pixmap: {e}")
            return None

    def paintEvent(self, event):
        """Optimized paint event to draw the background image with darker blur effect."""
        # Skip if not visible
        if not self._is_visible:
            super().paintEvent(event)
            return

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)

        rect = self.rect()

        # Create a path for the rounded rectangle (for clipping)
        path = QPainterPath()
        adjusted_rect = QRectF(rect.adjusted(5, 5, -5, -5))
        path.addRoundedRect(adjusted_rect, dp(10), dp(10))

        # Set clipping path to ensure everything respects the rounded corners
        painter.setClipPath(path)

        # First, draw a very dark background
        dark_overlay = QColor(COLOR_GUNMETAL_2)
        dark_overlay.setAlpha(230)
        painter.fillRect(rect, dark_overlay)

        # If we have a background image, draw it with blur effect
        if self.background_image and os.path.exists(self.background_image):
            current_size = self.size()

            # Check if we need to recreate the cached pixmap
            if self.cached_blurred_pixmap is None or self.cached_size != current_size:
                # Get or create the blurred pixmap
                self.cached_blurred_pixmap = ImageBackgroundBox.get_or_create_blurred_pixmap(
                    self.background_image, current_size
                )
                self.cached_size = current_size

            # Draw the blurred image if available
            if self.cached_blurred_pixmap:
                scaled_size = current_size * 1.5
                x_offset = int((scaled_size.width() - self.width()) / 2)
                y_offset = int((scaled_size.height() - self.height()) / 2)

                # Draw the blurred image centered and zoomed with reduced opacity
                painter.setOpacity(0.4)
                painter.drawPixmap(
                    self.rect(),
                    self.cached_blurred_pixmap,
                    QRect(x_offset, y_offset, self.width(), self.height())
                )
                painter.setOpacity(1.0)

        # Reset clipping
        painter.setClipping(False)

        # Draw a border around the image
        border_color = QColor(COLOR_VISTA_BLUE).darker(120)
        border_color.setAlpha(180)
        painter.setPen(QPen(border_color, 2))
        painter.setBrush(Qt.BrushStyle.NoBrush)
        border_rect = QRectF(rect.adjusted(5, 5, -5, -5))
        painter.drawRoundedRect(border_rect, dp(10), dp(10))

        # Let the parent class handle the rest
        super().paintEvent(event)

# Keep the original RotatingBackgroundBox for backward compatibility
class RotatingBackgroundBox(ImageBackgroundBox):
    """Legacy class that now inherits from ImageBackgroundBox for backward compatibility."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("RotatingBackgroundBox")

    def set_random_gradient(self):
        """For backward compatibility, now calls set_random_background."""
        self.set_random_background()

class BackgroundTableWidget(QTableWidget):
    """Custom QTableWidget with a background image"""
    def __init__(self, rows, cols, parent=None):
        super().__init__(rows, cols, parent)
        self.background_image = None
        self.cached_blurred_pixmap = None
        self.cached_size = None
        self.setObjectName("BackgroundTableWidget")

        # Load background images if not already loaded
        if not ImageBackgroundBox.background_images:
            ImageBackgroundBox.load_background_images()

        # Set initial background
        self.set_random_background()

    def set_random_background(self):
        """Sets a random background image from the available images."""
        # If no images are available, return
        if not ImageBackgroundBox.background_images:
            return

        # Rotate through the available images
        ImageBackgroundBox.current_image_index = (ImageBackgroundBox.current_image_index + 1) % len(ImageBackgroundBox.background_images)
        self.background_image = ImageBackgroundBox.background_images[ImageBackgroundBox.current_image_index]

        # Reset cached pixmaps
        self.cached_blurred_pixmap = None
        self.cached_size = None

        # Force a repaint to show the new background
        self.update()

    def resizeEvent(self, event):
        """Handle resize events to invalidate cached pixmaps"""
        super().resizeEvent(event)
        # Reset cached pixmaps when size changes
        self.cached_blurred_pixmap = None
        self.cached_size = None

    def paintEvent(self, event):
        """Custom paint event to draw the background image with darker blur effect."""
        # Draw the background image first
        background_path = self.background_image
        if background_path and not os.path.exists(background_path):
            # Try with resource_path
            background_path = resource_path(background_path)

        if background_path and os.path.exists(background_path):
            painter = QPainter(self.viewport())
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)

            # First, draw a very dark background to make the blur effect darker
            dark_overlay = QColor(COLOR_GUNMETAL_2)
            dark_overlay.setAlpha(230)  # 90% opacity for the dark overlay
            painter.fillRect(self.rect(), dark_overlay)

            current_size = self.size()

            # Check if we need to recreate the cached pixmaps
            if self.cached_blurred_pixmap is None or self.cached_size != current_size:
                # Use the shared caching mechanism from ImageBackgroundBox
                blurred_pixmap = ImageBackgroundBox.get_or_create_blurred_pixmap(
                    background_path, current_size)

                if blurred_pixmap:
                    # Cache the pixmap and size
                    self.cached_blurred_pixmap = blurred_pixmap
                    self.cached_size = current_size

            # Draw the blurred image if available
            if self.cached_blurred_pixmap:
                scaled_size = current_size * 1.5
                x_offset = int((scaled_size.width() - self.width()) / 2)
                y_offset = int((scaled_size.height() - self.height()) / 2)

                # Draw the blurred image centered and zoomed with reduced opacity
                painter.setOpacity(0.4)  # Reduced opacity for darker effect
                painter.drawPixmap(self.rect(), self.cached_blurred_pixmap,
                                  QRect(x_offset, y_offset, self.width(), self.height()))

                # Reset opacity
                painter.setOpacity(1.0)

            painter.end()

        # Call the parent's paintEvent to draw the table content
        super().paintEvent(event)

class BaseScreenWidget(QWidget): # Renamed from BaseScreen to avoid conflict with Kivy's Screen
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAutoFillBackground(True)
        palette = self.palette()
        # Use a slightly lighter gradient for the base screen background
        # to contrast with the darker nav drawer
        gradient = QLinearGradient(0, 0, 0, self.height())
        gradient.setColorAt(0, QColor(COLOR_GRADIENT_LIGHT))
        gradient.setColorAt(1, QColor(COLOR_GRADIENT_DARK))
        palette.setBrush(QPalette.ColorRole.Window, QBrush(gradient))
        # palette.setColor(QPalette.ColorRole.Window, QColor(COLOR_GRADIENT_DARK)) # Original solid color
        self.setPalette(palette)

    # Override resizeEvent to update gradient if necessary (for more complex gradients)
    # def resizeEvent(self, event):
    #     super().resizeEvent(event)
    #     # Update gradient final stop if it depends on height
    #     palette = self.palette()
    #     brush = palette.brush(QPalette.ColorRole.Window)
    #     if isinstance(brush.gradient(), QLinearGradient):
    #         gradient = brush.gradient()
    #         gradient.setFinalStop(0, self.height())
    #         palette.setBrush(QPalette.ColorRole.Window, QBrush(gradient))
    #         self.setPalette(palette)


# --- Placeholder Screens (Integrated PyQt6 versions) ---
class PlaceholderScreen(BaseScreenWidget):
    # Base class for new screens, can be customized further
    def __init__(self, screen_name="Placeholder", parent=None):
        super().__init__(parent)
        self.screen_name = screen_name
        layout = QVBoxLayout(self)
        layout.setContentsMargins(dp(20), dp(20), dp(20), dp(20))

        title = QLabel(screen_name)
        title.setObjectName("ScreenTitleLabel") # Style as screen title
        layout.addWidget(title, alignment=Qt.AlignmentFlag.AlignTop)

        content_label = QLabel(f"Content for {screen_name} goes here.")
        content_label.setFont(QFont("Arial", dp(16)))
        content_label.setStyleSheet(f"color: {COLOR_TEXT_LIGHT};")
        content_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(content_label, 1) # Allow label to expand

# --- History Screen ---
class HistoryScreen(BaseScreenWidget):
    screen_name_attr = "history_screen"

    def __init__(self, parent_app=None, parent=None):
        super().__init__(parent)
        self.app = parent_app
        self.worker_thread = None

        # Connect to history data cache updates
        history_data_cache.data_updated.connect(self.refresh_data)

        self._build_ui()
        self.refresh_data()

    def _build_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(dp(20), dp(20), dp(20), dp(20))
        main_layout.setSpacing(dp(15))

        # Title with action buttons
        title_layout = QHBoxLayout()

        title = QLabel("History")
        title.setObjectName("ScreenTitleLabel")
        title_layout.addWidget(title)

        title_layout.addStretch()

        refresh_btn = QPushButton("🔄 Refresh")
        refresh_btn.clicked.connect(self.refresh_data)
        title_layout.addWidget(refresh_btn)

        clear_btn = QPushButton("🗑️ Clear History")
        clear_btn.clicked.connect(self.clear_history)
        title_layout.addWidget(clear_btn)

        main_layout.addLayout(title_layout)

        # Create a tab widget to separate attribution and event history
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid {COLOR_VISTA_BLUE};
                border-radius: {dp(5)}px;
                background-color: {COLOR_GUNMETAL_2};
            }}
            QTabBar::tab {{
                background-color: {COLOR_GUNMETAL};
                color: {COLOR_TEXT_LIGHT};
                border: 1px solid {COLOR_VISTA_BLUE};
                border-bottom-color: {COLOR_VISTA_BLUE};
                border-top-left-radius: {dp(5)}px;
                border-top-right-radius: {dp(5)}px;
                padding: {dp(8)}px {dp(15)}px;
                margin-right: {dp(2)}px;
                font-size: {dp(14)}px;
                font-weight: bold;
            }}
            QTabBar::tab:selected {{
                background-color: {COLOR_BLUE_VIOLET};
                border-bottom-color: {COLOR_BLUE_VIOLET};
            }}
            QTabBar::tab:hover {{
                background-color: {COLOR_HOVER_BG};
            }}
        """)

        # Attribution History Tab
        attribution_tab = QWidget()
        attribution_layout = QVBoxLayout(attribution_tab)

        # Attribution table
        self.attribution_table = BackgroundTableWidget(0, 4)
        self.attribution_table.setHorizontalHeaderLabels(["Game Name", "Package ID", "GPS ADID", "Timestamp"])
        self.attribution_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        self.attribution_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        self.attribution_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        self.attribution_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        self.attribution_table.verticalHeader().setVisible(False)
        self.attribution_table.setShowGrid(False)
        self.attribution_table.setAlternatingRowColors(True)
        self.attribution_table.verticalHeader().setDefaultSectionSize(dp(40))

        # Style the table
        self.attribution_table.setStyleSheet(f"""
            QTableWidget {{
                color: {COLOR_TEXT_LIGHT};
                border-radius: {dp(10)}px;
                border: 1px solid {QColor(COLOR_VISTA_BLUE).darker(150).name()};
                gridline-color: {COLOR_MIDNIGHT_GREEN};
                selection-background-color: {COLOR_BLUE_VIOLET};
                selection-color: white;
                font-size: {dp(14)}px;
                font-weight: bold;
            }}
            QTableWidget::item {{
                padding: {dp(8)}px;
                border-radius: {dp(4)}px;
                background-color: transparent;
            }}
            QTableWidget::item:selected {{
                background-color: {QColor(COLOR_BLUE_VIOLET).name()}aa;
            }}
            QHeaderView::section {{
                background-color: {QColor(COLOR_GUNMETAL_2).name()}dd;
                color: {COLOR_VISTA_BLUE};
                padding: {dp(8)}px;
                border: none;
                border-bottom: 1px solid {COLOR_VISTA_BLUE};
                font-weight: bold;
                font-size: {dp(12)}px;
            }}
            QTableWidget::item:alternate {{
                background-color: {QColor(COLOR_GUNMETAL).lighter(120).name()}55;
            }}
        """)

        attribution_layout.addWidget(self.attribution_table)

        # Event History Tab
        event_tab = QWidget()
        event_layout = QVBoxLayout(event_tab)

        # Event table
        self.event_table = BackgroundTableWidget(0, 4)
        self.event_table.setHorizontalHeaderLabels(["Game Name", "Event Name", "GPS ADID", "Timestamp"])
        self.event_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        self.event_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        self.event_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        self.event_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        self.event_table.verticalHeader().setVisible(False)
        self.event_table.setShowGrid(False)
        self.event_table.setAlternatingRowColors(True)
        self.event_table.verticalHeader().setDefaultSectionSize(dp(40))

        # Use the same style as attribution table
        self.event_table.setStyleSheet(self.attribution_table.styleSheet())

        event_layout.addWidget(self.event_table)

        # Add tabs to tab widget
        self.tab_widget.addTab(attribution_tab, "Attribution History")
        self.tab_widget.addTab(event_tab, "Event History")

        main_layout.addWidget(self.tab_widget)

        # Status label at the bottom
        self.status_label = QLabel("Status: Ready")
        self.status_label.setFixedHeight(dp(30))
        main_layout.addWidget(self.status_label)

    def refresh_data(self):
        """Refresh the data from the history data cache"""
        self.update_status("Refreshing data...")

        # Get attribution history
        attributions = history_data_cache.get_attributions()
        self.attribution_table.setRowCount(0)

        for row, attribution in enumerate(attributions):
            self.attribution_table.insertRow(row)

            game_name_item = QTableWidgetItem(attribution.get("game_name", ""))
            self.attribution_table.setItem(row, 0, game_name_item)

            package_id_item = QTableWidgetItem(attribution.get("package_id", ""))
            self.attribution_table.setItem(row, 1, package_id_item)

            gps_adid_item = QTableWidgetItem(attribution.get("gps_adid", ""))
            self.attribution_table.setItem(row, 2, gps_adid_item)

            timestamp_item = QTableWidgetItem(attribution.get("timestamp", ""))
            self.attribution_table.setItem(row, 3, timestamp_item)

        # Get event history
        events = history_data_cache.get_events()
        self.event_table.setRowCount(0)

        for row, event in enumerate(events):
            self.event_table.insertRow(row)

            game_name_item = QTableWidgetItem(event.get("game_name", ""))
            self.event_table.setItem(row, 0, game_name_item)

            event_name_item = QTableWidgetItem(event.get("event_name", ""))
            self.event_table.setItem(row, 1, event_name_item)

            gps_adid_item = QTableWidgetItem(event.get("gps_adid", ""))
            self.event_table.setItem(row, 2, gps_adid_item)

            timestamp_item = QTableWidgetItem(event.get("timestamp", ""))
            self.event_table.setItem(row, 3, timestamp_item)

        self.update_status(f"Data refreshed. {len(attributions)} attributions, {len(events)} events.")

    def clear_history(self):
        """Clear all history data after confirmation"""
        confirm = QMessageBox.question(
            self,
            "Clear History",
            "Are you sure you want to clear all history data? This cannot be undone.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if confirm == QMessageBox.StandardButton.Yes:
            history_data_cache.clear()
            self.update_status("History cleared.")

    def update_status(self, message):
        """Update the status label"""
        self.status_label.setText(f"Status: {message}")

    def set_background(self):
        """Called when the screen becomes active to set a new background."""
        # No background rotation needed for this screen
        pass

# --- Attribution Screen ---
class AttributionScreen(BaseScreenWidget):
    screen_name_attr = "attribution_screen"

    def __init__(self, parent_app=None, parent=None): # Add parent_app
        super().__init__(parent)
        self.app = parent_app # Store app reference if needed for popups etc.

        # Initialize variables for attribution requests
        self.final_redirect_url = None
        self.extracted_package_id = None
        self.extracted_referrer = None
        self.extracted_app_token = None
        self.attribution_btn = None

        # Platform options (Android/iOS)
        self.platform_options = ["Android", "iOS"]
        self.current_platform = self.platform_options[0]  # Default to Android

        # Threading-related variables for safe UI updates
        self._pending_response_update = None
        self._button_enable_pending = False

        # QR scanner dialog reference removed

        self._build_ui()

    def _build_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(dp(20), dp(20), dp(20), dp(20))
        layout.setSpacing(dp(15))

        title = QLabel("Attribution")
        title.setObjectName("ScreenTitleLabel")
        layout.addWidget(title)

        # --- Form Area ---
        form_widget = QWidget()
        form_layout = QVBoxLayout(form_widget)
        form_layout.setSpacing(dp(10))

        # URL Input
        url_box = QHBoxLayout()
        url_box.addWidget(QLabel("URL:"))
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("Enter URL")
        url_box.addWidget(self.url_input, 1)
        btn_paste_url = QPushButton("Paste")
        btn_paste_url.setFixedWidth(dp(80)) # Smaller paste button
        btn_paste_url.clicked.connect(lambda: self.url_input.setText(QApplication.clipboard().text()))
        url_box.addWidget(btn_paste_url)
        form_layout.addLayout(url_box)

        # Platform Dropdown (Android/iOS)
        platform_box = QHBoxLayout()
        platform_box.addWidget(QLabel("Platform:"))
        self.platform_spinner = QComboBox()
        self.platform_spinner.addItems(self.platform_options)
        self.platform_spinner.currentTextChanged.connect(self.on_platform_selected)
        platform_box.addWidget(self.platform_spinner, 1)
        form_layout.addLayout(platform_box)

        # Device ID Input (GPS ADID or IDFA)
        adid_box = QHBoxLayout()
        self.device_id_label = QLabel("GPS ADID:")  # Will be updated based on platform selection
        adid_box.addWidget(self.device_id_label)
        self.gps_adid_input = QLineEdit()
        self.gps_adid_input.setPlaceholderText("Enter GPS ADID")
        adid_box.addWidget(self.gps_adid_input, 1)

        # Add dice button for random GPS ADID generation
        btn_random_adid = QPushButton("🎲")
        btn_random_adid.setFixedWidth(dp(40))
        btn_random_adid.setToolTip("Generate random GPS ADID")
        btn_random_adid.clicked.connect(self.generate_random_gps_adid)
        btn_random_adid.setStyleSheet(f"""
            background-color: {COLOR_VISTA_BLUE};
            color: white;
            font-weight: bold;
            font-size: {dp(16)}px;
            padding: {dp(2)}px;
            border-radius: {dp(5)}px;
        """)
        adid_box.addWidget(btn_random_adid)

        btn_paste_adid = QPushButton("Paste")
        btn_paste_adid.setFixedWidth(dp(80))
        btn_paste_adid.clicked.connect(lambda: self.gps_adid_input.setText(QApplication.clipboard().text()))
        adid_box.addWidget(btn_paste_adid)
        form_layout.addLayout(adid_box)

        # Action buttons
        action_layout = QHBoxLayout()

        # Complete Attribution button
        self.attribution_btn = QPushButton("🔄 Complete Attribution")
        self.attribution_btn.setStyleSheet(f"""
            background-color: {COLOR_BLUE_VIOLET};
            color: white;
            font-weight: bold;
            font-size: {dp(14)}px;
            padding: {dp(10)}px {dp(15)}px;
            border-radius: {dp(8)}px;
        """)
        self.attribution_btn.setMinimumHeight(dp(45))
        self.attribution_btn.clicked.connect(self.process_attribution)
        action_layout.addWidget(self.attribution_btn)

        # Scan QR Code button removed

        # Clear button
        clear_button = QPushButton("🗑️ Clear")
        clear_button.setStyleSheet(f"""
            background-color: {COLOR_MIDNIGHT_GREEN};
            color: white;
            font-weight: bold;
            font-size: {dp(14)}px;
            padding: {dp(10)}px {dp(15)}px;
            border-radius: {dp(8)}px;
        """)
        clear_button.setMinimumHeight(dp(45))
        clear_button.clicked.connect(self.clear_inputs)
        action_layout.addWidget(clear_button)

        form_layout.addLayout(action_layout)

        form_layout.addStretch(1) # Push form elements up
        layout.addWidget(form_widget) # Add form widget (no scroll needed yet)

        # --- Response Log Area ---
        self.response_log_container = RotatingBackgroundBox()
        response_layout = QVBoxLayout(self.response_log_container)
        response_layout.setContentsMargins(dp(10), dp(10), dp(10), dp(10))

        response_title = QLabel("Response Log")
        response_title.setFont(QFont("Arial", dp(14), QFont.Weight.Bold))
        response_layout.addWidget(response_title)

        self.response_text = QTextEdit()
        self.response_text.setReadOnly(True)
        self.response_text.setObjectName("ResponseLog")
        self.response_text.setPlaceholderText("Attribution results will appear here...")
        response_layout.addWidget(self.response_text)

        layout.addWidget(self.response_log_container, 1) # Response log takes expanding space

        # Set up a timer to process pending updates from background threads
        # This timer runs on the main thread and processes updates safely
        from PyQt6.QtCore import QTimer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._process_pending_updates)
        self.update_timer.start(100)  # Check for updates every 100ms

    def clear_inputs(self):
        """Clear all input fields and response text"""
        self.url_input.clear()
        self.gps_adid_input.clear()
        self.response_text.clear()

        # Reset platform to Android
        self.platform_spinner.setCurrentIndex(0)
        self.current_platform = self.platform_options[0]

        # Reset device ID label and placeholder
        self.device_id_label.setText("GPS ADID:")
        self.gps_adid_input.setPlaceholderText("Enter GPS ADID")

        # Reset attribution data
        self.final_redirect_url = None
        self.extracted_package_id = None
        self.extracted_referrer = None
        self.extracted_app_token = None

        # Re-enable the attribution button if it was disabled
        if hasattr(self, 'attribution_btn') and self.attribution_btn is not None:
            self.attribution_btn.setEnabled(True)

    def process_attribution(self):
        """Process the attribution URL by following redirects and making attribution requests"""
        url = self.url_input.text().strip()
        adid = self.gps_adid_input.text().strip()

        if not url:
            self.response_text.setText("Please enter a URL to process.")
            if self.app:
                self.app.show_info_popup("Input Error", "URL cannot be empty.")
            return

        # Get the device ID type name
        device_id_type = "IDFA" if self.current_platform == "iOS" else "GPS ADID"

        # Show initial processing message
        self.response_text.setText(f"Processing attribution...\nURL: {url}\n{device_id_type}: {adid}\nPlatform: {self.current_platform}\n\n")

        # Disable the button while processing
        if hasattr(self, 'attribution_btn') and self.attribution_btn is not None:
            self.attribution_btn.setEnabled(False)

        try:
            # Use thread pool for better thread management
            worker = Worker(self._follow_redirects_task, url)
            worker.result.connect(self._process_redirect_result_and_continue)
            worker.error.connect(self._handle_redirect_error)
            worker.finished.connect(lambda: self._ensure_button_enabled())

            # Submit to thread pool for managed execution
            worker.submit_to_pool()

            # Store a reference to prevent garbage collection
            self.worker_thread = worker

        except Exception as e:
            import traceback
            error_msg = f"Error starting attribution process: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)
            self.response_text.setText(f"{self.response_text.toPlainText()}\n\nError: {error_msg}")
            self._ensure_button_enabled()

    def _format_response_with_colors(self, status_code, response_type, response_text, response_data=None, attribution_info=None):
        """Format a response with color coding for better readability

        Args:
            status_code: HTTP status code or status indicator
            response_type: Type of response (SDK Click, Session, Attribution)
            response_text: Raw response text
            response_data: Optional parsed JSON data
            attribution_info: Optional attribution info

        Returns:
            Formatted HTML string with color coding
        """
        # Define a safe escape function
        def safe_escape(text):
            if text is None:
                return ""
            text = str(text)
            # Basic HTML escaping
            return text.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;").replace('"', "&quot;").replace("'", "&#39;")
        # Define colors for different status codes
        success_color = "#4CAF50"  # Green
        warning_color = "#FFC107"  # Amber
        error_color = "#F44336"    # Red
        header_color = "#2196F3"   # Blue
        section_color = "#9C27B0"  # Purple

        # Determine status color based on status code
        if isinstance(status_code, str):
            if "error" in status_code.lower():
                status_color = error_color
            elif "unknown" in status_code.lower():
                status_color = warning_color
            else:
                status_color = header_color
        else:
            if 200 <= status_code < 300:
                status_color = success_color
            elif 300 <= status_code < 400:
                status_color = warning_color
            else:
                status_color = error_color

        # Create a timestamp
        timestamp = time.strftime("%H:%M:%S")

        # Start building the HTML
        html = f"<div style='margin-bottom:15px; padding:10px; border-left:4px solid {header_color}; background-color:rgba(33,33,33,0.3);'>"
        html += f"<h3 style='color:{header_color}; margin:0;'>{response_type} Response - {timestamp}</h3>"
        html += f"<p style='color:{status_color}; font-weight:bold;'>Status: {status_code}</p>"

        # Add response body in a pre tag with word wrap
        html += f"<h4 style='color:{section_color}; margin-bottom:5px;'>RESPONSE BODY</h4>"
        # Escape the response text to avoid HTML injection issues
        escaped_text = safe_escape(response_text)
        html += f"<pre style='white-space:pre-wrap; word-break:break-word; background-color:rgba(0,0,0,0.2); padding:10px; border-radius:5px;'>{escaped_text}</pre>"

        # Add parsed JSON data if available
        if response_data:
            html += f"<h4 style='color:{section_color}; margin-bottom:5px;'>PARSED JSON RESPONSE</h4>"
            try:
                formatted_json = json.dumps(response_data, indent=2)
                escaped_json = safe_escape(formatted_json)
                html += f"<pre style='white-space:pre-wrap; word-break:break-word; background-color:rgba(0,0,0,0.2); padding:10px; border-radius:5px;'>{escaped_json}</pre>"
            except Exception as e:
                html += f"<pre style='color:{error_color};'>Error formatting JSON: {e}</pre>"

        # Add attribution info if available
        if attribution_info:
            html += f"<h4 style='color:{section_color}; margin-bottom:5px;'>ATTRIBUTION INFO</h4>"
            try:
                formatted_json = json.dumps(attribution_info, indent=2)
                escaped_json = safe_escape(formatted_json)
                html += f"<pre style='white-space:pre-wrap; word-break:break-word; background-color:rgba(0,0,0,0.2); padding:10px; border-radius:5px;'>{escaped_json}</pre>"

                # Add specific attribution details in a more readable format
                html += f"<h4 style='color:{section_color}; margin-bottom:5px;'>ATTRIBUTION DETAILS</h4>"
                html += "<ul style='margin-top:5px;'>"

                if 'tracker' in attribution_info:
                    html += f"<li><strong>Tracker:</strong> {attribution_info['tracker']}</li>"

                if 'campaign' in attribution_info:
                    html += f"<li><strong>Campaign:</strong> {attribution_info['campaign']}</li>"

                if 'adgroup' in attribution_info:
                    html += f"<li><strong>Adgroup:</strong> {attribution_info['adgroup']}</li>"

                if 'creative' in attribution_info:
                    html += f"<li><strong>Creative:</strong> {attribution_info['creative']}</li>"

                html += "</ul>"
            except Exception as e:
                html += f"<pre style='color:{error_color};'>Error formatting attribution info: {e}</pre>"

        # Close the div
        html += "</div>"

        return html

    def _update_response_text_safely(self, message, append=False, html=False):
        """Update the response text in a thread-safe manner with timeout protection

        Args:
            message: The message to display
            append: Whether to append to existing text or replace it
            html: Whether the message is HTML formatted
        """
        try:
            from PyQt6.QtGui import QTextCursor # Ensure QTextCursor is imported
            # Make sure we're on the main thread
            if threading.current_thread() != threading.main_thread():
                # Use a different approach to ensure this runs on the main thread
                # Store the parameters and use a flag to indicate pending update
                self._pending_response_update = (message, append, html)
                return

            # Update the response text
            if hasattr(self, 'response_text') and self.response_text is not None:
                # setTextFormat is not a valid QTextEdit method.
                # The format is determined by the method used to set/insert text (e.g., setHtml, insertHtml).
                if append:
                    # Get current text
                    # current_text = self.response_text.toHtml() if html else self.response_text.toPlainText() # Not strictly needed for new logic

                    # Append new message
                    try:
                        if html:
                            cursor = self.response_text.textCursor()
                            cursor.movePosition(QTextCursor.MoveOperation.End)
                            cursor.insertHtml(message) # Safely appends HTML fragment
                        else:
                            self.response_text.append(message) # Handles newline for plain text
                    except Exception as e:
                        # Fallback to plain text if HTML fails
                        print(f"Error updating response text with HTML: {e}, falling back to plain text")
                        try:
                            # Strip HTML tags for fallback
                            plain_message = message.replace("<", "&lt;").replace(">", "&gt;")
                            self.response_text.append(f"\n\n--- HTML RENDERING ERROR ---\n{plain_message}")
                        except Exception as e2:
                            print(f"Critical error in fallback text update: {e2}")
                else:
                    # Replace existing text
                    # For replacing, setHtml is appropriate. QTextEdit usually wraps fragments.
                    if html:
                        # If message is a fragment (like a div), QTextEdit will wrap it.
                        # If it's a full doc, it will use it.
                        self.response_text.setHtml(message)
                    else:
                        self.response_text.setText(message)

                # Scroll to the bottom to show the latest content
                self.response_text.verticalScrollBar().setValue(
                    self.response_text.verticalScrollBar().maximum()
                )

                # Process events to keep UI responsive, but limit the time spent here
                start_time = time.time()
                QApplication.processEvents()
                elapsed = time.time() - start_time
                if elapsed > 0.1:  # If processing events took more than 100ms, log a warning
                    print(f"WARNING: Processing events took {elapsed:.2f} seconds")
                else:
                    print(f"UI updated with message: {message[:50]}..." if len(message) > 50 else f"UI updated with message: {message}")
        except Exception as e:
            print(f"Error updating response text: {e}")

    def _ensure_button_enabled(self):
        """Ensure the attribution button is enabled, even after errors"""
        try:
            # Make sure we're on the main thread
            if threading.current_thread() != threading.main_thread():
                # Set a flag to indicate button should be enabled
                self._button_enable_pending = True
                return
            else:
                self._ensure_button_enabled_main_thread()
        except Exception as e:
            print(f"Error in _ensure_button_enabled: {e}")
            # Try one more time with a direct approach
            try:
                if hasattr(self, 'attribution_btn') and self.attribution_btn is not None:
                    self.attribution_btn.setEnabled(True)
            except:
                pass

    def _ensure_button_enabled_main_thread(self):
        """Enable the attribution button on the main thread with multiple safeguards"""
        try:
            # First try the attribution_btn
            if hasattr(self, 'attribution_btn') and self.attribution_btn is not None:
                self.attribution_btn.setEnabled(True)
                print("Attribution button re-enabled")

                # Process events to keep UI responsive
                QApplication.processEvents()

            # Also try attribution_button if it exists (for compatibility)
            if hasattr(self, 'attribution_button') and self.attribution_button is not None:
                self.attribution_button.setEnabled(True)
                print("Alternative attribution button re-enabled")

                # Process events again
                QApplication.processEvents()

            # Also try follow_redirects_btn if it exists
            if hasattr(self, 'follow_redirects_btn') and self.follow_redirects_btn is not None:
                self.follow_redirects_btn.setEnabled(True)

            # Try to find and enable any disabled buttons in this screen
            for button in self.findChildren(QPushButton):
                if not button.isEnabled():
                    button.setEnabled(True)
                    print(f"Re-enabled button: {button.text()}")
        except Exception as e:
            print(f"Error enabling attribution button: {e}")
            # Last resort - try to force UI update
            QApplication.processEvents()

    def _process_pending_updates(self):
        """Process any pending UI updates from background threads"""
        try:
            # Process pending response updates
            if hasattr(self, '_pending_response_update') and self._pending_response_update is not None:
                message, append, html = self._pending_response_update
                self._pending_response_update = None
                self._update_response_text_safely(message, append, html)

            # Process pending button enable requests
            if hasattr(self, '_button_enable_pending') and self._button_enable_pending:
                self._button_enable_pending = False
                self._ensure_button_enabled_main_thread()
        except Exception as e:
            print(f"Error processing pending updates: {e}")

    def _process_redirect_result_and_continue(self, result):
        """Process the redirect result and continue with attribution requests if possible"""
        try:
            # Safely extract values from result dictionary with fallbacks
            final_url = result.get('final_url', '')
            redirect_chain = result.get('redirect_chain', [])
            error = result.get('error', None)

            # Get the ADID from the input field
            adid = self.gps_adid_input.text().strip() if hasattr(self, 'gps_adid_input') else ''

            # Store the final URL for later use
            self.final_redirect_url = final_url

            # Build the response text with HTML formatting for better visual structure
            response_html = []

            # Add a styled header
            response_html.append("""
            <div style="background-color: rgba(33, 150, 243, 0.1); padding: 10px; border-left: 4px solid #2196F3; margin-bottom: 15px;">
                <h3 style="color: #2196F3; margin: 0;">Attribution Process</h3>
            </div>
            """)

            # Add the original URL with styling
            if redirect_chain:
                try:
                    original_url = redirect_chain[0].get('url', 'Unknown URL')
                    response_html.append(f"""
                    <div style="margin-bottom: 10px;">
                        <span style="font-weight: bold; color: #4CAF50;">Original URL:</span>
                        <span style="word-break: break-all;">{original_url}</span>
                    </div>
                    """)
                except (IndexError, AttributeError, TypeError) as e:
                    response_html.append(f"""
                    <div style="margin-bottom: 10px; color: #F44336;">
                        Error getting original URL: {str(e)}
                    </div>
                    """)

            # Add the redirect chain with improved styling
            if len(redirect_chain) > 1:
                response_html.append("""
                <div style="margin-top: 15px; margin-bottom: 10px;">
                    <span style="font-weight: bold; color: #4CAF50;">Redirect Chain:</span>
                </div>
                <div style="margin-left: 10px;">
                """)

                for i, redirect in enumerate(redirect_chain):
                    try:
                        url = redirect.get('url', 'Unknown URL')
                        status = redirect.get('status_code', 'Unknown Status')
                        rtype = redirect.get('type', 'Unknown Type')

                        response_html.append(f"""
                        <div style="margin-bottom: 10px; padding: 8px; background-color: rgba(0, 0, 0, 0.05); border-radius: 4px;">
                            <div style="font-weight: bold; color: #9C27B0;">{i+1}. <span style="word-break: break-all;">{url}</span></div>
                            <div style="margin-left: 15px; color: #FF9800;">Status: {status}</div>
                            <div style="margin-left: 15px; color: #FF9800;">Type: {rtype}</div>
                        </div>
                        """)
                    except Exception as e:
                        response_html.append(f"""
                        <div style="margin-bottom: 10px; padding: 8px; background-color: rgba(244, 67, 54, 0.1); border-radius: 4px;">
                            <div style="color: #F44336;">{i+1}. Error processing redirect: {str(e)}</div>
                        </div>
                        """)

                response_html.append("</div>")

            # Add the final URL with styling
            response_html.append(f"""
            <div style="margin-top: 15px; margin-bottom: 10px;">
                <span style="font-weight: bold; color: #4CAF50;">Final URL:</span>
                <span style="word-break: break-all;">{final_url}</span>
            </div>
            """)

            # Create a plain text version for fallback and internal use
            response_text = []
            if redirect_chain:
                try:
                    original_url = redirect_chain[0].get('url', 'Unknown URL')
                    response_text.append(f"Original URL: {original_url}")
                except (IndexError, AttributeError, TypeError) as e:
                    response_text.append(f"Error getting original URL: {str(e)}")

            if len(redirect_chain) > 1:
                response_text.append("\nRedirect Chain:")
                for i, redirect in enumerate(redirect_chain):
                    try:
                        url = redirect.get('url', 'Unknown URL')
                        status = redirect.get('status_code', 'Unknown Status')
                        rtype = redirect.get('type', 'Unknown Type')
                        response_text.append(f"{i+1}. {url}")
                        response_text.append(f"   Status: {status}")
                        response_text.append(f"   Type: {rtype}")
                    except Exception as e:
                        response_text.append(f"{i+1}. Error processing redirect: {str(e)}")

            response_text.append(f"\nFinal URL: {final_url}")

            # Add any error with styling
            if error:
                response_html.append(f"""
                <div style="margin-top: 15px; padding: 10px; background-color: rgba(244, 67, 54, 0.1); border-radius: 4px; border-left: 4px solid #F44336;">
                    <span style="font-weight: bold; color: #F44336;">Error:</span> {error}
                </div>
                """)
                response_text.append(f"\nError: {error}")
                self.response_text.setHtml('\n'.join(response_html))
                self._ensure_button_enabled()
                return

            # Check if the final URL is a Play Store URL, App Store URL, or market URL
            is_market_url = final_url.startswith('market://')
            is_play_store_url = 'play.google.com' in final_url
            is_app_store_url = 'apps.apple.com' in final_url or 'itunes.apple.com' in final_url

            # Set platform based on the URL type
            if is_app_store_url and hasattr(self, 'platform_spinner'):
                # Switch to iOS platform if we're dealing with an App Store URL
                self.platform_spinner.setCurrentText("iOS")
            elif (is_play_store_url or is_market_url) and hasattr(self, 'platform_spinner'):
                # Switch to Android platform if we're dealing with a Play Store URL
                self.platform_spinner.setCurrentText("Android")

            if not (is_market_url or is_play_store_url or is_app_store_url):
                response_text.append("\nError: Final URL is not a Play Store, App Store, or market URL.")
                self.response_text.setText('\n'.join(response_text))
                self._ensure_button_enabled()
                return

            # Extract app information with styled formatting
            try:
                app_info = self._extract_app_info(final_url)
                if app_info:
                    response_html.append("""
                    <div style="margin-top: 15px; margin-bottom: 10px;">
                        <span style="font-weight: bold; color: #4CAF50;">App Information:</span>
                    </div>
                    <div style="margin-left: 10px; padding: 10px; background-color: rgba(0, 0, 0, 0.05); border-radius: 4px;">
                    """)

                    # Add app info to both HTML and plain text
                    response_text.append("\nApp Information:")
                    for key, value in app_info.items():
                        response_html.append(f"""
                        <div style="margin-bottom: 5px;">
                            <span style="font-weight: bold; color: #9C27B0;">{key}:</span> {value}
                        </div>
                        """)
                        response_text.append(f"{key}: {value}")

                    response_html.append("</div>")
            except Exception as e:
                error_msg = str(e)
                response_html.append(f"""
                <div style="margin-top: 15px; padding: 10px; background-color: rgba(244, 67, 54, 0.1); border-radius: 4px;">
                    <span style="font-weight: bold; color: #F44336;">Error extracting app info:</span> {error_msg}
                </div>
                """)
                response_text.append(f"\nError extracting app info: {error_msg}")
                import traceback
                print(f"App info extraction error: {traceback.format_exc()}")

            # Extract package ID and referrer
            package_id = None
            referrer = None

            try:
                # Parse the URL to extract parameters
                parsed_url = urlparse(final_url)
                query_params = parse_qs(parsed_url.query)

                # Debug log
                print(f"Parsing final URL: {final_url}")
                print(f"Query parameters: {query_params}")

                # Extract package ID with styled formatting
                if 'id' in query_params and query_params['id'][0]:
                    package_id = query_params['id'][0]
                    response_html.append(f"""
                    <div style="margin-top: 15px; margin-bottom: 10px;">
                        <span style="font-weight: bold; color: #4CAF50;">Package ID:</span> {package_id}
                    </div>
                    """)
                    response_text.append(f"\nPackage ID: {package_id}")
                else:
                    # Try to extract package ID from the path if it's not in the query parameters
                    path_parts = parsed_url.path.strip('/').split('/')
                    if len(path_parts) > 0 and path_parts[-1] != 'details':
                        # Last part of the path might be the package ID
                        potential_package_id = path_parts[-1]
                        response_html.append(f"""
                        <div style="margin-top: 15px; margin-bottom: 10px;">
                            <span style="font-weight: bold; color: #4CAF50;">Potential Package ID (from path):</span> {potential_package_id}
                        </div>
                        """)
                        response_text.append(f"\nPotential Package ID (from path): {potential_package_id}")
                        package_id = potential_package_id
                    else:
                        response_html.append(f"""
                        <div style="margin-top: 15px; margin-bottom: 10px; color: #F44336;">
                            <span style="font-weight: bold;">Error:</span> Could not find package ID in URL
                        </div>
                        """)
                        response_text.append("\nError: Could not find package ID in URL")
                        package_id = None

                # Check for referrer in the original URL first (important for Play Store URLs)
                original_referrer = None
                if redirect_chain and len(redirect_chain) > 0:
                    for redirect in redirect_chain:
                        url = redirect.get('url', '')
                        if 'play.google.com/store/apps/details' in url or url.startswith('market://'):
                            parsed_redirect_url = urlparse(url)
                            redirect_query_params = parse_qs(parsed_redirect_url.query)
                            if 'referrer' in redirect_query_params and redirect_query_params['referrer'][0]:
                                original_referrer = redirect_query_params['referrer'][0]
                                print(f"Found referrer in redirect chain: {original_referrer}")
                                break

                # Extract referrer with styled formatting
                if original_referrer:
                    referrer = original_referrer
                    response_html.append(f"""
                    <div style="margin-top: 15px; margin-bottom: 10px;">
                        <span style="font-weight: bold; color: #4CAF50;">Referrer:</span>
                        <span style="word-break: break-all;">{referrer}</span>
                    </div>
                    """)
                    response_text.append(f"\nReferrer: {referrer}")
                elif 'referrer' in query_params and query_params['referrer'][0]:
                    referrer = query_params['referrer'][0]
                    response_html.append(f"""
                    <div style="margin-top: 15px; margin-bottom: 10px;">
                        <span style="font-weight: bold; color: #4CAF50;">Referrer:</span>
                        <span style="word-break: break-all;">{referrer}</span>
                    </div>
                    """)
                    response_text.append(f"\nReferrer: {referrer}")
                else:
                    response_html.append(f"""
                    <div style="margin-top: 15px; margin-bottom: 10px; color: #FF9800;">
                        <span style="font-weight: bold;">Warning:</span> No referrer found in URL
                    </div>
                    """)
                    response_text.append("\nWarning: No referrer found in URL")
                    referrer = None
            except Exception as e:
                error_msg = str(e)
                response_html.append(f"""
                <div style="margin-top: 15px; padding: 10px; background-color: rgba(244, 67, 54, 0.1); border-radius: 4px; border-left: 4px solid #F44336;">
                    <span style="font-weight: bold; color: #F44336;">Error parsing URL:</span> {error_msg}
                </div>
                """)
                response_text.append(f"\nError parsing URL: {error_msg}")
                import traceback
                print(f"URL parsing error: {traceback.format_exc()}")
                self.response_text.setHtml('\n'.join(response_html))
                self._ensure_button_enabled()
                return

            # Store the extracted data for attribution requests
            self.extracted_package_id = package_id
            self.extracted_referrer = referrer

            # Update the response text with what we have so far
            self.response_text.setHtml('\n'.join(response_html))

            # Check if we have the app token in local data
            try:
                app_token = self._find_app_token_for_package(package_id)
                if app_token:
                    response_html.append(f"""
                    <div style="margin-top: 15px; margin-bottom: 10px;">
                        <span style="font-weight: bold; color: #4CAF50;">Found App Token:</span> {app_token}
                    </div>
                    """)
                    response_text.append(f"\nFound App Token: {app_token}")
                    self.extracted_app_token = app_token

                    # Update the response text
                    self.response_text.setHtml('\n'.join(response_html))

                    # Continue with attribution requests
                    response_html.append("""
                    <div style="margin-top: 20px; padding: 10px; background-color: rgba(33, 150, 243, 0.1); border-radius: 4px; border-left: 4px solid #2196F3;">
                        <span style="font-weight: bold; color: #2196F3;">Proceeding with attribution requests...</span>
                    </div>
                    """)
                    response_text.append("\n\nProceeding with attribution requests...")
                    self.response_text.setHtml('\n'.join(response_html))

                    # Store parameters for use in the sequence
                    self.extracted_package_id = package_id
                    self.extracted_referrer = referrer
                    self.extracted_app_token = app_token

                    # Store parameters for use in the sequence
                    self.attribution_params = {
                        'package_id': package_id,
                        'referrer': referrer,
                        'app_token': app_token,
                        'device_id': adid,
                        'is_ios': self.current_platform == "iOS"
                    }

                    try:
                        # Start the first step in the sequence
                        self._start_sdk_click_request()
                    except Exception as e:
                        import traceback
                        error_msg = f"Error starting SDK Click request: {str(e)}"
                        trace_msg = traceback.format_exc()
                        print(f"{error_msg}\n{trace_msg}")

                        response_html.append(f"""
                        <div style="margin-top: 15px; padding: 10px; background-color: rgba(244, 67, 54, 0.1); border-radius: 4px; border-left: 4px solid #F44336;">
                            <span style="font-weight: bold; color: #F44336;">Error starting attribution process:</span> {error_msg}
                        </div>
                        """)
                        response_text.append(f"\nError starting attribution process: {str(e)}")
                        self.response_text.setHtml('\n'.join(response_html))
                        self._ensure_button_enabled()
                else:
                    response_html.append(f"""
                    <div style="margin-top: 15px; padding: 10px; background-color: rgba(244, 67, 54, 0.1); border-radius: 4px; border-left: 4px solid #F44336;">
                        <span style="font-weight: bold; color: #F44336;">Error:</span> Could not find app token for this package ID in local data.
                    </div>
                    """)
                    response_text.append("\nError: Could not find app token for this package ID in local data.")
                    self.response_text.setHtml('\n'.join(response_html))
                    self._ensure_button_enabled()
            except Exception as e:
                error_msg = str(e)
                response_html.append(f"""
                <div style="margin-top: 15px; padding: 10px; background-color: rgba(244, 67, 54, 0.1); border-radius: 4px; border-left: 4px solid #F44336;">
                    <span style="font-weight: bold; color: #F44336;">Error in attribution process:</span> {error_msg}
                </div>
                """)
                response_text.append(f"\nError in attribution process: {error_msg}")
                import traceback
                print(f"Attribution process error: {traceback.format_exc()}")
                self.response_text.setHtml('\n'.join(response_html))
                self._ensure_button_enabled()

        except Exception as e:
            # Catch-all for any unexpected errors
            import traceback
            error_msg = f"Unexpected error in redirect processing: {str(e)}"
            trace_msg = traceback.format_exc()
            print(f"{error_msg}\n{trace_msg}")

            if hasattr(self, 'response_text'):
                error_html = f"""
                <div style="padding: 15px; background-color: rgba(244, 67, 54, 0.1); border-radius: 4px; border-left: 4px solid #F44336;">
                    <h3 style="color: #F44336; margin-top: 0;">Error Processing Redirect</h3>
                    <p>{error_msg}</p>
                </div>
                """
                self.response_text.setHtml(error_html)

            self._ensure_button_enabled()

    def _follow_redirects_task(self, url):
        """Worker task to follow redirects with appropriate user agent based on platform.
        Enhanced to handle JavaScript redirects and complex redirect chains."""
        redirect_chain = []
        current_url = url
        max_redirects = 20  # Increased from 10 to handle longer chains
        redirect_count = 0
        error = None

        # Track visited URLs to detect loops
        visited_urls = set()

        # Use appropriate user agent based on platform
        if self.current_platform == "iOS":
            # iOS user agent
            user_agent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1 Mobile/15E148 Safari/604.1'
        else:
            # Android user agent
            user_agent = 'Mozilla/5.0 (Linux; Android 10; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.162 Mobile Safari/537.36'

        headers = {
            'User-Agent': user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }

        try:
            while redirect_count < max_redirects:
                # Normalize URL
                if not current_url.startswith(('http://', 'https://', 'market://')):
                    current_url = 'https://' + current_url

                # Check for redirect loops
                if current_url in visited_urls:
                    redirect_chain.append({
                        'url': current_url,
                        'status_code': 'Loop',
                        'type': 'Redirect Loop Detected'
                    })
                    error = f"Redirect loop detected at {current_url}"
                    break

                visited_urls.add(current_url)

                # Check if it's a market:// URL or App Store URL and convert it
                if (current_url.startswith('market://') or
                    'itunes.apple.com' in current_url or
                    'apps.apple.com' in current_url or
                    current_url.startswith('itms-apps://') or
                    current_url.startswith('itms-appss://')):

                    old_url = current_url
                    current_url = self._convert_market_to_play_store(current_url)

                    # Only add to redirect chain if the URL actually changed
                    if old_url != current_url:
                        redirect_chain.append({
                            'url': old_url,
                            'status_code': 'N/A (Converted)',
                            'type': 'Store URL Conversion'
                        })
                        continue  # Skip to next iteration with the converted URL

                # Make the request with SSL verification enabled using the global session
                try:
                    response = requests_session.get(
                        current_url,
                        headers=headers,
                        allow_redirects=False,  # Handle redirects manually to track them
                        timeout=15
                    )
                except requests.exceptions.SSLError:
                    # If SSL verification fails, try again without verification
                    print(f"SSL verification failed for {current_url}, retrying without verification")
                    response = requests.get(
                        current_url,
                        headers=headers,
                        allow_redirects=False,
                        timeout=15,
                        verify=False
                    )

                # Add this URL to the redirect chain
                redirect_chain.append({
                    'url': current_url,
                    'status_code': response.status_code,
                    'type': 'HTTP Request'
                })

                # Check if we got a redirect
                if 300 <= response.status_code < 400:
                    if 'Location' in response.headers:
                        next_url = response.headers['Location']

                        # Handle relative URLs
                        if next_url.startswith('/'):
                            parsed_url = urlparse(current_url)
                            next_url = f"{parsed_url.scheme}://{parsed_url.netloc}{next_url}"

                        current_url = next_url
                        redirect_count += 1
                    else:
                        # Redirect status but no Location header
                        error = f"Received redirect status {response.status_code} but no Location header"
                        break
                else:
                    # Not an HTTP redirect, check for JavaScript redirects or other special cases

                    # Check for JavaScript redirects in the response content
                    content_type = response.headers.get('Content-Type', '').lower()
                    if 'text/html' in content_type and response.content:
                        # Look for common JavaScript redirect patterns
                        js_redirect_url = self._extract_js_redirect(response.text, current_url)

                        if js_redirect_url:
                            redirect_chain.append({
                                'url': current_url,
                                'status_code': 'JS Redirect',
                                'type': 'JavaScript Redirect'
                            })
                            current_url = js_redirect_url
                            redirect_count += 1
                            continue

                        # Check for meta refresh redirects
                        meta_redirect_url = self._extract_meta_refresh(response.text, current_url)
                        if meta_redirect_url:
                            redirect_chain.append({
                                'url': current_url,
                                'status_code': 'Meta Refresh',
                                'type': 'Meta Refresh Redirect'
                            })
                            current_url = meta_redirect_url
                            redirect_count += 1
                            continue

                        # Check for special cases like tz_check pages
                        if 'tz_check' in current_url or 'timezone' in current_url:
                            tz_redirect_url = self._handle_tz_check_page(response.text, current_url)
                            if tz_redirect_url:
                                redirect_chain.append({
                                    'url': current_url,
                                    'status_code': 'TZ Check',
                                    'type': 'Timezone Check Redirect'
                                })
                                current_url = tz_redirect_url
                                redirect_count += 1
                                continue

                    # Check if we've reached a market:// URL in the response content
                    if response.text and 'market://' in response.text:
                        market_url = self._extract_market_url(response.text)
                        if market_url:
                            redirect_chain.append({
                                'url': current_url,
                                'status_code': 'Market URL Found',
                                'type': 'Market URL in Content'
                            })
                            current_url = market_url
                            redirect_count += 1
                            continue

                    # No more redirects found, we're done
                    break

            # Check if we hit the max redirects
            if redirect_count >= max_redirects:
                error = f"Exceeded maximum number of redirects ({max_redirects})"

        except requests.exceptions.RequestException as e:
            error = f"Request error: {str(e)}"
            # Add the error to the chain
            redirect_chain.append({
                'url': current_url,
                'status_code': 'Error',
                'type': f'Error: {str(e)}'
            })
        except Exception as e:
            error = f"Unexpected error: {str(e)}"
            # Add the error to the chain
            redirect_chain.append({
                'url': current_url,
                'status_code': 'Error',
                'type': f'Unexpected Error: {str(e)}'
            })

        # Check if we found a market URL in the chain
        market_url = self._find_market_url_in_chain(redirect_chain)
        if market_url and not current_url.startswith('market://'):
            redirect_chain.append({
                'url': market_url,
                'status_code': 'Found in Chain',
                'type': 'Market URL from Chain'
            })
            current_url = market_url

        # Return the final URL, redirect chain, and any error
        return {
            'final_url': current_url,
            'redirect_chain': redirect_chain,
            'error': error
        }

    def _extract_js_redirect(self, html_content, base_url):
        """Extract JavaScript redirect URL from HTML content"""
        if not html_content:
            return None

        # Common JavaScript redirect patterns
        js_redirect_patterns = [
            # window.location patterns
            r'window\.location(?:\.href)?\s*=\s*[\'"]([^\'"]*)[\'"]\s*;',
            r'window\.location\.replace\([\'"]([^\'"]*)[\'"]\)',
            # document.location patterns
            r'document\.location(?:\.href)?\s*=\s*[\'"]([^\'"]*)[\'"]\s*;',
            r'document\.location\.replace\([\'"]([^\'"]*)[\'"]\)',
            # setTimeout redirects
            r'setTimeout\([\'"]window\.location(?:\.href)?\s*=\s*[\'"]([^\'"]*)[\'"]\s*[\'"]',
            r'setTimeout\([\'"]document\.location(?:\.href)?\s*=\s*[\'"]([^\'"]*)[\'"]\s*[\'"]',
            # Encoded redirects
            r'window\.location(?:\.href)?\s*=\s*(?:atob|decodeURIComponent)\([\'"]([^\'"]*)[\'"]',
            # Redirect functions
            r'function\s+redirect\(\)\s*{[^}]*window\.location(?:\.href)?\s*=\s*[\'"]([^\'"]*)[\'"]\s*;',
            # URL in data attributes
            r'data-redirect-url=[\'"]([^\'"]*)[\'"]\s*',
            # Redirect URL in variable
            r'var\s+(?:url|redirectUrl|targetUrl)\s*=\s*[\'"]([^\'"]*)[\'"]\s*;'
        ]

        for pattern in js_redirect_patterns:
            match = re.search(pattern, html_content, re.IGNORECASE)
            if match:
                redirect_url = match.group(1)

                # Handle relative URLs
                if redirect_url.startswith('/'):
                    parsed_url = urlparse(base_url)
                    redirect_url = f"{parsed_url.scheme}://{parsed_url.netloc}{redirect_url}"
                elif not redirect_url.startswith(('http://', 'https://', 'market://')):
                    # Handle relative URLs without leading slash
                    parsed_url = urlparse(base_url)
                    base_path = os.path.dirname(parsed_url.path)
                    redirect_url = f"{parsed_url.scheme}://{parsed_url.netloc}{base_path}/{redirect_url}"

                return redirect_url

        # Check for redirect_url in query parameters (common in tracking pages)
        parsed_url = urlparse(base_url)
        query_params = parse_qs(parsed_url.query)
        redirect_param_names = ['redirect_url', 'redirectUrl', 'url', 'target', 'dest', 'destination']

        for param_name in redirect_param_names:
            if param_name in query_params and query_params[param_name][0]:
                redirect_url = query_params[param_name][0]

                # URL might be encoded
                try:
                    redirect_url = html.unescape(redirect_url)
                    # Try URL decoding if it looks encoded
                    if '%' in redirect_url:
                        from urllib.parse import unquote
                        redirect_url = unquote(redirect_url)
                except:
                    pass

                # Handle relative URLs
                if redirect_url.startswith('/'):
                    parsed_url = urlparse(base_url)
                    redirect_url = f"{parsed_url.scheme}://{parsed_url.netloc}{redirect_url}"

                return redirect_url

        return None

    def _extract_meta_refresh(self, html_content, base_url):
        """Extract meta refresh redirect URL from HTML content"""
        if not html_content:
            return None

        # Look for meta refresh tags
        meta_refresh_pattern = r'<meta[^>]*http-equiv=[\'"]refresh[\'"][^>]*content=[\'"](\d+;\s*url=([^\'"]*))[\'"]\s*/?>'
        match = re.search(meta_refresh_pattern, html_content, re.IGNORECASE)

        if match:
            redirect_url = match.group(2)

            # Handle relative URLs
            if redirect_url.startswith('/'):
                parsed_url = urlparse(base_url)
                redirect_url = f"{parsed_url.scheme}://{parsed_url.netloc}{redirect_url}"
            elif not redirect_url.startswith(('http://', 'https://', 'market://')):
                # Handle relative URLs without leading slash
                parsed_url = urlparse(base_url)
                base_path = os.path.dirname(parsed_url.path)
                redirect_url = f"{parsed_url.scheme}://{parsed_url.netloc}{base_path}/{redirect_url}"

            return redirect_url

        return None

    def _handle_tz_check_page(self, html_content, base_url):
        """Handle timezone check pages that are common in offer walls"""
        if not html_content:
            return None

        # Extract the redirect URL from the page
        # First check for redirect_url parameter
        parsed_url = urlparse(base_url)
        query_params = parse_qs(parsed_url.query)

        if 'redirect_url' in query_params and query_params['redirect_url'][0]:
            redirect_url = query_params['redirect_url'][0]

            # URL might be encoded
            try:
                redirect_url = html.unescape(redirect_url)
                # Try URL decoding if it looks encoded
                if '%' in redirect_url:
                    from urllib.parse import unquote
                    redirect_url = unquote(redirect_url)
            except:
                pass

            return redirect_url

        # Look for the redirect URL in the HTML
        redirect_patterns = [
            # Common patterns in tz_check pages
            r'var\s+redirectUrl\s*=\s*[\'"]([^\'"]*)[\'"]\s*;',
            r'window\.location(?:\.href)?\s*=\s*[\'"]([^\'"]*)[\'"]\s*;',
            r'<a[^>]*href=[\'"]([^\'"]*)[\'"][^>]*id=[\'"]redirect-link[\'"]',
            r'<form[^>]*action=[\'"]([^\'"]*)[\'"][^>]*id=[\'"]redirect-form[\'"]'
        ]

        for pattern in redirect_patterns:
            match = re.search(pattern, html_content, re.IGNORECASE)
            if match:
                redirect_url = match.group(1)

                # Handle relative URLs
                if redirect_url.startswith('/'):
                    parsed_url = urlparse(base_url)
                    redirect_url = f"{parsed_url.scheme}://{parsed_url.netloc}{redirect_url}"

                return redirect_url

        # If we can't find a redirect URL, try to construct one with timezone info
        # This is a common pattern in tz_check pages
        if 'tz_check' in base_url or 'timezone' in base_url:
            # Add timezone parameter to the URL
            import time
            tz_offset = time.strftime('%z')

            # If there's a click_id parameter, preserve it
            click_id = None
            if 'click_id' in query_params and query_params['click_id'][0]:
                click_id = query_params['click_id'][0]

            # If there's a redirect_url parameter but it was empty or we couldn't decode it,
            # try again with more aggressive decoding
            if 'redirect_url' in query_params:
                try:
                    redirect_url = query_params['redirect_url'][0]
                    # Double decode if needed
                    if '%25' in redirect_url:
                        from urllib.parse import unquote
                        redirect_url = unquote(unquote(redirect_url))
                    return redirect_url
                except:
                    pass

            # If we still don't have a redirect URL, try to construct one from the base URL
            # by removing the tz_check part and adding the timezone parameter
            base_path = parsed_url.path.replace('/tz_check', '')
            new_url = f"{parsed_url.scheme}://{parsed_url.netloc}{base_path}"

            # Add parameters
            params = {'tz': tz_offset}
            if click_id:
                params['click_id'] = click_id

            # Add any other parameters from the original URL except redirect_url
            for key, value in query_params.items():
                if key not in ['redirect_url', 'tz', 'click_id'] and value[0]:
                    params[key] = value[0]

            # Construct the new URL
            if params:
                param_str = '&'.join([f"{k}={v}" for k, v in params.items()])
                new_url = f"{new_url}?{param_str}"

            return new_url

        return None

    def _extract_market_url(self, html_content):
        """Extract market:// URL from HTML content"""
        if not html_content:
            return None

        # Look for market:// URLs
        market_url_pattern = r'market://[^\s\'"<>]+'
        match = re.search(market_url_pattern, html_content)

        if match:
            return match.group(0)

        # Look for Play Store URLs that could be converted to market:// URLs
        play_store_pattern = r'https?://play\.google\.com/store/apps/details\?[^\s\'"<>]+'
        match = re.search(play_store_pattern, html_content)

        if match:
            play_store_url = match.group(0)
            # Convert to market:// URL
            parsed_url = urlparse(play_store_url)
            query_params = parse_qs(parsed_url.query)

            if 'id' in query_params and query_params['id'][0]:
                package_id = query_params['id'][0]
                return f"market://details?id={package_id}"

        return None

    def _find_market_url_in_chain(self, redirect_chain):
        """Find a market:// URL in the redirect chain"""
        if not redirect_chain:
            return None

        # Look for market:// URLs in the chain
        for redirect in reversed(redirect_chain):  # Start from the end (most recent)
            url = redirect.get('url', '')
            if url.startswith('market://'):
                return url

        # If no market:// URL found, look for Play Store URLs that could be converted
        for redirect in reversed(redirect_chain):
            url = redirect.get('url', '')
            if 'play.google.com/store/apps/details' in url:
                parsed_url = urlparse(url)
                query_params = parse_qs(parsed_url.query)

                if 'id' in query_params and query_params['id'][0]:
                    package_id = query_params['id'][0]

                    # Check if there's a referrer parameter in the Play Store URL
                    if 'referrer' in query_params and query_params['referrer'][0]:
                        referrer = query_params['referrer'][0]
                        return f"market://details?id={package_id}&referrer={referrer}"
                    else:
                        return f"market://details?id={package_id}"

        return None

    def _convert_market_to_play_store(self, market_url):
        """Convert a market:// URL to a https://play.google.com URL or handle App Store URLs.
        Enhanced to handle market://launch URLs and other special cases."""
        # Debug log
        print(f"Converting URL: {market_url}")

        # Handle iOS App Store URLs with special protocols
        if market_url.startswith('itms-apps://') or market_url.startswith('itms-appss://'):
            # Convert itms-apps:// or itms-appss:// to https://
            fixed_url = market_url.replace('itms-apps://', 'https://')
            fixed_url = fixed_url.replace('itms-appss://', 'https://')

            # Fix any escaped characters in the URL
            if '\\u003D' in fixed_url:
                fixed_url = fixed_url.replace('\\u003D', '=')

            print(f"Converted iOS special protocol URL to: {fixed_url}")
            return fixed_url

        # Check if this is an App Store URL
        if 'itunes.apple.com' in market_url or 'apps.apple.com' in market_url:
            # Already a standard App Store URL, just return it
            return market_url

        # Parse the market URL
        parsed = urlparse(market_url)

        # Get the query parameters
        query_params = parse_qs(parsed.query)

        # Extract the package name or ID
        path = parsed.path

        # Debug log
        print(f"Parsed path: {path}")
        print(f"Query params: {query_params}")

        # Handle different market URL formats
        if path.startswith('/details'):
            # Already in the right format, just change the scheme and host
            # Make sure to preserve all query parameters
            return f"https://play.google.com{path}?{parsed.query}"
        elif path.startswith('/launch'):
            # Handle market://launch URLs (common in adjust redirects)
            # These URLs typically have the package ID in the 'id' parameter
            if 'id' in query_params and query_params['id'][0]:
                package_name = query_params['id'][0]
                # Create a copy of query_params without the 'id' parameter to avoid duplication
                new_params = {k: v for k, v in query_params.items() if k != 'id'}

                # Preserve the referrer parameter which is important for attribution
                if new_params:
                    new_query = urlencode(new_params, doseq=True)
                    return f"https://play.google.com/store/apps/details?id={package_name}&{new_query}"
                else:
                    return f"https://play.google.com/store/apps/details?id={package_name}"
            else:
                print("WARNING: market://launch URL without id parameter")
                return "https://play.google.com/store/apps/details"
        else:
            # For other formats, try to extract the package name from the path
            # and preserve all query parameters
            package_name = path.strip('/')

            # If we have an 'id' in the query parameters, use that instead
            if 'id' in query_params and query_params['id'][0]:
                package_name = query_params['id'][0]
                # Create a copy of query_params without the 'id' parameter
                new_params = {k: v for k, v in query_params.items() if k != 'id'}
            else:
                new_params = query_params

            # Rebuild the query string with all other parameters
            new_query = urlencode(new_params, doseq=True) if new_params else ""

            # If we have a package name and other query parameters
            if package_name and new_query:
                return f"https://play.google.com/store/apps/details?id={package_name}&{new_query}"
            # If we only have a package name
            elif package_name:
                return f"https://play.google.com/store/apps/details?id={package_name}"
            # If we don't have a package name but have other query parameters
            elif new_query:
                return f"https://play.google.com/store/apps/details?{new_query}"
            # Fallback with empty query
            else:
                print("WARNING: Could not extract package name from market URL")
                return "https://play.google.com/store/apps/details"

    def _process_redirect_result(self, result):
        """Process the result of the redirect following task"""
        final_url = result['final_url']
        redirect_chain = result['redirect_chain']
        error = result['error']

        # Store the final URL for later use in attribution requests
        self.final_redirect_url = final_url

        # Build the response text
        response_text = []

        # Add the original URL
        if redirect_chain:
            original_url = redirect_chain[0]['url']
            response_text.append(f"Original URL: {original_url}")

        # Add the redirect chain
        if len(redirect_chain) > 1:
            response_text.append("\nRedirect Chain:")
            for i, redirect in enumerate(redirect_chain):
                response_text.append(f"{i+1}. {redirect['url']}")
                response_text.append(f"   Status: {redirect['status_code']}")
                response_text.append(f"   Type: {redirect['type']}")

        # Add the final URL
        response_text.append(f"\nFinal URL: {final_url}")

        # Add any error
        if error:
            response_text.append(f"\nError: {error}")

        # Check if the final URL is a Play Store URL, App Store URL, or market URL
        is_market_url = final_url.startswith('market://')
        is_play_store_url = 'play.google.com' in final_url
        is_app_store_url = 'apps.apple.com' in final_url or 'itunes.apple.com' in final_url

        if is_market_url or is_play_store_url or is_app_store_url:
            # Extract app information
            app_info = self._extract_app_info(final_url)

            if app_info:
                response_text.append("\nApp Information:")
                for key, value in app_info.items():
                    response_text.append(f"{key}: {value}")

                # Extract package ID and referrer
                package_id = None
                referrer = None

                # Fix any escaped characters in the URL
                if '\\u003D' in final_url:
                    final_url = final_url.replace('\\u003D', '=')

                # Parse the URL to extract parameters
                parsed_url = urlparse(final_url)
                query_params = parse_qs(parsed_url.query)

                # Debug log
                print(f"Parsing final URL in _process_redirect_result: {final_url}")
                print(f"Query parameters: {query_params}")

                # Determine if this is an App Store URL
                is_app_store = ('apps.apple.com' in final_url or
                               'itunes.apple.com' in final_url or
                               final_url.startswith('itms-apps://') or
                               final_url.startswith('itms-appss://'))

                if is_app_store:
                    # For App Store URLs, extract the app ID
                    if 'id' in query_params and query_params['id'][0]:
                        package_id = query_params['id'][0]
                        response_text.append(f"\nApp ID: {package_id}")
                    else:
                        # Try to extract app ID from the path
                        path_parts = parsed_url.path.strip('/').split('/')
                        if len(path_parts) > 0:
                            potential_app_id = path_parts[-1]
                            if potential_app_id.isdigit():
                                package_id = potential_app_id
                                response_text.append(f"\nApp ID (from path): {package_id}")
                            # Sometimes the ID is in the form "id1354260888"
                            elif potential_app_id.startswith('id') and potential_app_id[2:].isdigit():
                                package_id = potential_app_id[2:]  # Remove the "id" prefix
                                response_text.append(f"\nApp ID (from path): {package_id}")
                            else:
                                response_text.append("\nError: Could not find App ID in URL")
                                package_id = None
                        else:
                            response_text.append("\nError: Could not find App ID in URL")
                            package_id = None
                else:
                    # For Play Store URLs, extract the package ID
                    if 'id' in query_params and query_params['id'][0]:
                        package_id = query_params['id'][0]
                        response_text.append(f"\nPackage ID: {package_id}")
                    else:
                        # Try to extract package ID from the path if it's not in the query parameters
                        path_parts = parsed_url.path.strip('/').split('/')
                        if len(path_parts) > 0 and path_parts[-1] != 'details':
                            # Last part of the path might be the package ID
                            potential_package_id = path_parts[-1]
                            response_text.append(f"\nPotential Package ID (from path): {potential_package_id}")
                            package_id = potential_package_id
                        else:
                            response_text.append("\nError: Could not find package ID in URL")
                            package_id = None

                # Extract referrer
                if 'referrer' in query_params and query_params['referrer'][0]:
                    referrer = query_params['referrer'][0]
                    response_text.append(f"\nReferrer: {referrer}")
                else:
                    response_text.append("\nWarning: No referrer found in URL")
                    referrer = None

                # Store the extracted data for attribution requests
                self.extracted_package_id = package_id
                self.extracted_referrer = referrer

                # Check if we have the app token in local data
                app_token = self._find_app_token_for_package(package_id)
                if app_token:
                    response_text.append(f"\nFound App Token: {app_token}")
                    self.extracted_app_token = app_token

                    # Add a button to make attribution requests
                    if not hasattr(self, 'attribution_btn') or self.attribution_btn is None:
                        self.attribution_btn = QPushButton("Make Attribution Requests")
                        self.attribution_btn.clicked.connect(self.make_attribution_requests)
                        # Find the action_layout to add the button
                        for child in self.findChildren(QHBoxLayout):
                            if self.follow_redirects_btn in [child.itemAt(i).widget() for i in range(child.count()) if child.itemAt(i).widget() is not None]:
                                child.addWidget(self.attribution_btn)
                                break
                    self.attribution_btn.setVisible(True)
                else:
                    response_text.append("\nWarning: Could not find app token for this package ID in local data.")
                    # Hide the attribution button if it exists
                    if hasattr(self, 'attribution_btn') and self.attribution_btn is not None:
                        self.attribution_btn.setVisible(False)
        else:
            # Hide the attribution button if it exists
            if hasattr(self, 'attribution_btn') and self.attribution_btn is not None:
                self.attribution_btn.setVisible(False)

        # Update the response text
        self.response_text.setText('\n'.join(response_text))

    def _handle_redirect_error(self, error_message):
        """Handle errors from the redirect worker"""
        try:
            if hasattr(self, 'response_text'):
                current_text = self.response_text.toPlainText()
                if current_text:
                    self.response_text.setText(f"{current_text}\n\nError following redirects: {error_message}")
                else:
                    self.response_text.setText(f"Error following redirects: {error_message}")

            # Log the error for debugging
            print(f"Redirect error: {error_message}")

            # Always ensure the button is enabled
            self._ensure_button_enabled()
        except Exception as e:
            import traceback
            print(f"Error in _handle_redirect_error: {str(e)}\n{traceback.format_exc()}")
            self._ensure_button_enabled()

    def _find_app_token_for_package(self, package_id):
        """Find the app token for a given package ID in the local data"""
        if not package_id:
            return None

        # Get all games data from the cache
        games_data = game_data_cache.get_data()

        # Look for a game with matching package name
        for _, game_data in games_data.items():
            if game_data.get('package_name') == package_id:
                return game_data.get('app_token')

        return None

    def _find_game_name_for_package(self, package_id):
        """Find the game name for a given package ID in the local data"""
        if not package_id:
            return None

        # Get all games data from the cache
        games_data = game_data_cache.get_data()

        # Look for a game with matching package name
        for game_name, game_data in games_data.items():
            if game_data.get('package_name') == package_id:
                return game_name

        return None

    def make_attribution_requests(self):
        """Make the three attribution requests in sequence using separate workers for each step"""
        if not self.extracted_package_id or not self.extracted_referrer or not self.extracted_app_token:
            self.response_text.setText("Error: Missing required data for attribution requests.")
            return

        # Disable the button while processing
        self.attribution_btn.setEnabled(False)

        # Clear previous response text and show initial message
        self.response_text.clear()
        self.response_text.setText("Starting attribution process...\n")

        # Force UI update
        QApplication.processEvents()

        # Store parameters for use in the sequence
        self.attribution_params = {
            'package_id': self.extracted_package_id,
            'referrer': self.extracted_referrer,
            'app_token': self.extracted_app_token,
            'device_id': self.gps_adid_input.text().strip(),
            'is_ios': self.current_platform == "iOS"
        }

        # Start the first step in the sequence
        self._start_sdk_click_request()

    def _start_sdk_click_request(self):
        """Start the SDK Click request as the first step in the attribution sequence"""
        # Clear previous response text
        self._update_response_text_safely("", append=False, html=False)

        # Update UI with a status message
        status_html = """
        <div style='margin-bottom:15px; padding:10px; border-left:4px solid #2196F3; background-color:rgba(33,33,33,0.3);'>
            <h3 style='color:#2196F3; margin:0;'>SDK Click Request</h3>
            <p style='color:#FFC107; font-weight:bold;'>Status: Starting...</p>
            <p>Preparing to make SDK Click request as the first step in the attribution process.</p>
        </div>
        """

        self._update_response_text_safely(
            message=status_html,
            append=True,
            html=True
        )

        QApplication.processEvents()

        # Check if attribution_params is properly set
        if not hasattr(self, 'attribution_params') or not self.attribution_params:
            error_msg = "Error: Attribution parameters are not properly set"
            print(error_msg)

            # Format the error with colors
            formatted_error = self._format_response_with_colors(
                status_code="Error",
                response_type="SDK Click Setup",
                response_text=error_msg
            )

            # Update the response text with HTML formatting
            self._update_response_text_safely(
                message=formatted_error,
                append=True,
                html=True
            )

            self._ensure_button_enabled()
            return

        # Set up a watchdog timer to recover from freezes
        self._setup_watchdog_timer()

        try:
            # Create a worker just for the SDK Click request
            worker = Worker(self._sdk_click_request_task, self.attribution_params)

            # Connect signals with proper error handling
            worker.result.connect(self._handle_sdk_click_result)
            worker.error.connect(lambda err: self._handle_request_error("SDK Click", err))
            worker.finished.connect(self._cancel_watchdog_timer)

            # Submit to thread pool
            worker.submit_to_pool()

            # Store reference to prevent garbage collection
            self.current_worker = worker

            print("SDK Click request worker submitted successfully")
        except Exception as e:
            import traceback
            error_msg = f"Error creating SDK Click worker: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)

            # Format the error with colors
            formatted_error = self._format_response_with_colors(
                status_code="Error",
                response_type="SDK Click Setup",
                response_text=error_msg
            )

            # Update the response text with HTML formatting
            self._update_response_text_safely(
                message=formatted_error,
                append=True,
                html=True
            )

            self._ensure_button_enabled()

    def _setup_watchdog_timer(self, timeout=15):
        """Set up a watchdog timer to recover from freezes using threading.Timer"""
        # Cancel any existing timer
        self._cancel_watchdog_timer()

        # Create a new timer using threading.Timer instead of QTimer
        self.watchdog_timer = threading.Timer(timeout, self._handle_watchdog_timeout)
        self.watchdog_timer.daemon = True
        self.watchdog_timer.start()

        # Also set up a more aggressive backup timer that will force-enable the button
        # This ensures the UI never stays frozen for more than 30 seconds
        self.emergency_timer = threading.Timer(30, self._handle_emergency_timeout)
        self.emergency_timer.daemon = True
        self.emergency_timer.start()

        print("Watchdog timer started")

    def _cancel_watchdog_timer(self):
        """Cancel the watchdog timer if it exists"""
        if hasattr(self, 'watchdog_timer') and self.watchdog_timer:
            self.watchdog_timer.cancel()
            self.watchdog_timer = None

        if hasattr(self, 'emergency_timer') and self.emergency_timer:
            self.emergency_timer.cancel()
            self.emergency_timer = None

        print("Watchdog timer cancelled")

    def _handle_watchdog_timeout(self):
        """Handle a watchdog timer timeout (request took too long)"""
        print("Watchdog timer triggered - request took too long")
        self.response_text.setText(f"{self.response_text.toPlainText()}\n\nWarning: Request is taking longer than expected. It may have frozen.")
        self._ensure_button_enabled()

    def _handle_emergency_timeout(self):
        """Emergency handler that will always re-enable the button no matter what"""
        print("EMERGENCY TIMEOUT: Forcing UI to unfreeze")
        self.response_text.setText(f"{self.response_text.toPlainText()}\n\nEMERGENCY RECOVERY: The application detected a potential freeze and has recovered.")

        # Force process events to ensure UI updates
        for _ in range(5):
            QApplication.processEvents()

        # Force enable the button
        if hasattr(self, 'attribution_button'):
            self.attribution_button.setEnabled(True)
        elif hasattr(self, 'attribution_btn'):
            self.attribution_btn.setEnabled(True)

        # Cancel any ongoing operations
        if hasattr(self, 'current_worker'):
            try:
                self.current_worker.cancel()
            except:
                pass

    def _process_referrer(self, referrer, offerwall_type=None):
        """
        Process the referrer parameter based on offerwall type to ensure it's properly formatted.

        Args:
            referrer (str): The original referrer string
            offerwall_type (str, optional): The type of offerwall if known

        Returns:
            str: Properly formatted referrer string
        """
        if not referrer:
            return ""

        # Log the original referrer for debugging
        print(f"Processing referrer: {referrer}")

        # First, try to decode the referrer to handle any URL encoding
        try:
            from urllib.parse import unquote
            decoded_referrer = unquote(referrer)
            print(f"Decoded referrer: {decoded_referrer}")
        except Exception as e:
            print(f"Error decoding referrer: {e}")
            decoded_referrer = referrer

        # Check for different Adjust referrer formats

        # Case 1: adjust_reftag format (common in Tyrads campaigns)
        if 'adjust_reftag' in referrer or 'adjust_reftag' in decoded_referrer:
            print("Found adjust_reftag format - preserving as is")
            # This format should be preserved exactly as is
            # Make sure it's not double-encoded
            try:
                if '%3D' in referrer:
                    from urllib.parse import unquote
                    referrer = unquote(referrer)
                    print(f"Decoded adjust_reftag referrer: {referrer}")
            except Exception as e:
                print(f"Error decoding adjust_reftag referrer: {e}")
            return referrer

        # Case 2: adjust_external_click_id format
        if 'adjust_external_click_id' in referrer or 'adjust_external_click_id' in decoded_referrer:
            print("Found adjust_external_click_id format - preserving as is")
            # This format should also be preserved exactly as is
            # Make sure it's not double-encoded
            try:
                if '%3D' in referrer:
                    from urllib.parse import unquote
                    referrer = unquote(referrer)
                    print(f"Decoded adjust_external_click_id referrer: {referrer}")
            except Exception as e:
                print(f"Error decoding adjust_external_click_id referrer: {e}")
            return referrer

        # Case 3: For Tyrads campaigns with utm_source=Tyrads
        if 'utm_source=Tyrads' in referrer or 'utm_source=Tyrads' in decoded_referrer or 'MT-GP-US-Tyrads' in referrer or 'MT-GP-US-Tyrads' in decoded_referrer:
            print("Found Tyrads campaign - preserving as is")
            # Make sure it's not double-encoded
            try:
                if '%3D' in referrer:
                    from urllib.parse import unquote
                    referrer = unquote(referrer)
                    print(f"Decoded Tyrads referrer: {referrer}")
            except Exception as e:
                print(f"Error decoding Tyrads referrer: {e}")
            return referrer

        # Case 4: For AyetStudios and similar offerwalls that use specific formats
        if offerwall_type == "AyetStudios":
            print("Processing AyetStudios format")
            # AyetStudios uses a specific format
            if not referrer.startswith('adjust_external_click_id='):
                # Add the prefix if it's missing
                return f"adjust_external_click_id={referrer}"

        # Case 5: For other offerwalls or unknown types with no adjust prefix
        if not any(prefix in referrer for prefix in ['adjust_', 'utm_']):
            print("No adjust prefix found - adding adjust_external_click_id prefix")
            # Add the prefix if it's missing
            return f"adjust_external_click_id={referrer}"

        # Default case: If none of the above conditions match, return the original referrer
        print("Using original referrer format")
        return referrer

    def _verify_app_token_for_package(self, app_token, package_id):
        """
        Verify that the app_token matches the package_id in our local data.

        Args:
            app_token (str): The app token to verify
            package_id (str): The package ID to check against

        Returns:
            bool: True if the app_token matches the package_id, False otherwise
        """
        if not app_token or not package_id:
            return False

        # Get all games data from the cache
        games_data = game_data_cache.get_data()

        # Look for a game with matching package name
        for game_name, game_data in games_data.items():
            if game_data.get('package_name') == package_id:
                stored_app_token = game_data.get('app_token')
                if stored_app_token == app_token:
                    print(f"Verified app_token {app_token} matches package_id {package_id} for game {game_name}")
                    return True
                else:
                    print(f"WARNING: app_token mismatch for {game_name}:")
                    print(f"  - Provided: {app_token}")
                    print(f"  - Expected: {stored_app_token}")
                    return False

        # If we didn't find a matching package_id
        print(f"WARNING: No game found with package_id {package_id}")
        return False

    def _sdk_click_request_task(self, params):
        """Task to make just the SDK Click request"""
        try:
            # Extract parameters
            package_id = params['package_id']
            referrer = params['referrer']
            app_token = params['app_token']
            device_id = params['device_id']
            is_ios = params['is_ios']

            # Verify app_token matches package_id
            print(f"Verifying app_token: {app_token} for package_id: {package_id}")
            if not self._verify_app_token_for_package(app_token, package_id):
                print(f"WARNING: app_token {app_token} may not match package_id {package_id}")

            # Make sure the referrer is properly processed
            if referrer:
                # Don't URL encode the referrer here - it should be passed as-is
                # The SDK Click request will handle the encoding
                print(f"Original referrer: {referrer}")

            # Process the referrer to ensure it's properly formatted
            processed_referrer = self._process_referrer(referrer)
            if processed_referrer != referrer:
                print(f"Referrer processed from: {referrer}")
                print(f"                     to: {processed_referrer}")
                referrer = processed_referrer

            # Log parameters with more detail
            print(f"SDK Click task with: package_id={package_id}, app_token={app_token}, device_id={device_id}")
            print(f"Using referrer: {referrer}")

            # Additional debug logging for the referrer
            try:
                from urllib.parse import unquote
                print(f"Decoded referrer for SDK Click: {unquote(referrer)}")

                # Check if the referrer contains campaign information
                if 'utm_campaign' in unquote(referrer):
                    print(f"Campaign information found in referrer: {unquote(referrer)}")
                elif 'adjust_reftag' in unquote(referrer):
                    print(f"Adjust reftag found in referrer: {unquote(referrer)}")
            except Exception as e:
                print(f"Error analyzing referrer: {e}")

            # Generate IDs
            android_uuid = str(uuid.uuid4()) if not is_ios else None
            google_app_set_id = str(uuid.uuid4()) if not is_ios else None
            ios_uuid = str(uuid.uuid4()) if is_ios else None

            # Get current timestamp
            from datetime import datetime
            now = datetime.now()
            current_time = now.strftime("%Y-%m-%dT%H:%M:%S") + ".{:03d}Z-0400".format(int(now.microsecond / 1000))

            # Set headers based on platform
            if is_ios:
                headers = {
                    'Accept-Encoding': 'gzip',
                    'Client-SDK': 'ios4.38.0',
                    'Connection': 'Keep-Alive',
                    'Host': 'app.adjust.com',
                    'User-Agent': 'CFNetwork/1220.1 Darwin/20.3.0',
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            else:
                headers = {
                    'Accept-Encoding': 'gzip',
                    'Client-SDK': 'unity4.38.0@android4.38.3',
                    'Connection': 'Keep-Alive',
                    'Host': 'app.adjust.com',
                    'User-Agent': 'Dalvik/2.1.0 (Linux; U; Android 9; SM-S9160 Build/PQ3A.190605.09201023)',
                    'Content-Type': 'application/x-www-form-urlencoded'
                }

            # Build payload based on platform
            if is_ios:
                payload = {
                    'country': 'US',
                    'app_version': '1.44.1',
                    'app_token': app_token,
                    'installed_at': current_time,
                    'device_type': 'tablet',
                    'language': 'en',
                    'idfa': device_id,
                    'connectivity_type': '4',
                    'click_time': current_time,
                    'cpu_type': 'arm64',
                    'install_version': '1.44.1',
                    'screen_size': 'large',
                    'subsession_count': '1',
                    'screen_density': 'high',
                    'session_count': '1',
                    'session_length': '0',
                    'created_at': current_time,
                    'device_manufacturer': 'apple',
                    'display_width': '1080',
                    'time_spent': '0',
                    'device_name': 'iPhone',
                    'needs_response_details': '1',
                    'updated_at': current_time,
                    'screen_format': 'long',
                    'click_time_server': current_time,
                    'os_version': '14.5',
                    'install_begin_time_server': current_time,
                    'callback_params': '{"context_install":"ae9c5476ee38459c88c639e1888ea065"}',
                    'ios_uuid': ios_uuid,
                    'environment': 'production',
                    'attribution_deeplink': '1',
                    'install_begin_time': current_time,
                    'display_height': '1920',
                    'bundle_id': package_id,
                    'os_name': 'ios',
                    'tracking_enabled': '1',
                    'sent_at': current_time
                }
            else:
                # Check if this is a Tyrads campaign
                is_tyrads_campaign = False
                if referrer and ('utm_source=Tyrads' in referrer or 'MT-GP-US-Tyrads' in referrer):
                    is_tyrads_campaign = True
                    print("Detected Tyrads campaign - adding special parameters for SDK Click")

                payload = {
                    'country': 'US',
                    'api_level': '28',
                    'event_buffering_enabled': '0',
                    'hardware_name': 'PQ3A.190605.09201023 release-keys',
                    'app_version': '1.44.1',
                    'app_token': app_token,
                    'installed_at': current_time,
                    'device_type': 'tablet',
                    'language': 'en',
                    'gps_adid': device_id,
                    'source': 'install_referrer' if not is_tyrads_campaign else 'tyrads',
                    'connectivity_type': '4',
                    'mcc': '310',
                    'os_build': 'PQ3A.190605.09201023',
                    'click_time': current_time,
                    'cpu_type': 'x86_64',
                    'install_version': '1.44.1',
                    'screen_size': 'large',
                    'gps_adid_src': 'service',
                    'subsession_count': '1',
                    'screen_density': 'high',
                    'session_count': '1',
                    'ui_mode': '1',
                    'gps_adid_attempt': '1',
                    'session_length': '0',
                    'created_at': current_time,
                    'referrer_api': 'google',
                    'device_manufacturer': 'samsung',
                    'display_width': '720',
                    'time_spent': '0',
                    'google_app_set_id': google_app_set_id,
                    'device_name': 'SM-S9160',
                    'needs_response_details': '1',
                    'updated_at': current_time,
                    'screen_format': 'long',
                    'mnc': '005',
                    'click_time_server': current_time,
                    'os_version': '9',
                    'google_play_instant': '0',
                    'install_begin_time_server': current_time,
                    'callback_params': '{"context_install":"ae9c5476ee38459c88c639e1888ea065"}',
                    'android_uuid': android_uuid,
                    'referrer': referrer,
                    'environment': 'production',
                    'attribution_deeplink': '1',
                    'install_begin_time': current_time,
                    'display_height': '1280',
                    'package_name': package_id,
                    'os_name': 'android',
                    'tracking_enabled': '1',
                    'sent_at': current_time
                }

                # Add additional parameters for Tyrads campaigns
                if is_tyrads_campaign:
                    # Try to extract campaign information
                    try:
                        from urllib.parse import parse_qs, unquote
                        decoded_referrer = unquote(referrer)

                        # Add campaign parameter if available
                        if 'utm_campaign=' in decoded_referrer:
                            params_dict = parse_qs(decoded_referrer)
                            if 'utm_campaign' in params_dict:
                                campaign = params_dict['utm_campaign'][0]
                                print(f"Adding campaign parameter to SDK Click: {campaign}")
                                payload['campaign'] = campaign

                        # If MT-GP-US-Tyrads is in the referrer, add it as campaign
                        if 'MT-GP-US-Tyrads' in decoded_referrer and 'campaign' not in payload:
                            print("Adding MT-GP-US-Tyrads as campaign parameter to SDK Click")
                            payload['campaign'] = 'MT-GP-US-Tyrads'

                        # If adjust_reftag is in the referrer, extract it
                        if 'adjust_reftag=' in decoded_referrer:
                            reftag_value = decoded_referrer.split('adjust_reftag=')[1].split('&')[0]
                            print(f"Adding reftag parameter to SDK Click: {reftag_value}")
                            payload['reftag'] = reftag_value
                    except Exception as e:
                        print(f"Error adding Tyrads parameters to SDK Click: {e}")

            # Log the full payload for debugging
            print(f"SDK Click payload: {payload}")

            # Make the request - REMOVED PROXY SETTINGS FOR ATTRIBUTION
            url = "https://app.adjust.com/sdk_click"
            print(f"Making SDK Click request to {url}")

            # Create a new session for this request only
            session = requests.Session()
            response = session.post(
                url,
                headers=headers,
                data=payload,
                timeout=10  # Increased timeout for better reliability
            )

            # Parse the response if it's JSON
            response_data = None
            try:
                if response.headers.get('Content-Type', '').startswith('application/json'):
                    response_data = response.json()
                    print(f"SDK Click JSON response: {response_data}")
            except Exception as json_err:
                print(f"Error parsing JSON response: {json_err}")

            # Return the result
            return {
                'success': True,
                'status_code': response.status_code,
                'response_text': response.text,
                'response_data': response_data,
                'url': url,
                'params': params,  # Pass through the parameters for the next step
                'current_time': current_time,
                'android_uuid': android_uuid,
                'google_app_set_id': google_app_set_id,
                'ios_uuid': ios_uuid
            }

        except Exception as e:
            import traceback
            error_msg = f"SDK Click request error: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'params': params  # Pass through the parameters for error handling
            }

    def _handle_sdk_click_result(self, result):
        """Handle the result of the SDK Click request and start the Session request"""
        try:
            # Cancel the watchdog timer
            self._cancel_watchdog_timer()

            # Update UI with result
            if result.get('success', False):
                status_code_val = result.get('status_code', 'Unknown') # Renamed to avoid conflict
                response_text_content = result.get('response_text', 'No response body') # Renamed
                response_data = result.get('response_data', None)

                # Format the response with colors
                formatted_response = self._format_response_with_colors(
                    status_code=status_code_val,
                    response_type="SDK Click",
                    response_text=response_text_content,
                    response_data=response_data
                )

                # Update the response text with HTML formatting, appending to existing content
                self._update_response_text_safely(
                    message=formatted_response,
                    append=True,
                    html=True
                )

                # Store the result for later
                self.sdk_click_result = result

                # Add a small delay between requests to simulate natural behavior
                # This helps avoid triggering fraud detection systems
                import random
                import time
                delay = random.uniform(2.0, 3.5)  # Increased delay for better simulation
                print(f"Adding delay of {delay:.2f} seconds before Session request...")
                time.sleep(delay)

                # Start the next step
                self._start_session_request(result)
            else:
                error = result.get('error', 'Unknown error')

                # Format the error response with colors
                formatted_error = self._format_response_with_colors(
                    status_code="Error",
                    response_type="SDK Click",
                    response_text=f"Request failed: {error}"
                )

                # Update the response text with HTML formatting
                self._update_response_text_safely(
                    message=formatted_error,
                    append=True,
                    html=True
                )

                self._ensure_button_enabled()
        except Exception as e:
            import traceback
            error_msg = f"Error handling SDK Click result: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)

            # Format the error with colors
            formatted_error = self._format_response_with_colors(
                status_code="Error",
                response_type="SDK Click Handler",
                response_text=error_msg
            )

            # Update the response text with HTML formatting
            self._update_response_text_safely(
                message=formatted_error,
                append=True,
                html=True
            )

            self._ensure_button_enabled()

    def _start_session_request(self, sdk_click_result):
        """Start the Session request as the second step in the attribution sequence"""
        # Update UI with a status message
        status_html = """
        <div style='margin-bottom:15px; padding:10px; border-left:4px solid #2196F3; background-color:rgba(33,33,33,0.3);'>
            <h3 style='color:#2196F3; margin:0;'>Session Request</h3>
            <p style='color:#FFC107; font-weight:bold;'>Status: Starting...</p>
            <p>Preparing to make Session request as the second step in the attribution process.</p>
        </div>
        """

        self._update_response_text_safely(
            message=status_html,
            append=True,
            html=True
        )

        QApplication.processEvents()

        # Set up a watchdog timer to recover from freezes
        self._setup_watchdog_timer()

        try:
            # Create a worker just for the Session request
            worker = Worker(self._session_request_task, sdk_click_result)

            # Connect signals with proper error handling
            worker.result.connect(self._handle_session_result)
            worker.error.connect(lambda err: self._handle_request_error("Session", err))
            worker.finished.connect(self._cancel_watchdog_timer)

            # Submit to thread pool
            worker.submit_to_pool()

            # Store reference to prevent garbage collection
            self.current_worker = worker

            print("Session request worker submitted successfully")
        except Exception as e:
            import traceback
            error_msg = f"Error creating Session worker: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)

            # Format the error with colors
            formatted_error = self._format_response_with_colors(
                status_code="Error",
                response_type="Session Setup",
                response_text=error_msg
            )

            # Update the response text with HTML formatting
            self._update_response_text_safely(
                message=formatted_error,
                append=True,
                html=True
            )

            self._ensure_button_enabled()

    def _session_request_task(self, sdk_click_result):
        """Task to make just the Session request"""
        try:
            # Extract parameters
            params = sdk_click_result.get('params', {})
            package_id = params.get('package_id', '')
            app_token = params.get('app_token', '')
            device_id = params.get('device_id', '')
            is_ios = params.get('is_ios', False)

            # Get values from SDK Click result
            current_time = sdk_click_result.get('current_time', '')
            android_uuid = sdk_click_result.get('android_uuid', None)
            google_app_set_id = sdk_click_result.get('google_app_set_id', None)
            ios_uuid = sdk_click_result.get('ios_uuid', None)

            # Log parameters
            print(f"Session task with: package_id={package_id}, app_token={app_token}, device_id={device_id}")

            # Set headers based on platform
            if is_ios:
                headers = {
                    'Accept-Encoding': 'gzip',
                    'Client-SDK': 'ios4.38.0',
                    'Connection': 'Keep-Alive',
                    'Host': 'app.adjust.com',
                    'User-Agent': 'CFNetwork/1220.1 Darwin/20.3.0',
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            else:
                headers = {
                    'Accept-Encoding': 'gzip',
                    'Client-SDK': 'unity4.38.0@android4.38.3',
                    'Connection': 'Keep-Alive',
                    'Host': 'app.adjust.com',
                    'User-Agent': 'Dalvik/2.1.0 (Linux; U; Android 9; SM-S9160 Build/PQ3A.190605.09201023)',
                    'Content-Type': 'application/x-www-form-urlencoded'
                }

            # Build payload based on platform
            if is_ios:
                payload = {
                    'country': 'US',
                    'app_version': '1.44.1',
                    'app_token': app_token,
                    'installed_at': current_time,
                    'created_at': current_time,
                    'device_type': 'tablet',
                    'language': 'en',
                    'idfa': device_id,
                    'connectivity_type': '4',
                    'device_manufacturer': 'apple',
                    'display_width': '1080',
                    'device_name': 'iPhone',
                    'needs_response_details': '1',
                    'updated_at': current_time,
                    'cpu_type': 'arm64',
                    'screen_size': 'large',
                    'screen_format': 'long',
                    'os_version': '14.5',
                    'ios_uuid': ios_uuid,
                    'environment': 'production',
                    'screen_density': 'high',
                    'attribution_deeplink': '1',
                    'session_count': '1',
                    'display_height': '1920',
                    'bundle_id': package_id,
                    'os_name': 'ios',
                    'tracking_enabled': '1',
                    'sent_at': current_time
                }
            else:
                payload = {
                    'gps_adid_attempt': '1',
                    'country': 'US',
                    'api_level': '28',
                    'event_buffering_enabled': '0',
                    'hardware_name': 'PQ3A.190605.09201023 release-keys',
                    'app_version': '1.44.1',
                    'app_token': app_token,
                    'installed_at': current_time,
                    'created_at': current_time,
                    'device_type': 'tablet',
                    'language': 'en',
                    'gps_adid': device_id,
                    'connectivity_type': '4',
                    'mcc': '310',
                    'device_manufacturer': 'samsung',
                    'display_width': '720',
                    'google_app_set_id': google_app_set_id,
                    'device_name': 'SM-S9160',
                    'needs_response_details': '1',
                    'os_build': 'PQ3A.190605.09201023',
                    'updated_at': current_time,
                    'cpu_type': 'x86_64',
                    'screen_size': 'large',
                    'screen_format': 'long',
                    'gps_adid_src': 'service',
                    'mnc': '005',
                    'os_version': '9',
                    'android_uuid': android_uuid,
                    'environment': 'production',
                    'screen_density': 'high',
                    'attribution_deeplink': '1',
                    'session_count': '1',
                    'display_height': '1280',
                    'package_name': package_id,
                    'os_name': 'android',
                    'ui_mode': '1',
                    'tracking_enabled': '1',
                    'sent_at': current_time
                }

            # Log the full payload for debugging
            print(f"Session payload: {payload}")

            # Make the request - REMOVED PROXY SETTINGS FOR ATTRIBUTION
            url = "https://app.adjust.com/session"
            print(f"Making Session request to {url}")

            # Create a new session for this request only
            session = requests.Session()
            response = session.post(
                url,
                headers=headers,
                data=payload,
                timeout=10  # Increased timeout for better reliability
            )

            # Parse the response if it's JSON
            response_data = None
            try:
                if response.headers.get('Content-Type', '').startswith('application/json'):
                    response_data = response.json()
                    print(f"Session JSON response: {response_data}")
            except Exception as json_err:
                print(f"Error parsing JSON response: {json_err}")

            # Return the result
            return {
                'success': True,
                'status_code': response.status_code,
                'response_text': response.text,
                'response_data': response_data,
                'url': url,
                'sdk_click_result': sdk_click_result  # Pass through the SDK Click result for the next step
            }

        except Exception as e:
            import traceback
            error_msg = f"Session request error: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'sdk_click_result': sdk_click_result  # Pass through the SDK Click result for error handling
            }

    def _handle_session_result(self, result):
        """Handle the result of the Session request and start the Attribution request"""
        try:
            # Cancel the watchdog timer
            self._cancel_watchdog_timer()

            # Update UI with result
            if result.get('success', False):
                status_code_val = result.get('status_code', 'Unknown') # Renamed
                response_text_content = result.get('response_text', 'No response body') # Renamed
                response_data = result.get('response_data', None)

                # Format the response with colors
                formatted_response = self._format_response_with_colors(
                    status_code=status_code_val,
                    response_type="Session",
                    response_text=response_text_content,
                    response_data=response_data
                )

                # Update the response text with HTML formatting, appending to existing content
                self._update_response_text_safely(
                    message=formatted_response,
                    append=True,
                    html=True
                )

                # Store the result for later
                self.session_result = result

                # Add a small delay between requests to simulate natural behavior
                # This helps avoid triggering fraud detection systems
                import random
                import time
                delay = random.uniform(2.0, 3.5)  # Increased delay for better simulation
                print(f"Adding delay of {delay:.2f} seconds before Attribution request...")
                time.sleep(delay)

                # Start the next step
                self._start_attribution_request(result)
            else:
                error = result.get('error', 'Unknown error')

                # Format the error response with colors
                formatted_error = self._format_response_with_colors(
                    status_code="Error",
                    response_type="Session",
                    response_text=f"Request failed: {error}"
                )

                # Update the response text with HTML formatting
                self._update_response_text_safely(
                    message=formatted_error,
                    append=True,
                    html=True
                )

                self._ensure_button_enabled()
        except Exception as e:
            import traceback
            error_msg = f"Error handling Session result: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)

            # Format the error with colors
            formatted_error = self._format_response_with_colors(
                status_code="Error",
                response_type="Session Handler",
                response_text=error_msg
            )

            # Update the response text with HTML formatting
            self._update_response_text_safely(
                message=formatted_error,
                append=True,
                html=True
            )

            self._ensure_button_enabled()

    def _start_attribution_request(self, session_result):
        """Start the Attribution request as the third step in the attribution sequence"""
        # Update UI with a status message
        status_html = """
        <div style='margin-bottom:15px; padding:10px; border-left:4px solid #2196F3; background-color:rgba(33,33,33,0.3);'>
            <h3 style='color:#2196F3; margin:0;'>Attribution Request</h3>
            <p style='color:#FFC107; font-weight:bold;'>Status: Starting...</p>
            <p>Preparing to make attribution request to complete the attribution process.</p>
        </div>
        """

        self._update_response_text_safely(
            message=status_html,
            append=True,
            html=True
        )

        QApplication.processEvents()

        # Set up a watchdog timer to recover from freezes
        self._setup_watchdog_timer()

        try:
            # Create a worker just for the Attribution request
            worker = Worker(self._attribution_request_task, session_result)

            # Connect signals with proper error handling
            worker.result.connect(self._handle_attribution_request_result)
            worker.error.connect(lambda err: self._handle_request_error("Attribution", err))
            worker.finished.connect(self._cancel_watchdog_timer)

            # Submit to thread pool
            worker.submit_to_pool()

            # Store reference to prevent garbage collection
            self.current_worker = worker

            print("Attribution request worker submitted successfully")
        except Exception as e:
            import traceback
            error_msg = f"Error creating Attribution worker: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)

            # Format the error with colors
            formatted_error = self._format_response_with_colors(
                status_code="Error",
                response_type="Attribution Setup",
                response_text=error_msg
            )

            # Update the response text with HTML formatting
            self._update_response_text_safely(
                message=formatted_error,
                append=True,
                html=True
            )

            self._ensure_button_enabled()

    def _attribution_request_task(self, session_result):
        """Task to make just the Attribution request"""
        try:
            # Extract parameters from the SDK Click result
            sdk_click_result = session_result.get('sdk_click_result', {})
            params = sdk_click_result.get('params', {})
            package_id = params.get('package_id', '')
            app_token = params.get('app_token', '')
            device_id = params.get('device_id', '')
            is_ios = params.get('is_ios', False)

            # Get values from SDK Click result
            current_time = sdk_click_result.get('current_time', '')
            android_uuid = sdk_click_result.get('android_uuid', None)
            google_app_set_id = sdk_click_result.get('google_app_set_id', None)
            ios_uuid = sdk_click_result.get('ios_uuid', None)

            # Log parameters
            print(f"Attribution task with: package_id={package_id}, app_token={app_token}, device_id={device_id}")

            # Set headers based on platform
            if is_ios:
                headers = {
                    'Accept-Encoding': 'gzip',
                    'Client-SDK': 'ios4.38.0',
                    'Connection': 'Keep-Alive',
                    'Host': 'app.adjust.com',
                    'User-Agent': 'CFNetwork/1220.1 Darwin/20.3.0'
                }
            else:
                headers = {
                    'Accept-Encoding': 'gzip',
                    'Client-SDK': 'unity4.38.0@android4.38.3',
                    'Connection': 'Keep-Alive',
                    'Host': 'app.adjust.com',
                    'User-Agent': 'Dalvik/2.1.0 (Linux; U; Android 9; SM-S9160 Build/PQ3A.190605.09201023)'
                }

            # Build parameters based on platform
            if is_ios:
                request_params = {
                    'initiated_by': 'sdk',
                    'app_version': '1.44.1',
                    'app_token': app_token,
                    'os_version': '14.5',
                    'created_at': current_time,
                    'device_type': 'tablet',
                    'idfa': device_id,
                    'ios_uuid': ios_uuid,
                    'device_name': 'iPhone',
                    'environment': 'production',
                    'needs_response_details': '1',
                    'attribution_deeplink': '1',
                    'bundle_id': package_id,
                    'os_name': 'ios',
                    'tracking_enabled': '1',
                    'sent_at': current_time
                }
            else:
                request_params = {
                    'initiated_by': 'sdk',
                    'gps_adid_attempt': '1',
                    'api_level': '28',
                    'event_buffering_enabled': '0',
                    'app_version': '1.44.1',
                    'app_token': app_token,
                    'os_version': '9',
                    'created_at': current_time,
                    'device_type': 'tablet',
                    'gps_adid': device_id,
                    'android_uuid': android_uuid,
                    'google_app_set_id': google_app_set_id,
                    'device_name': 'SM-S9160',
                    'environment': 'production',
                    'needs_response_details': '1',
                    'attribution_deeplink': '1',
                    'package_name': package_id,
                    'os_name': 'android',
                    'ui_mode': '1',
                    'gps_adid_src': 'service',
                    'tracking_enabled': '1',
                    'sent_at': current_time
                }

            # Log the full parameters for debugging
            print(f"Initial attribution request parameters: {request_params}")

            # Check if we have a referrer in the SDK Click parameters
            sdk_params = sdk_click_result.get('params', {})
            sdk_referrer = sdk_params.get('referrer', '')

            # Always include the referrer in the attribution request
            if sdk_referrer:
                print(f"Adding referrer from SDK Click to attribution request: {sdk_referrer}")
                # Add the referrer to the attribution request
                request_params['referrer'] = sdk_referrer

                # For adjust_reftag format, also add the raw_referrer parameter
                if 'adjust_reftag' in sdk_referrer:
                    print("Adding raw_referrer parameter for adjust_reftag format")
                    request_params['raw_referrer'] = sdk_referrer

                    # Extract the reftag value for additional parameters
                    try:
                        from urllib.parse import parse_qs, unquote
                        decoded_referrer = unquote(sdk_referrer)
                        if 'adjust_reftag=' in decoded_referrer:
                            reftag_value = decoded_referrer.split('adjust_reftag=')[1].split('&')[0]
                            print(f"Extracted reftag value: {reftag_value}")
                            request_params['reftag'] = reftag_value
                    except Exception as e:
                        print(f"Error extracting reftag value: {e}")

                # For Tyrads campaigns, add additional parameters that might help with attribution
                if 'utm_source=Tyrads' in sdk_referrer or 'MT-GP-US-Tyrads' in sdk_referrer:
                    print("Detected Tyrads campaign, adding source parameter")
                    request_params['source'] = 'tyrads'

                    # Add campaign parameter if available
                    if 'utm_campaign=' in sdk_referrer:
                        try:
                            from urllib.parse import parse_qs, unquote
                            decoded_referrer = unquote(sdk_referrer)
                            params_dict = parse_qs(decoded_referrer)
                            if 'utm_campaign' in params_dict:
                                campaign = params_dict['utm_campaign'][0]
                                print(f"Adding campaign parameter: {campaign}")
                                request_params['campaign'] = campaign
                        except Exception as e:
                            print(f"Error extracting campaign parameter: {e}")

                    # If MT-GP-US-Tyrads is in the referrer, add it as campaign
                    if 'MT-GP-US-Tyrads' in sdk_referrer and 'campaign' not in request_params:
                        print("Adding MT-GP-US-Tyrads as campaign parameter")
                        request_params['campaign'] = 'MT-GP-US-Tyrads'

            # Make sure we have all necessary parameters for attribution
            # Add tracker parameter if we have a Tyrads campaign
            if 'source' in request_params and request_params['source'] == 'tyrads':
                if 'tracker' not in request_params:
                    print("Adding tracker parameter for Tyrads campaign")
                    request_params['tracker'] = 'tyrads'

            # Add tracker_id parameter if we have a reftag
            if 'reftag' in request_params and 'tracker_id' not in request_params:
                print(f"Adding tracker_id parameter with reftag value: {request_params['reftag']}")
                request_params['tracker_id'] = request_params['reftag']

            # Make the request - REMOVED PROXY SETTINGS FOR ATTRIBUTION
            url = "https://app.adjust.com/attribution"
            print(f"Making Attribution request to {url}")
            print(f"Final attribution request parameters: {request_params}")

            # Create a new session for this request only
            session = requests.Session()

            # Add a small delay before making the request to simulate natural behavior
            import time
            import random
            delay = random.uniform(1.0, 2.0)
            print(f"Adding delay of {delay:.2f} seconds before Attribution request...")
            time.sleep(delay)

            response = session.get(
                url,
                headers=headers,
                params=request_params,
                timeout=15  # Increased timeout for better reliability
            )

            # Parse the response if it's JSON
            response_data = None
            attribution_info = None
            try:
                if response.headers.get('Content-Type', '').startswith('application/json'):
                    response_data = response.json()
                    print(f"Attribution JSON response: {response_data}")

                    # Extract attribution information if available
                    if 'attribution' in response_data:
                        attribution_info = response_data['attribution']
                        print(f"Attribution info found: {attribution_info}")

                        # Check if we got an organic attribution when we expected a campaign attribution
                        if attribution_info.get('network') == 'Organic' and sdk_referrer and ('adjust_reftag' in sdk_referrer or 'utm_campaign' in sdk_referrer or 'MT-GP-US-Tyrads' in sdk_referrer):
                            print(f"WARNING: Got Organic attribution despite having campaign referrer: {sdk_referrer}")
                            print("This may indicate an issue with the referrer parameter or the attribution window")

                            # Add detailed debug information
                            print("=== ATTRIBUTION DEBUG INFORMATION ===")
                            print(f"Package ID: {package_id}")
                            print(f"App Token: {app_token}")
                            print(f"Device ID: {device_id}")
                            print(f"Referrer: {sdk_referrer}")

                            # Try to parse the referrer to see what might be wrong
                            try:
                                from urllib.parse import parse_qs, unquote
                                decoded_referrer = unquote(sdk_referrer)
                                print(f"Decoded referrer: {decoded_referrer}")

                                # Check for specific patterns
                                if 'adjust_reftag=' in decoded_referrer:
                                    reftag_value = decoded_referrer.split('adjust_reftag=')[1].split('&')[0]
                                    print(f"Reftag value: {reftag_value}")

                                # Parse all parameters
                                if '=' in decoded_referrer:
                                    params_dict = parse_qs(decoded_referrer)
                                    print(f"Parsed referrer parameters: {params_dict}")
                            except Exception as e:
                                print(f"Error analyzing referrer: {e}")

                            print("=== END DEBUG INFORMATION ===")

                        # Log more details about the attribution
                        if 'network' in attribution_info:
                            print(f"Attribution network: {attribution_info['network']}")
                        if 'campaign' in attribution_info:
                            print(f"Attribution campaign: {attribution_info['campaign']}")
                        if 'adgroup' in attribution_info:
                            print(f"Attribution adgroup: {attribution_info['adgroup']}")
                        if 'creative' in attribution_info:
                            print(f"Attribution creative: {attribution_info['creative']}")

                    # Check for specific error messages
                    if 'error' in response_data:
                        print(f"Attribution error: {response_data['error']}")

                    # Check for message field
                    if 'message' in response_data:
                        print(f"Attribution message: {response_data['message']}")
            except Exception as json_err:
                print(f"Error parsing JSON response: {json_err}")

            # Return the result
            return {
                'success': True,
                'status_code': response.status_code,
                'response_text': response.text,
                'response_data': response_data,
                'attribution_info': attribution_info,
                'url': response.url,
                'session_result': session_result,  # Pass through the Session result
                'sdk_click_result': sdk_click_result  # Pass through the SDK Click result
            }

        except Exception as e:
            import traceback
            error_msg = f"Attribution request error: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'session_result': session_result,
                'sdk_click_result': sdk_click_result
            }

    def _handle_attribution_request_result(self, result):
        """Handle the result of the Attribution request and complete the process"""
        try:
            # Cancel the watchdog timer
            self._cancel_watchdog_timer()

            # Update UI with result immediately
            if result.get('success', False):
                status_code = result.get('status_code', 'Unknown')
                response_text = result.get('response_text', 'No response body')
                response_data = result.get('response_data', None)
                attribution_info = result.get('attribution_info', None)

                # Format the response with colors
                formatted_response = self._format_response_with_colors(
                    status_code=status_code,
                    response_type="Attribution",
                    response_text=response_text,
                    response_data=response_data,
                    attribution_info=attribution_info
                )

                # Add detailed attribution information if available
                if attribution_info:
                    attribution_html = """
                    <div style='margin-top:10px; padding:10px; border-radius:8px; background-color:rgba(33,150,243,0.15); border:1px solid #2196F3;'>
                        <h4 style='color:#2196F3; margin:0;'>Attribution Details</h4>
                        <table style='width:100%; margin-top:8px; border-collapse:collapse;'>
                    """

                    # Add network information
                    network = attribution_info.get('network', 'Unknown')
                    network_color = "#4CAF50" if network != "Organic" else "#FF9800"
                    attribution_html += f"""
                        <tr>
                            <td style='padding:5px; font-weight:bold; color:#E0E0E0;'>Network:</td>
                            <td style='padding:5px; color:{network_color};'>{network}</td>
                        </tr>
                    """

                    # Add campaign information if available
                    if 'campaign' in attribution_info:
                        attribution_html += f"""
                        <tr>
                            <td style='padding:5px; font-weight:bold; color:#E0E0E0;'>Campaign:</td>
                            <td style='padding:5px; color:#4CAF50;'>{attribution_info['campaign']}</td>
                        </tr>
                        """

                    # Add adgroup information if available
                    if 'adgroup' in attribution_info:
                        attribution_html += f"""
                        <tr>
                            <td style='padding:5px; font-weight:bold; color:#E0E0E0;'>Ad Group:</td>
                            <td style='padding:5px; color:#4CAF50;'>{attribution_info['adgroup']}</td>
                        </tr>
                        """

                    # Add creative information if available
                    if 'creative' in attribution_info:
                        attribution_html += f"""
                        <tr>
                            <td style='padding:5px; font-weight:bold; color:#E0E0E0;'>Creative:</td>
                            <td style='padding:5px; color:#4CAF50;'>{attribution_info['creative']}</td>
                        </tr>
                        """

                    # Add tracker information
                    if 'tracker_name' in attribution_info:
                        attribution_html += f"""
                        <tr>
                            <td style='padding:5px; font-weight:bold; color:#E0E0E0;'>Tracker:</td>
                            <td style='padding:5px; color:#E0E0E0;'>{attribution_info['tracker_name']}</td>
                        </tr>
                        """

                    # Close the table and div
                    attribution_html += """
                        </table>
                    </div>
                    """

                    # Append the attribution details to the formatted response
                    formatted_response += attribution_html

                # Update the response text with HTML formatting, appending to existing content
                self._update_response_text_safely(
                    message=formatted_response,
                    append=True,
                    html=True
                )

                # Add a summary section at the end
                summary_html = """
                <div style='margin-top:20px; padding:15px; border-radius:10px; background-color:rgba(33,150,243,0.2); border:2px solid #2196F3;'>
                    <h3 style='color:#2196F3; margin:0;'>Attribution Process Complete</h3>
                    <p style='color:#4CAF50; font-weight:bold; margin-top:10px;'>All steps completed successfully!</p>
                </div>
                """

                self._update_response_text_safely(
                    message=summary_html,
                    append=True,
                    html=True
                )

                # Store the result for later
                self.attribution_result = result

                # Re-enable the button IMMEDIATELY
                self._ensure_button_enabled()

                # Process the result in a separate thread to avoid freezing
                # Use a simple thread instead of Worker to avoid potential issues
                processing_thread = threading.Thread(
                    target=lambda: self._process_attribution_result(result)
                )
                processing_thread.daemon = True
                processing_thread.start()

                # We don't need to wait for the result processing to complete
                # The UI will remain responsive
            else:
                error = result.get('error', 'Unknown error')

                # Format the error response with colors
                formatted_error = self._format_response_with_colors(
                    status_code="Error",
                    response_type="Attribution",
                    response_text=f"Request failed: {error}"
                )

                # Update the response text with HTML formatting
                self._update_response_text_safely(
                    message=formatted_error,
                    append=True,
                    html=True
                )

                # Add a summary section at the end
                summary_html = """
                <div style='margin-top:20px; padding:15px; border-radius:10px; background-color:rgba(244,67,54,0.2); border:2px solid #F44336;'>
                    <h3 style='color:#F44336; margin:0;'>Attribution Process Failed</h3>
                    <p style='color:#F44336; font-weight:bold; margin-top:10px;'>Attribution request failed. Please check the error details above.</p>
                </div>
                """

                self._update_response_text_safely(
                    message=summary_html,
                    append=True,
                    html=True
                )

                self._ensure_button_enabled()
        except Exception as e:
            import traceback
            error_msg = f"Error handling Attribution result: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)

            # Format the error with colors
            formatted_error = self._format_response_with_colors(
                status_code="Error",
                response_type="Attribution Handler",
                response_text=error_msg
            )

            # Update the response text with HTML formatting
            self._update_response_text_safely(
                message=formatted_error,
                append=True,
                html=True
            )

            # Add a summary section at the end
            summary_html = """
            <div style='margin-top:20px; padding:15px; border-radius:10px; background-color:rgba(244,67,54,0.2); border:2px solid #F44336;'>
                <h3 style='color:#F44336; margin:0;'>Attribution Process Failed</h3>
                <p style='color:#F44336; font-weight:bold; margin-top:10px;'>An unexpected error occurred. Please check the error details above.</p>
            </div>
            """

            self._update_response_text_safely(
                message=summary_html,
                append=True,
                html=True
            )

            self._ensure_button_enabled()

    def _process_attribution_result(self, attribution_result):
        """Process the attribution result in a separate thread to avoid freezing the UI"""
        try:
            # Get the original parameters
            sdk_click_result = attribution_result.get('sdk_click_result', {})
            params = sdk_click_result.get('params', {})
            package_id = params.get('package_id', '')
            device_id = params.get('device_id', '')

            # Find the game name for this package ID
            game_name = self._find_game_name_for_package(package_id)

            # Create a detailed summary in HTML format
            summary_html = """
            <div style='margin-top:20px; padding:15px; border-radius:10px; background-color:rgba(33,150,243,0.2); border:2px solid #2196F3;'>
                <h3 style='color:#2196F3; margin:0;'>Attribution Process Details</h3>
            """

            if game_name:
                # Get the device ID type name
                device_id_type = "IDFA" if params.get('is_ios', False) else "GPS ADID"

                # Add to history
                try:
                    history_data_cache.add_attribution(
                        game_name=game_name,
                        package_id=package_id,
                        gps_adid=device_id  # We still use gps_adid field name for compatibility
                    )
                    # Add to summary
                    summary_html += f"""
                    <p style='color:#4CAF50; font-weight:bold; margin-top:10px;'>
                        Attribution recorded in history for <span style='color:#FF9800;'>{game_name}</span>
                        with {device_id_type}: <span style='color:#FF9800;'>{device_id}</span>
                    </p>
                    """
                except Exception as history_error:
                    summary_html += f"""
                    <p style='color:#F44336; font-weight:bold; margin-top:10px;'>
                        Warning: Failed to record in history: {str(history_error)}
                    </p>
                    """
            else:
                summary_html += f"""
                <p style='color:#F44336; font-weight:bold; margin-top:10px;'>
                    Warning: Could not find game name for package ID: <span style='color:#FF9800;'>{package_id}</span>
                </p>
                """

            # Add attribution details table
            summary_html += """
            <h4 style='color:#2196F3; margin-top:15px;'>Attribution Process Summary</h4>
            <table style='width:100%; border-collapse:collapse; margin-top:10px;'>
                <tr style='background-color:rgba(0,0,0,0.2);'>
                    <th style='padding:8px; text-align:left; border:1px solid #555;'>Step</th>
                    <th style='padding:8px; text-align:left; border:1px solid #555;'>Status</th>
                    <th style='padding:8px; text-align:left; border:1px solid #555;'>URL</th>
                </tr>
            """

            # SDK Click
            sdk_status = sdk_click_result.get('status_code', 'Unknown')
            sdk_url = sdk_click_result.get('url', 'Unknown URL')

            # Determine status color
            sdk_status_color = "#4CAF50" if 200 <= sdk_status < 300 else "#F44336" if isinstance(sdk_status, int) else "#FFC107"

            summary_html += f"""
            <tr>
                <td style='padding:8px; text-align:left; border:1px solid #555;'>SDK Click</td>
                <td style='padding:8px; text-align:left; border:1px solid #555; color:{sdk_status_color};'>{sdk_status}</td>
                <td style='padding:8px; text-align:left; border:1px solid #555; word-break:break-all;'>{sdk_url}</td>
            </tr>
            """

            # Session
            session_result = attribution_result.get('session_result', {})
            session_status = session_result.get('status_code', 'Unknown')
            session_url = session_result.get('url', 'Unknown URL')

            # Determine status color
            session_status_color = "#4CAF50" if 200 <= session_status < 300 else "#F44336" if isinstance(session_status, int) else "#FFC107"

            summary_html += f"""
            <tr>
                <td style='padding:8px; text-align:left; border:1px solid #555;'>Session</td>
                <td style='padding:8px; text-align:left; border:1px solid #555; color:{session_status_color};'>{session_status}</td>
                <td style='padding:8px; text-align:left; border:1px solid #555; word-break:break-all;'>{session_url}</td>
            </tr>
            """

            # Attribution
            attribution_status = attribution_result.get('status_code', 'Unknown')
            attribution_url = attribution_result.get('url', 'Unknown URL')

            # Determine status color
            attribution_status_color = "#4CAF50" if 200 <= attribution_status < 300 else "#F44336" if isinstance(attribution_status, int) else "#FFC107"

            summary_html += f"""
            <tr>
                <td style='padding:8px; text-align:left; border:1px solid #555;'>Attribution</td>
                <td style='padding:8px; text-align:left; border:1px solid #555; color:{attribution_status_color};'>{attribution_status}</td>
                <td style='padding:8px; text-align:left; border:1px solid #555; word-break:break-all;'>{attribution_url}</td>
            </tr>
            """

            # Close the table and div
            summary_html += """
            </table>
            </div>
            """

            # Update the UI with the complete summary
            # Store the update for main thread processing
            self._pending_response_update = (summary_html, True, True)

        except Exception as e:
            import traceback
            error_msg = f"Error processing attribution result: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)

            # Format the error with colors for the UI
            error_html = f"""
            <div style='margin-top:20px; padding:15px; border-radius:10px; background-color:rgba(244,67,54,0.2); border:2px solid #F44336;'>
                <h3 style='color:#F44336; margin:0;'>Error Processing Attribution Result</h3>
                <pre style='white-space:pre-wrap; word-break:break-word; background-color:rgba(0,0,0,0.2); padding:10px; border-radius:5px; margin-top:10px; color:#F44336;'>{error_msg}</pre>
            </div>
            """

            # Update UI with error message
            # Store the update for main thread processing
            self._pending_response_update = (error_html, True, True)

    def _update_response_text_safe(self, text, append=False, html=False):
        """Update the response text safely from any thread - Legacy method

        This is kept for compatibility with old code. New code should use _update_response_text_safely.
        """
        try:
            # Just delegate to the new method
            self._update_response_text_safely(text, append, html)
        except Exception as e:
            print(f"Error in legacy response text update: {e}")

    def _complete_attribution_process(self, attribution_result):
        """Complete the attribution process and update the UI - LEGACY METHOD
        This is kept for compatibility with old code but should not be used directly.
        New code should use _process_attribution_result instead."""
        try:
            # This is now just a wrapper for the new method
            self._process_attribution_result(attribution_result)
        except Exception as e:
            import traceback
            error_msg = f"Error in legacy attribution process: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)
            if hasattr(self, 'response_text'):
                self.response_text.setText(f"Error: {error_msg}")
                QApplication.processEvents()
        finally:
            # Always re-enable the button
            self._ensure_button_enabled()

    def _handle_request_error(self, request_type, error_message):
        """Handle errors from any request worker"""
        try:
            # Cancel the watchdog timer
            self._cancel_watchdog_timer()

            error_msg = f"{request_type} request error: {error_message}"
            print(error_msg)

            # Format the error with colors
            formatted_error = self._format_response_with_colors(
                status_code="Error",
                response_type=request_type, # Simplified: e.g., "SDK Click", "Session"
                response_text=error_msg
            )

            # Update the response text with HTML formatting
            self._update_response_text_safely(
                message=formatted_error,
                append=True,
                html=True
            )

            # Add a summary section at the end
            summary_html = f"""
            <div style='margin-top:20px; padding:15px; border-radius:10px; background-color:rgba(244,67,54,0.2); border:2px solid #F44336;'>
                <h3 style='color:#F44336; margin:0;'>{request_type} Request Failed</h3>
                <p style='color:#F44336; font-weight:bold; margin-top:10px;'>An error occurred during the {request_type.lower()} request. Please check the error details above.</p>
            </div>
            """

            self._update_response_text_safely(
                message=summary_html,
                append=True,
                html=True
            )

            QApplication.processEvents()
        except Exception as e:
            print(f"Error in _handle_request_error: {e}")
        finally:
            # Always re-enable the button
            self._ensure_button_enabled()

    # Old attribution methods have been replaced with a step-by-step approach

    def _handle_attribution_result(self, result):
        """Handle the result of the attribution requests - this is called by the old attribution system
        and is kept for compatibility. The new system uses _handle_attribution_request_result."""
        try:
            # This method is now just a wrapper for the new system
            print(f"Old attribution result handler called - using compatibility mode with result: {type(result)}")

            # If we have a valid result, try to process it
            if isinstance(result, dict) and result.get('success', False):
                # Process the result in a separate thread to avoid freezing
                processing_thread = threading.Thread(
                    target=lambda: self._process_attribution_result(result)
                )
                processing_thread.daemon = True
                processing_thread.start()
                print("Started processing thread for legacy attribution result")
            else:
                print(f"Legacy attribution result was not valid: {result}")

            # Re-enable the button
            self._ensure_button_enabled()

        except Exception as e:
            # Catch-all for any unexpected errors
            import traceback
            error_msg = f"Unexpected error handling attribution result: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)

            if hasattr(self, 'response_text'):
                self.response_text.setText(f"Error handling attribution result: {str(e)}")

        finally:
            # Always ensure the button is enabled
            self._ensure_button_enabled()

    def _find_game_name_for_package(self, package_id):
        """Find the game name for a given package ID in the local data"""
        if not package_id:
            return None

        # Get all games data from the cache
        games_data = game_data_cache.get_data()

        # Look for a game with matching package name
        for game_name, game_data in games_data.items():
            if game_data.get('package_name') == package_id:
                return game_name

        return None

    def _handle_attribution_error(self, error_message):
        """Handle errors from the attribution worker - this is called by the old attribution system
        and is kept for compatibility. The new system uses _handle_request_error."""
        try:
            # Log the error for debugging
            print(f"Old attribution error handler called - using compatibility mode")
            print(f"Attribution error: {error_message}")

            # Re-enable the button
            self._ensure_button_enabled()
        except Exception as e:
            import traceback
            print(f"Error in _handle_attribution_error: {str(e)}\n{traceback.format_exc()}")
        finally:
            # Always ensure the button is enabled
            self._ensure_button_enabled()

    def _extract_app_info(self, store_url):
        """Extract app information from a Play Store or App Store URL"""
        app_info = {}

        # Determine if this is an App Store URL
        is_app_store = ('apps.apple.com' in store_url or
                        'itunes.apple.com' in store_url or
                        store_url.startswith('itms-apps://') or
                        store_url.startswith('itms-appss://'))

        try:
            # Fix special iOS URL protocols if needed
            if store_url.startswith('itms-apps://') or store_url.startswith('itms-appss://'):
                fixed_url = store_url.replace('itms-apps://', 'https://')
                fixed_url = fixed_url.replace('itms-appss://', 'https://')

                # Fix any escaped characters in the URL
                if '\\u003D' in fixed_url:
                    fixed_url = fixed_url.replace('\\u003D', '=')

                store_url = fixed_url
                print(f"Using fixed URL for app info extraction: {store_url}")

            # Parse the URL
            parsed_url = urlparse(store_url)
            query_params = parse_qs(parsed_url.query)

            if is_app_store:
                # For App Store URLs, the ID is usually in the path or in the 'id' parameter
                if 'id' in query_params:
                    app_id = query_params['id'][0]
                    app_info['App ID'] = app_id
                else:
                    # Try to extract from path
                    path_parts = parsed_url.path.strip('/').split('/')
                    if len(path_parts) > 0:
                        potential_app_id = path_parts[-1]
                        if potential_app_id.isdigit():
                            app_info['App ID'] = potential_app_id
                        # Sometimes the ID is in the form "id1354260888"
                        elif potential_app_id.startswith('id') and potential_app_id[2:].isdigit():
                            app_info['App ID'] = potential_app_id[2:]  # Remove the "id" prefix

                # Set user agent for iOS
                user_agent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1 Mobile/15E148 Safari/604.1'
            else:
                # For Play Store URLs, extract the package name
                if 'id' in query_params:
                    package_name = query_params['id'][0]
                    app_info['Package Name'] = package_name

                # Set user agent for Android
                user_agent = 'Mozilla/5.0 (Linux; Android 10; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.162 Mobile Safari/537.36'

            # Try to fetch the store page to extract more information
            headers = {'User-Agent': user_agent}
            print(f"Fetching app info from: {store_url}")
            response = requests.get(store_url, headers=headers, timeout=15, verify=True)

            if response.status_code == 200:
                html_content = response.text

                # Extract app name
                app_name_match = re.search(r'<title>(.*?)</title>', html_content)
                if app_name_match:
                    app_name = app_name_match.group(1).strip()
                    # Remove " - Apps on Google Play" suffix if present
                    app_name = re.sub(r'\s*-\s*Apps on Google Play$', '', app_name)
                    # Remove " on the App Store" suffix if present
                    app_name = re.sub(r'\s*on the App Store$', '', app_name)
                    app_info['App Name'] = app_name

                # Extract app icon URL
                icon_url_patterns = [
                    r'<img.*?itemprop="image".*?src="(.*?)"',
                    r'<meta property="og:image" content="(.*?)">',
                    r'<img.*?class="T75of.*?src="(.*?)"',
                    # iOS App Store specific patterns
                    r'<meta name="apple-itunes-app".*?content="(.*?)"',
                    r'<link rel="apple-touch-icon".*?href="(.*?)"'
                ]

                for pattern in icon_url_patterns:
                    icon_url_match = re.search(pattern, html_content, re.IGNORECASE)
                    if icon_url_match:
                        icon_url = icon_url_match.group(1)
                        if not icon_url.startswith('http'):
                            icon_url = f"{parsed_url.scheme}://{parsed_url.netloc}{icon_url}"
                        app_info['Icon URL'] = icon_url
                        break

                # Extract developer name
                dev_name_patterns = [
                    # Google Play pattern
                    r'<a.*?href="/store/apps/developer\?id=.*?">(.*?)</a>',
                    # iOS App Store pattern
                    r'<h2 class="product-header__identity app-header__identity">.*?<a.*?>(.*?)</a>'
                ]

                for pattern in dev_name_patterns:
                    dev_name_match = re.search(pattern, html_content, re.IGNORECASE | re.DOTALL)
                    if dev_name_match:
                        app_info['Developer'] = html.unescape(dev_name_match.group(1).strip())
                        break

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            app_info['Error'] = f"Failed to extract app info: {str(e)}"
            print(f"App info extraction error: {error_details}")

        return app_info

    # QR code scanner methods removed

    def on_platform_selected(self, text):
        """Called when a platform is selected in the platform_spinner"""
        self.current_platform = text
        print(f"AttributionScreen: Platform selected: {self.current_platform}")

        # Update the label and placeholder text based on platform
        if text == "iOS":
            self.device_id_label.setText("IDFA:")
            self.gps_adid_input.setPlaceholderText("Enter IDFA")
        else:  # Android
            self.device_id_label.setText("GPS ADID:")
            self.gps_adid_input.setPlaceholderText("Enter GPS ADID")

    def generate_random_gps_adid(self):
        """Generate a random device ID (UUID) and set it in the input field"""
        random_id = str(uuid.uuid4())
        self.gps_adid_input.setText(random_id)

        # Get the appropriate device ID type name based on platform
        device_id_type = "IDFA" if self.current_platform == "iOS" else "GPS ADID"

        # Show a message in the response text area
        current_text = self.response_text.toPlainText()
        if current_text:
            self.response_text.setText(f"{current_text}\nGenerated random {device_id_type}: {random_id}")
        else:
            self.response_text.setText(f"Generated random {device_id_type}: {random_id}")

    def set_background(self):
        """Called when the screen becomes active to set a new background."""
        if hasattr(self, 'response_log_container') and isinstance(self.response_log_container, (RotatingBackgroundBox, ImageBackgroundBox)):
            self.response_log_container.set_random_background()


# TokenStorageScreen implementation
class TokenStorageScreen(BaseScreenWidget):
    screen_name_attr = "token_storage_screen"

    def __init__(self, parent_app=None, parent=None):
        super().__init__(parent)
        self.app = parent_app
        self.current_games_data = {}
        self.filtered_games = []
        self.current_filter = "All Events"
        self.current_sort = "Name (A-Z)"
        self.search_text = ""

        self._build_ui()

        # Connect to game_data_cache updates
        game_data_cache.data_updated.connect(self.refresh_data)

        # Initial data load
        self.refresh_data()

    def _build_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(dp(20), dp(20), dp(20), dp(20))
        main_layout.setSpacing(dp(15))

        # Title with action buttons
        title_layout = QHBoxLayout()

        title = QLabel("Token Storage")
        title.setObjectName("ScreenTitleLabel")
        title_layout.addWidget(title)

        title_layout.addStretch()

        refresh_btn = QPushButton("🔄 Refresh Data")
        refresh_btn.clicked.connect(lambda: self.refresh_data())
        title_layout.addWidget(refresh_btn)

        refresh_icons_btn = QPushButton("🖼️ Refresh Icons")
        refresh_icons_btn.clicked.connect(lambda: self.refresh_data(scrape_icons=True))
        refresh_icons_btn.setToolTip("Refresh data and scrape Play Store URLs for game icons")
        title_layout.addWidget(refresh_icons_btn)

        search_playstore_btn = QPushButton("🔍 Search Play Store")
        search_playstore_btn.clicked.connect(self.search_missing_games)
        search_playstore_btn.setToolTip("Search Play Store for games missing package names or details")
        title_layout.addWidget(search_playstore_btn)

        add_game_btn = QPushButton("➕ Add New Game")
        add_game_btn.clicked.connect(self.add_new_game)
        title_layout.addWidget(add_game_btn)

        main_layout.addLayout(title_layout)

        # Search and filter section
        filter_layout = QHBoxLayout()

        # Search box
        search_layout = QVBoxLayout()
        search_layout.addWidget(QLabel("Search Games"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search by name, package, token...")
        self.search_input.textChanged.connect(self.on_search_changed)
        search_layout.addWidget(self.search_input)
        filter_layout.addLayout(search_layout)

        # Event filter dropdown
        event_filter_layout = QVBoxLayout()
        event_filter_layout.addWidget(QLabel("Filter by Event"))
        self.event_filter = QComboBox()
        self.event_filter.addItem("All Events")
        self.event_filter.currentTextChanged.connect(self.on_filter_changed)
        event_filter_layout.addWidget(self.event_filter)
        filter_layout.addLayout(event_filter_layout)

        # Sort dropdown
        sort_layout = QVBoxLayout()
        sort_layout.addWidget(QLabel("Sort By"))
        self.sort_dropdown = QComboBox()
        self.sort_dropdown.addItems([
            "Name (A-Z)",
            "Name (Z-A)",
            "Most Events",
            "Fewest Events",
            "Recently Added",
            "Purchase Events",
            "Missing App Token"
        ])
        self.sort_dropdown.currentTextChanged.connect(self.on_sort_changed)
        sort_layout.addWidget(self.sort_dropdown)
        filter_layout.addLayout(sort_layout)

        main_layout.addLayout(filter_layout)

        # Stats cards
        stats_layout = QHBoxLayout()

        # Total Games card
        self.total_games_card = self._create_stat_card("Total Games", "0", "📱")
        stats_layout.addWidget(self.total_games_card)

        # Total Events card
        self.total_events_card = self._create_stat_card("Total Events", "0", "🎮")
        stats_layout.addWidget(self.total_events_card)

        # Avg Events per Game card
        self.avg_events_card = self._create_stat_card("Avg Events per Game", "0", "📊")
        stats_layout.addWidget(self.avg_events_card)

        # Last Updated card
        self.last_updated_card = self._create_stat_card("Last Updated", "Never", "🕒")
        stats_layout.addWidget(self.last_updated_card)

        main_layout.addLayout(stats_layout)

        # Games table - use our custom BackgroundTableWidget
        self.games_table = BackgroundTableWidget(0, 6)  # 0 rows, 6 columns
        self.games_table.setHorizontalHeaderLabels(["ICON", "GAME", "APP TOKEN", "STORE", "EVENTS", "ACTIONS"])

        # Configure column sizes for better fit and responsive resizing
        # Icon column - fixed but wider to properly display icons
        self.games_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        self.games_table.setColumnWidth(0, dp(80))  # Increased width for icon column

        # Game name column - give it more space with stretch
        self.games_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)

        # App token column - interactive to allow user resizing
        self.games_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.Interactive)
        self.games_table.setColumnWidth(2, dp(150))  # Increased width for better readability

        # Store column - fixed width but slightly wider
        self.games_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)
        self.games_table.setColumnWidth(3, dp(70))  # Adjusted for better button display

        # Events column - interactive to allow user resizing
        self.games_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeMode.Interactive)
        self.games_table.setColumnWidth(4, dp(180))  # Set initial width

        # Actions column - fixed width but wider for better button spacing
        self.games_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)
        self.games_table.setColumnWidth(5, dp(90))  # Increased width for better button spacing

        self.games_table.verticalHeader().setVisible(False)
        self.games_table.setShowGrid(False)
        self.games_table.setAlternatingRowColors(True)

        # Set row height to be slightly taller for better readability
        self.games_table.verticalHeader().setDefaultSectionSize(dp(80))

        # Style the table to match app's color scheme with improved readability
        self.games_table.setStyleSheet(f"""
            QTableWidget {{
                color: {COLOR_TEXT_LIGHT};
                border-radius: {dp(10)}px;
                border: 1px solid {QColor(COLOR_VISTA_BLUE).darker(150).name()};
                gridline-color: {COLOR_MIDNIGHT_GREEN};
                selection-background-color: {COLOR_BLUE_VIOLET};
                selection-color: white;
                font-size: {dp(16)}px;  /* Larger font for better readability */
                font-weight: bold;
            }}

            QTableWidget::item {{
                padding: {dp(10)}px;  /* Increased padding for better spacing */
                border-radius: {dp(5)}px;
                background-color: transparent;
            }}

            QTableWidget::item:selected {{
                background-color: {QColor(COLOR_BLUE_VIOLET).name()}aa; /* Semi-transparent */
            }}

            QHeaderView::section {{
                background-color: {QColor(COLOR_GUNMETAL_2).name()}dd; /* Semi-transparent */
                color: {COLOR_VISTA_BLUE};
                padding: {dp(8)}px;  /* Increased padding */
                border: none;
                border-bottom: 1px solid {COLOR_VISTA_BLUE};
                font-weight: bold;
                font-size: {dp(14)}px;  /* Larger header font */
            }}

            QTableWidget::item:alternate {{
                background-color: {QColor(COLOR_GUNMETAL).lighter(120).name()}55; /* Very transparent */
            }}

            /* Make text in cells ellide (show ... when too long) */
            QTableWidget::item {{
                text-overflow: ellipsis;
            }}
        """)

        main_layout.addWidget(self.games_table)

    def _create_stat_card(self, title, value, icon=""):
        # Use RotatingBackgroundBox instead of QFrame to match response logs
        card = RotatingBackgroundBox()
        card.setObjectName("StatCard")
        # The RotatingBackgroundBox already has styling, but we'll add some additional styles
        card.setStyleSheet(f"""
            QFrame#StatCard {{
                border-radius: {dp(10)}px;
                padding: {dp(10)}px;
                border: 1px solid {QColor(COLOR_VISTA_BLUE).darker(150).name()};
            }}
        """)

        layout = QVBoxLayout(card)

        # Icon and title in a horizontal layout
        header_layout = QHBoxLayout()
        icon_label = QLabel(icon)
        icon_label.setFont(QFont("Arial", dp(24)))
        header_layout.addWidget(icon_label)

        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", dp(22), QFont.Weight.ExtraBold))
        title_label.setStyleSheet("color: #8F9DF7;")
        header_layout.addWidget(title_label)
        header_layout.addStretch()

        layout.addLayout(header_layout)

        # Value
        value_label = QLabel(value)

        # Create a consistent object name by removing spaces and special characters
        # For "Avg Events per Game", create a specific name to match what we're looking for
        if title == "Avg Events per Game":
            value_label.setObjectName("AvgEventsperGameValue")
        else:
            clean_title = ''.join(c for c in title if c.isalnum())
            value_label.setObjectName(f"{clean_title}Value")

        # Print the object name for debugging
        print(f"Created label with name: {value_label.objectName()} for title: {title}")

        # Make the value text BIGGER and BOLDER
        value_label.setFont(QFont("Arial", dp(50), QFont.Weight.ExtraBold))
        value_label.setStyleSheet("color: white; font-weight: 900;")
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(value_label)

        return card

    def refresh_data(self, _=None, scrape_icons=False):
        """Refresh the data from the game_data_cache"""
        self.current_games_data = game_data_cache.get_data()

        # If scrape_icons is True, scrape Play Store URLs for icons
        if scrape_icons:
            self.scrape_playstore_icons()

        # Update the event filter dropdown with unique event names
        self.update_event_filter_options()

        # Update stats
        self.update_stats()

        # Apply current filters and sort
        self.apply_filters_and_sort()

        # Update the table
        self.update_games_table()

    def update_event_filter_options(self):
        """Update the event filter dropdown with unique event names"""
        self.event_filter.blockSignals(True)
        current_text = self.event_filter.currentText()

        self.event_filter.clear()
        self.event_filter.addItem("All Events")

        # Collect unique event names
        unique_events = set()
        for game_data in self.current_games_data.values():
            events = game_data.get("events", {})
            unique_events.update(events.keys())

        # Add sorted event names
        for event in sorted(unique_events):
            self.event_filter.addItem(event)

        # Restore previous selection if it exists
        index = self.event_filter.findText(current_text)
        if index >= 0:
            self.event_filter.setCurrentIndex(index)
        else:
            self.event_filter.setCurrentIndex(0)

        self.event_filter.blockSignals(False)

    def update_stats(self):
        """Update the statistics cards"""
        # Count total games
        total_games = len(self.current_games_data)

        # Count total events
        total_events = 0
        for game_data in self.current_games_data.values():
            total_events += len(game_data.get("events", {}))

        # Calculate average events per game
        avg_events = total_events / total_games if total_games > 0 else 0

        # Print debug info to identify the actual label names
        print("Debug - Stats card labels:")
        for card in [self.total_games_card, self.total_events_card, self.avg_events_card, self.last_updated_card]:
            for child in card.findChildren(QLabel):
                print(f"  Label: {child.objectName()}")

        # Update the UI with explicit error handling
        try:
            self.total_games_card.findChild(QLabel, "TotalGamesValue").setText(str(total_games))
            self.total_events_card.findChild(QLabel, "TotalEventsValue").setText(str(total_events))

            # Use the exact name we set in _create_stat_card
            avg_label = self.avg_events_card.findChild(QLabel, "AvgEventsperGameValue")

            if avg_label:
                avg_label.setText(f"{avg_events:.1f}")
                print(f"Successfully updated avg events label: {avg_events:.1f}")
            else:
                print("Could not find AvgEventsperGameValue label, trying fallback...")
                # Fallback: find any label with "Value" in its name in the avg_events_card
                for child in self.avg_events_card.findChildren(QLabel):
                    print(f"Found child label: {child.objectName()}")
                    if "Value" in child.objectName():
                        child.setText(f"{avg_events:.1f}")
                        print(f"Updated label {child.objectName()} with value {avg_events:.1f}")
                        break

            # Update last updated timestamp
            from datetime import datetime
            now = datetime.now()
            timestamp = now.strftime("%m/%d/%Y, %I:%M:%S %p")

            last_updated_label = self.last_updated_card.findChild(QLabel, "LastUpdatedValue")
            if last_updated_label:
                last_updated_label.setText(timestamp)

        except Exception as e:
            print(f"Error updating stats: {e}")

    def on_search_changed(self, text):
        """Handle search text changes with debouncing"""
        self.search_text = text.lower()

        # Use a single-shot timer to debounce the search
        # This prevents UI freezes by delaying the search until typing stops
        if hasattr(self, '_search_timer'):
            self._search_timer.stop()
        else:
            self._search_timer = QTimer()
            self._search_timer.setSingleShot(True)
            self._search_timer.timeout.connect(self._delayed_search)

        # Start a 300ms timer - will only trigger if no new keystrokes happen
        self._search_timer.start(300)

    def _delayed_search(self):
        """Perform the actual search after debounce delay"""
        try:
            # Show a loading indicator in the status bar if available
            if hasattr(self.app, 'set_status'):
                self.app.set_status(f"Searching for: {self.search_text}...")

            # Apply filters and update table
            self.apply_filters_and_sort()
            self.update_games_table()

            # Update status when done
            if hasattr(self.app, 'set_status'):
                self.app.set_status(f"Found {len(self.filtered_games)} results")
        except Exception as e:
            print(f"Search error: {str(e)}")
            # Reset the UI if there's an error
            if hasattr(self.app, 'set_status'):
                self.app.set_status(f"Search error: {str(e)}")
            # Clear the table to prevent partial updates
            self.games_table.setRowCount(0)

    def on_filter_changed(self, filter_text):
        """Handle event filter changes"""
        self.current_filter = filter_text
        self.apply_filters_and_sort()
        self.update_games_table()

    def on_sort_changed(self, sort_option):
        """Handle sort option changes"""
        self.current_sort = sort_option
        self.apply_filters_and_sort()
        self.update_games_table()

    def apply_filters_and_sort(self):
        """Apply search, filter, and sort to the games data - highly optimized version"""
        # Start with an empty list with pre-allocated capacity for better performance
        filtered_games = []

        # Limit the maximum number of items to process at once to prevent UI freezes
        MAX_ITEMS = 1000
        if len(self.current_games_data) > MAX_ITEMS:
            print(f"Warning: Large dataset ({len(self.current_games_data)} items). Limiting to {MAX_ITEMS} items.")

        # Pre-compute filter values for better performance
        search_text = self.search_text.lower() if self.search_text else None
        event_filter = self.current_filter if self.current_filter != "All Events" else None

        # Pre-compute event counts only if needed for sorting
        event_counts = None
        if self.current_sort in ["Most Events", "Fewest Events"]:
            event_counts = {}
            # Only compute counts for games that will pass the filters
            for game_name, game_data in self.current_games_data.items():
                # Apply search filter first to avoid unnecessary event count calculations
                if search_text:
                    game_name_lower = game_name.lower()
                    if search_text not in game_name_lower:
                        # Quick check on name before checking other fields
                        package_name = game_data.get("package_name", "").lower()
                        app_token = game_data.get("app_token", "").lower()

                        if (search_text not in package_name and
                            search_text not in app_token):
                            continue

                # Apply event filter
                if event_filter:
                    events = game_data.get("events", {})
                    if event_filter not in events:
                        continue

                # Only calculate event count if the game passes filters
                event_counts[game_name] = len(game_data.get("events", {}))

        # Determine sort key function based on current sort
        if self.current_sort == "Name (A-Z)":
            sort_key = lambda x: x[0]
            sort_reverse = False
        elif self.current_sort == "Name (Z-A)":
            sort_key = lambda x: x[0]
            sort_reverse = True
        elif self.current_sort == "Most Events":
            sort_key = lambda x: event_counts.get(x[0], 0) if event_counts else 0
            sort_reverse = True
        elif self.current_sort == "Fewest Events":
            sort_key = lambda x: event_counts.get(x[0], 0) if event_counts else 0
            sort_reverse = False
        elif self.current_sort == "Recently Added":
            # Sort by timestamp if available, otherwise use name
            # We'll use a helper function to extract timestamp or creation info
            def get_timestamp(item):
                game_data = item[1]
                # Check for timestamp field (could be added_at, created_at, etc.)
                for field in ['added_at', 'created_at', 'timestamp', 'last_updated']:
                    if field in game_data and game_data[field]:
                        return game_data[field]
                # If no timestamp found, return empty string (will sort at beginning)
                return ""

            sort_key = get_timestamp
            sort_reverse = True  # Most recent first
        elif self.current_sort == "Purchase Events":
            # Sort games with purchase events first
            def has_purchase_event(item):
                game_data = item[1]
                events = game_data.get("events", {})
                # Check for any event with "purchase" in the name (case insensitive)
                for event_name in events.keys():
                    if "purchase" in event_name.lower():
                        return 1
                return 0

            sort_key = has_purchase_event
            sort_reverse = True  # Games with purchase events first
        elif self.current_sort == "Missing App Token":
            # Sort games with missing app tokens first
            def missing_app_token(item):
                game_data = item[1]
                app_token = game_data.get("app_token", "")
                # Return 1 if app token is missing or empty, 0 otherwise
                return 1 if not app_token or app_token == "AppToken_NotFound" else 0

            sort_key = missing_app_token
            sort_reverse = True  # Games with missing app tokens first
        else:
            # Default sort
            sort_key = lambda x: x[0]
            sort_reverse = False

        # Process games in batches to prevent UI freezes
        count = 0
        for game_name, game_data in self.current_games_data.items():
            # Limit the number of items to prevent UI freezes
            if count >= MAX_ITEMS:
                break

            # Search filter - optimized with early returns
            if search_text:
                # Check name first as it's most likely to match
                if search_text in game_name.lower():
                    pass  # Continue processing this game
                # Only check other fields if name doesn't match
                elif search_text in game_data.get("package_name", "").lower():
                    pass  # Continue processing this game
                elif search_text in game_data.get("app_token", "").lower():
                    pass  # Continue processing this game
                else:
                    # Skip this game if no fields match
                    continue

            # Event filter - only apply if we have a specific event filter
            if event_filter and event_filter not in game_data.get("events", {}):
                continue

            # If we get here, the game passed all filters
            filtered_games.append((game_name, game_data))
            count += 1

        # Apply sorting - more efficient with pre-computed key function
        # Use try/except to handle any sorting errors gracefully
        try:
            # For complex sorting functions, wrap in a safe sort key function
            # that handles exceptions and returns a default value
            def safe_sort_key(item):
                try:
                    return sort_key(item)
                except Exception as e:
                    print(f"Error in sort key for {item[0] if isinstance(item, tuple) and len(item) > 0 else 'unknown'}: {str(e)}")
                    # Return a default value based on sort type
                    if self.current_sort in ["Most Events", "Fewest Events"]:
                        return 0
                    elif self.current_sort in ["Recently Added"]:
                        return ""
                    elif self.current_sort in ["Purchase Events", "Missing App Token"]:
                        return 0
                    else:
                        return item[0] if isinstance(item, tuple) and len(item) > 0 else ""

            # Use the safe sort key function
            filtered_games.sort(key=safe_sort_key, reverse=sort_reverse)
        except Exception as e:
            print(f"Sorting error: {str(e)}")
            # Fall back to basic sorting if custom sort fails
            filtered_games.sort(key=lambda x: x[0])

        # Update the instance variable
        self.filtered_games = filtered_games

    def update_games_table(self):
        """Update the games table with filtered and sorted data - optimized for performance"""
        try:
            # Temporarily block signals to prevent UI updates during table population
            self.games_table.blockSignals(True)

            # Disable sorting temporarily for better performance
            self.games_table.setSortingEnabled(False)

            # Clear existing rows
            self.games_table.setRowCount(0)

            # Limit the number of rows to display to prevent UI freezes
            MAX_DISPLAY_ROWS = 500
            display_count = min(len(self.filtered_games), MAX_DISPLAY_ROWS)

            if len(self.filtered_games) > MAX_DISPLAY_ROWS:
                print(f"Warning: Limiting display to {MAX_DISPLAY_ROWS} of {len(self.filtered_games)} results")
                if hasattr(self.app, 'set_status'):
                    self.app.set_status(f"Showing {MAX_DISPLAY_ROWS} of {len(self.filtered_games)} results. Please refine your search for more specific results.")

            # Pre-allocate rows for better performance
            self.games_table.setRowCount(display_count)

            # Process rows in batches to prevent UI freezes
            for row, (game_name, game_data) in enumerate(self.filtered_games[:display_count]):
                # Icon cell - optimized to reduce widget creation overhead
                icon_url = game_data.get("icon_url", "")
                icon_widget = QWidget()
                icon_layout = QHBoxLayout(icon_widget)
                # Reduce margins to ensure icon fits properly in the cell
                icon_layout.setContentsMargins(dp(2), dp(2), dp(2), dp(2))
                icon_layout.setSpacing(0)
                icon_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

                # Create a label for the icon - smaller size to fit better
                icon_label = QLabel()
                icon_label.setFixedSize(dp(50), dp(50))
                icon_label.setScaledContents(True)
                icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                icon_label.setStyleSheet(f"""
                    border-radius: {dp(6)}px;
                    background-color: {COLOR_GUNMETAL};
                    border: 1px solid {COLOR_VISTA_BLUE};
                    padding: {dp(1)}px;
                """)

                # Only load icons for visible rows to improve performance
                if icon_url and row < 50:  # Only load icons for first 50 rows initially
                    # Use a placeholder icon initially - smaller size
                    placeholder_icon = QPixmap(dp(50), dp(50))
                    placeholder_icon.fill(Qt.GlobalColor.transparent)
                    icon_label.setPixmap(placeholder_icon)

                    # Load the actual icon in a separate thread to avoid blocking the UI
                    self.load_icon_for_label(icon_url, icon_label)
                else:
                    # Use a default icon if no URL is provided or for deferred loading
                    default_icon = QPixmap(dp(50), dp(50))
                    default_icon.fill(QColor(COLOR_GUNMETAL))
                    painter = QPainter(default_icon)
                    painter.setPen(QColor(COLOR_VISTA_BLUE))
                    painter.setFont(QFont("Arial", dp(20), QFont.Weight.Bold))
                    painter.drawText(default_icon.rect(), Qt.AlignmentFlag.AlignCenter,
                                    game_name[0].upper() if game_name and len(game_name) > 0 else "?")
                    painter.end()
                    icon_label.setPixmap(default_icon)

                icon_layout.addWidget(icon_label)
                self.games_table.setCellWidget(row, 0, icon_widget)

                # Game name cell
                game_cell = QTableWidgetItem(game_name)
                self.games_table.setItem(row, 1, game_cell)

                # App token cell - with better formatting
                app_token = game_data.get("app_token", "")

                # Create a widget to hold the token with better styling
                token_widget = QWidget()
                token_layout = QHBoxLayout(token_widget)
                token_layout.setContentsMargins(dp(2), dp(2), dp(2), dp(2))

                # Create a label for the token with ellipsis for long tokens
                token_label = QLabel(app_token)
                token_label.setToolTip(app_token)  # Show full token on hover

                # Style based on whether token exists
                if app_token and app_token != "AppToken_NotFound":
                    token_label.setStyleSheet(f"""
                        color: {COLOR_TEXT_LIGHT};
                        font-size: {dp(12)}px;  /* Larger font */
                        font-weight: bold;
                        background-color: {COLOR_GUNMETAL_2}55;
                        border-radius: {dp(5)}px;  /* More rounded corners */
                        padding: {dp(4)}px {dp(8)}px;  /* More padding */
                        border: 1px solid {COLOR_VISTA_BLUE}55;
                    """)
                else:
                    token_label.setText("Missing")
                    token_label.setStyleSheet(f"""
                        color: #FF6B6B;
                        font-size: {dp(12)}px;  /* Larger font */
                        font-weight: bold;
                        background-color: {COLOR_GUNMETAL_2}55;
                        border-radius: {dp(5)}px;  /* More rounded corners */
                        padding: {dp(4)}px {dp(8)}px;  /* More padding */
                        border: 1px solid #FF6B6B55;
                    """)

                # Set text elision mode
                token_label.setTextFormat(Qt.TextFormat.PlainText)
                token_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

                # Add to layout
                token_layout.addWidget(token_label)
                self.games_table.setCellWidget(row, 2, token_widget)

                # Play Store button cell
                store_widget = QWidget()
                store_layout = QHBoxLayout(store_widget)
                store_layout.setContentsMargins(dp(2), dp(2), dp(2), dp(2))
                store_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

                # Get Play Store URL or package name
                playstore_url = game_data.get("playstore_url", "")
                package_name = game_data.get("package_name", "")

                # Create a button to open the Play Store
                store_btn = QPushButton("🔗")
                store_btn.setFixedSize(dp(40), dp(40))
                store_btn.setToolTip(f"Open in Play Store: {package_name}")

                # Style the button
                if playstore_url or package_name:
                    store_btn.setStyleSheet(f"""
                        background-color: {COLOR_VISTA_BLUE}55;
                        color: white;
                        border: 1px solid {COLOR_VISTA_BLUE};
                        border-radius: {dp(5)}px;
                        font-size: {dp(16)}px;
                        padding: {dp(2)}px;
                    """)

                    # Create URL if only package name is available
                    if package_name and not playstore_url:
                        playstore_url = f"https://play.google.com/store/apps/details?id={package_name}"

                    # Connect button to open URL
                    if playstore_url:
                        store_btn.clicked.connect(lambda _, url=playstore_url: self.open_url(url))
                    else:
                        store_btn.setEnabled(False)
                else:
                    # Disable button if no URL or package name
                    store_btn.setEnabled(False)
                    store_btn.setStyleSheet(f"""
                        background-color: {COLOR_GUNMETAL_2};
                        color: {COLOR_TEXT_LIGHT};
                        border: 1px solid {COLOR_GRADIENT_LIGHT};
                        border-radius: {dp(5)}px;
                        font-size: {dp(16)}px;
                        padding: {dp(2)}px;
                    """)

                store_layout.addWidget(store_btn)
                self.games_table.setCellWidget(row, 3, store_widget)

                # Events cell - create a more compact widget with event chips
                events = game_data.get("events", {})
                events_widget = QWidget()
                events_layout = QHBoxLayout(events_widget)
                events_layout.setContentsMargins(dp(3), dp(1), dp(3), dp(1))
                events_layout.setSpacing(dp(3))

                # Add event chips in a more compact layout
                event_count = len(events)

                if event_count > 0:
                    # Create a flow layout container for event chips
                    flow_widget = QWidget()
                    flow_layout = QHBoxLayout(flow_widget)
                    flow_layout.setContentsMargins(0, 0, 0, 0)
                    flow_layout.setSpacing(dp(3))

                    # Check for purchase events to highlight them
                    has_purchase = any("purchase" in event_name.lower() for event_name in events.keys())

                    # Show different layouts based on event count
                    if event_count <= 2:
                        # For 1-2 events, show them directly
                        for event_name in list(events.keys())[:2]:
                            # Highlight purchase events
                            is_purchase = "purchase" in event_name.lower()
                            bg_color = COLOR_BLUE_VIOLET if is_purchase else COLOR_VISTA_BLUE

                            event_chip = QLabel(event_name)
                            event_chip.setStyleSheet(f"""
                                background-color: {bg_color};
                                color: white;
                                border-radius: {dp(5)}px;
                                padding: {dp(3)}px {dp(6)}px;
                                font-size: {dp(11)}px;
                                font-weight: bold;
                                border: 1px solid {QColor(bg_color).lighter(130).name()};
                            """)
                            flow_layout.addWidget(event_chip)
                    else:
                        # For 3+ events, show count with indicator for purchase events
                        count_label = QLabel(f"{event_count} events")

                        # Use different color if has purchase events
                        bg_color = COLOR_BLUE_VIOLET if has_purchase else COLOR_VISTA_BLUE
                        count_label.setStyleSheet(f"""
                            background-color: {bg_color};
                            color: white;
                            border-radius: {dp(5)}px;
                            padding: {dp(3)}px {dp(6)}px;
                            font-size: {dp(11)}px;
                            font-weight: bold;
                            border: 1px solid {QColor(bg_color).lighter(130).name()};
                        """)
                        flow_layout.addWidget(count_label)

                        # Add purchase indicator if applicable
                        if has_purchase:
                            purchase_label = QLabel("💰")
                            purchase_label.setToolTip("Has purchase events")
                            purchase_label.setStyleSheet(f"""
                                background-color: {COLOR_BLUE_VIOLET};
                                color: white;
                                border-radius: {dp(5)}px;
                                padding: {dp(2)}px {dp(4)}px;
                                font-size: {dp(12)}px;
                                font-weight: bold;
                                border: 1px solid {QColor(COLOR_BLUE_VIOLET).lighter(130).name()};
                            """)
                            flow_layout.addWidget(purchase_label)

                    # Add the flow layout to the main layout with stretch to fill the space
                    events_layout.addWidget(flow_widget, 1)
                else:
                    # No events - simple label
                    no_events_label = QLabel("No events")
                    no_events_label.setStyleSheet(f"color: {COLOR_TEXT_LIGHT}; font-size: {dp(9)}px;")
                    events_layout.addWidget(no_events_label)
                    events_layout.addStretch()

                self.games_table.setCellWidget(row, 4, events_widget)

                # Actions cell - more compact view and edit buttons
                actions_widget = QWidget()
                actions_layout = QHBoxLayout(actions_widget)
                actions_layout.setContentsMargins(dp(2), dp(2), dp(2), dp(2))
                actions_layout.setSpacing(dp(5))
                actions_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

                # Create a horizontal button group
                buttons_widget = QWidget()
                buttons_layout = QHBoxLayout(buttons_widget)
                buttons_layout.setContentsMargins(0, 0, 0, 0)
                buttons_layout.setSpacing(dp(2))

                # View button
                view_btn = QPushButton("👁️")
                view_btn.setFixedSize(dp(30), dp(30))
                view_btn.setToolTip("View game details")
                view_btn.setStyleSheet(f"""
                    background-color: {COLOR_VISTA_BLUE}55;
                    color: white;
                    border: 1px solid {COLOR_VISTA_BLUE};
                    border-radius: {dp(5)}px;
                    font-size: {dp(16)}px;
                    padding: 0px;
                    font-weight: bold;
                """)
                view_btn.clicked.connect(lambda _, name=game_name: self.view_game(name))
                buttons_layout.addWidget(view_btn)

                # Edit button
                edit_btn = QPushButton("✏️")
                edit_btn.setFixedSize(dp(30), dp(30))
                edit_btn.setToolTip("Edit game")
                edit_btn.setStyleSheet(f"""
                    background-color: {COLOR_BLUE_VIOLET}55;
                    color: white;
                    border: 1px solid {COLOR_BLUE_VIOLET};
                    border-radius: {dp(5)}px;
                    font-size: {dp(16)}px;
                    padding: 0px;
                    font-weight: bold;
                """)
                edit_btn.clicked.connect(lambda _, name=game_name: self.edit_game(name))
                buttons_layout.addWidget(edit_btn)

                actions_layout.addWidget(buttons_widget)
                self.games_table.setCellWidget(row, 5, actions_widget)

                # Process application events periodically to keep UI responsive
                if row % 50 == 0 and row > 0:
                    QApplication.processEvents()

            # Re-enable sorting
            self.games_table.setSortingEnabled(True)

            # Load remaining icons after table is populated
            if display_count > 50:
                QTimer.singleShot(500, self._load_remaining_icons)

        except Exception as e:
            print(f"Error updating table: {str(e)}")
        finally:
            # Always re-enable signals
            self.games_table.blockSignals(False)

    def _load_remaining_icons(self):
        """Load icons for remaining rows after initial table population"""
        try:
            # Process remaining rows (after the first 50)
            for row in range(50, self.games_table.rowCount()):
                # Get the game data for this row
                name_item = self.games_table.item(row, 1)
                if not name_item:
                    continue

                game_name = name_item.text()
                game_data = next((data for name, data in self.filtered_games if name == game_name), None)
                if not game_data:
                    continue

                # Get the icon widget
                icon_widget = self.games_table.cellWidget(row, 0)
                if not icon_widget:
                    continue

                # Find the icon label within the widget
                icon_label = icon_widget.findChild(QLabel)
                if not icon_label:
                    continue

                # Load the icon if available
                icon_url = game_data.get("icon_url", "")
                if icon_url:
                    self.load_icon_for_label(icon_url, icon_label)

                # Process events every few rows to keep UI responsive
                if row % 20 == 0:
                    QApplication.processEvents()

        except Exception as e:
            print(f"Error loading remaining icons: {str(e)}")

    def view_game(self, game_name):
        """Open dialog to view game data in read-only mode"""
        game_data = self.current_games_data.get(game_name, {})

        # Create a dialog similar to edit dialog but with read-only fields
        dialog = QDialog(self)
        dialog.setWindowTitle(f"View Game: {game_name}")
        dialog.setMinimumWidth(dp(500))
        dialog.setMinimumHeight(dp(400))

        layout = QVBoxLayout(dialog)

        # Form layout for game details
        form_layout = QFormLayout()

        # Game Name
        game_name_label = QLabel(game_name)
        game_name_label.setStyleSheet(f"font-weight: bold; font-size: {dp(14)}px;")
        form_layout.addRow("Game Name:", game_name_label)

        # App Token
        app_token_label = QLabel(game_data.get("app_token", ""))
        form_layout.addRow("App Token:", app_token_label)

        # Package Name
        package_name_label = QLabel(game_data.get("package_name", ""))
        form_layout.addRow("Package Name:", package_name_label)

        # Play Store URL
        playstore_url_label = QLabel(game_data.get("playstore_url", ""))
        playstore_url_label.setOpenExternalLinks(True)
        if game_data.get("playstore_url"):
            playstore_url_label.setText(f"<a href='{game_data.get('playstore_url')}'>{game_data.get('playstore_url')}</a>")
        form_layout.addRow("Play Store URL:", playstore_url_label)

        # Icon URL
        icon_url_label = QLabel(game_data.get("icon_url", ""))
        form_layout.addRow("Icon URL:", icon_url_label)

        layout.addLayout(form_layout)

        # Events section
        layout.addWidget(QLabel("Events:"))

        # Events display (read-only text area)
        events_display = QTextEdit()
        events_display.setReadOnly(True)

        # Format events as JSON for the text editor
        import json
        events = game_data.get("events", {})
        if events:
            events_display.setText(json.dumps(events, indent=2))
        else:
            events_display.setText("No events found")

        layout.addWidget(events_display)

        # Close button
        buttons_layout = QHBoxLayout()
        close_button = QPushButton("Close")
        close_button.clicked.connect(dialog.accept)
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_button)
        layout.addLayout(buttons_layout)

        dialog.exec()

    def add_new_game(self):
        """Open dialog to add a new game"""
        dialog = self.create_game_edit_dialog()
        if dialog.exec():
            game_data = dialog.get_game_data()
            game_name = game_data["game_name"]

            if not game_name:
                self.app.show_info_popup("Error", "Game name cannot be empty")
                return

            # Update game_data_cache and save to file
            local_data = self.app.load_local_data_file()
            local_data[game_name] = game_data["data"]
            self.app.save_local_data_file(local_data)
            game_data_cache.update({game_name: game_data["data"]})

    def edit_game(self, game_name):
        """Open dialog to edit a game"""
        game_data = self.current_games_data.get(game_name, {})
        dialog = self.create_game_edit_dialog(game_data, game_name)

        if dialog.exec():
            updated_data = dialog.get_game_data()
            new_game_name = updated_data["game_name"]

            if not new_game_name:
                self.app.show_info_popup("Error", "Game name cannot be empty")
                return

            # Update the last_updated timestamp
            from datetime import datetime
            updated_data["data"]["last_updated"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Preserve the original added_at timestamp if it exists
            if "added_at" in game_data:
                updated_data["data"]["added_at"] = game_data["added_at"]
            # Fallback to check if it's in the nested data structure
            elif game_data and isinstance(game_data, dict) and "added_at" in game_data.get("data", {}):
                updated_data["data"]["added_at"] = game_data["data"]["added_at"]

            # Update game_data_cache and save to file
            local_data = self.app.load_local_data_file()
            if new_game_name != game_name and game_name in local_data:
                del local_data[game_name]
            local_data[new_game_name] = updated_data["data"]
            self.app.save_local_data_file(local_data)
            game_data_cache.update(local_data)

    def search_missing_games(self):
        """Search Play Store for games missing package names or details"""
        # Check if BeautifulSoup is installed
        if not BeautifulSoup:
            self.app.show_info_popup("Missing Dependency",
                                    "The BeautifulSoup library is required for HTML scraping.\n\n"
                                    "Please install it with: pip install beautifulsoup4")
            return

        # Find games missing package names or Play Store URLs
        missing_games = []
        for game_name, game_data in self.current_games_data.items():
            package_name = game_data.get('package_name', '')
            playstore_url = game_data.get('playstore_url', '')

            if not package_name or package_name == 'PackageName_NotFound' or not playstore_url:
                missing_games.append(game_name)

        if not missing_games:
            self.app.show_info_popup("No Missing Data",
                                    "All games have package names and Play Store URLs.\n\n"
                                    "No search needed.")
            return

        # Confirmation dialog
        confirm_dialog = QDialog(self)
        confirm_dialog.setWindowTitle("Confirm Search")
        confirm_dialog.setMinimumWidth(dp(400))
        confirm_layout = QVBoxLayout(confirm_dialog)

        # Info label
        confirm_info = QLabel(f"Found {len(missing_games)} games missing package names or Play Store URLs. Do you want to search for them?")
        confirm_info.setWordWrap(True)
        confirm_layout.addWidget(confirm_info)

        # Buttons
        buttons_layout = QHBoxLayout()
        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(confirm_dialog.reject)
        search_btn = QPushButton("Start Search")
        search_btn.clicked.connect(confirm_dialog.accept)
        search_btn.setStyleSheet(f"background-color: {COLOR_BLUE_VIOLET}; color: white;")

        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(search_btn)
        confirm_layout.addLayout(buttons_layout)

        # Show the dialog
        if confirm_dialog.exec() != QDialog.DialogCode.Accepted:
            return

        # Create progress dialog
        progress_dialog = QDialog(self)
        progress_dialog.setWindowTitle("Play Store Search Progress")
        progress_dialog.setMinimumWidth(dp(600))
        progress_layout = QVBoxLayout(progress_dialog)

        # Progress information
        progress_label = QLabel(f"Searching for game details: 0 of {len(missing_games)}")
        progress_label.setStyleSheet(f"font-size: {dp(16)}px; font-weight: bold; margin: {dp(10)}px 0;")
        progress_layout.addWidget(progress_label)

        # Search method info
        method_label = QLabel("Using HTML Scraping for search")
        method_label.setStyleSheet(f"font-size: {dp(14)}px; color: {COLOR_VISTA_BLUE};")
        progress_layout.addWidget(method_label)

        # Progress bar
        progress_bar = QProgressBar()
        progress_bar.setRange(0, len(missing_games))
        progress_bar.setValue(0)
        progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid {COLOR_GRADIENT_LIGHT};
                border-radius: {dp(5)}px;
                text-align: center;
                height: {dp(25)}px;
                background-color: {COLOR_GUNMETAL_2};
            }}
            QProgressBar::chunk {{
                background-color: {COLOR_BLUE_VIOLET};
                border-radius: {dp(4)}px;
            }}
        """)
        progress_layout.addWidget(progress_bar)

        # Status details
        status_label = QLabel("Starting search...")
        status_label.setWordWrap(True)
        status_label.setStyleSheet(f"font-size: {dp(14)}px; margin: {dp(5)}px 0;")
        progress_layout.addWidget(status_label)

        # Stats
        stats_layout = QHBoxLayout()

        updated_label = QLabel("Updated: 0")
        updated_label.setStyleSheet(f"font-size: {dp(14)}px; color: {COLOR_VIVID_SKY_BLUE}; font-weight: bold;")
        stats_layout.addWidget(updated_label)

        skipped_label = QLabel("Skipped: 0")
        skipped_label.setStyleSheet(f"font-size: {dp(14)}px; color: {COLOR_VISTA_BLUE};")
        stats_layout.addWidget(skipped_label)

        remaining_label = QLabel(f"Remaining: {len(missing_games)}")
        remaining_label.setStyleSheet(f"font-size: {dp(14)}px; color: {COLOR_TEXT_LIGHT};")
        stats_layout.addWidget(remaining_label)

        progress_layout.addLayout(stats_layout)

        # Current game details
        current_game_frame = QFrame()
        current_game_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {COLOR_GUNMETAL_2};
                border-radius: {dp(5)}px;
                padding: {dp(10)}px;
                margin-top: {dp(10)}px;
            }}
        """)
        current_game_layout = QVBoxLayout(current_game_frame)

        current_game_label = QLabel("Current Game:")
        current_game_label.setStyleSheet(f"font-size: {dp(14)}px; font-weight: bold;")
        current_game_layout.addWidget(current_game_label)

        current_game_name = QLabel("")
        current_game_name.setStyleSheet(f"font-size: {dp(16)}px; color: {COLOR_VIVID_SKY_BLUE}; font-weight: bold;")
        current_game_layout.addWidget(current_game_name)

        progress_layout.addWidget(current_game_frame)

        # Buttons
        buttons_layout = QHBoxLayout()

        stop_button = QPushButton("Stop")
        stop_button.clicked.connect(progress_dialog.reject)
        stop_button.setStyleSheet(f"""
            background-color: {COLOR_GUNMETAL_2};
            color: {COLOR_TEXT_LIGHT};
            font-size: {dp(14)}px;
            padding: {dp(8)}px {dp(15)}px;
            border-radius: {dp(5)}px;
            border: 1px solid {COLOR_GRADIENT_LIGHT};
        """)
        buttons_layout.addWidget(stop_button)

        progress_layout.addLayout(buttons_layout)

        # Show the dialog but don't block
        progress_dialog.setModal(False)
        progress_dialog.show()

        # Process each missing game
        updated_count = 0
        skipped_count = 0
        processed_count = 0
        continue_processing = True

        for game_name in missing_games:
            if not continue_processing:
                break

            # Update progress
            processed_count += 1
            progress_bar.setValue(processed_count)
            progress_label.setText(f"Searching for game details: {processed_count} of {len(missing_games)}")
            status_label.setText(f"Processing: {game_name}")
            current_game_name.setText(game_name)
            remaining_label.setText(f"Remaining: {len(missing_games) - processed_count}")

            # Process events to update UI
            QApplication.processEvents()

            # Show the search dialog
            search_dialog = PlayStoreSearchDialog(self, game_name)
            if search_dialog.exec() == QDialog.DialogCode.Accepted:
                # Get the selected result
                result = search_dialog.get_selected_result()

                if result == "SKIP":
                    # User chose to skip this game
                    skipped_count += 1
                    skipped_label.setText(f"Skipped: {skipped_count}")
                    status_label.setText(f"Skipped: {game_name}")
                elif result:
                    # Update the game data
                    success = self.update_game_with_playstore_data(game_name, result)
                    if success:
                        updated_count += 1
                        updated_label.setText(f"Updated: {updated_count}")
                        status_label.setText(f"Updated: {game_name} with {result['app_name']}")
            else:
                # User cancelled the dialog
                continue_processing = False
                status_label.setText("Search cancelled by user")

            # Process events to update UI
            QApplication.processEvents()

        # Close the progress dialog
        progress_dialog.close()

        # Show final results
        if updated_count > 0:
            self.app.show_info_popup("Search Complete",
                                    f"Updated {updated_count} of {len(missing_games)} games with Play Store data.\n"
                                    f"Skipped: {skipped_count} games.\n\n"
                                    f"The table will now refresh to show the updated data.")

            # Refresh the data to show the updates
            self.refresh_data()
        else:
            self.app.show_info_popup("Search Complete",
                                    f"No games were updated.\n"
                                    f"Skipped: {skipped_count} games.")

        # Return focus to the main window
        self.setFocus()

    def update_game_with_playstore_data(self, game_name, playstore_data):
        """Update a game with data from the Play Store"""
        if not game_name or not playstore_data:
            return False

        try:
            # Get the current game data
            local_data = self.app.load_local_data_file()
            if game_name not in local_data:
                return False

            # Update the game data with Play Store information
            local_data[game_name]['package_name'] = playstore_data.get('package_name', '')
            local_data[game_name]['playstore_url'] = playstore_data.get('play_store_url', '')

            # Only update the icon URL if it's not already set or is the default
            current_icon = local_data[game_name].get('icon_url', '')
            if not current_icon or current_icon == 'Icon_NotFound':
                local_data[game_name]['icon_url'] = playstore_data.get('icon_url', '')

            # Save the updated data
            self.app.save_local_data_file(local_data)
            game_data_cache.update(local_data)

            return True
        except Exception as e:
            print(f"Error updating game with Play Store data: {e}")
            return False

    def delete_game(self, game_name):
        """Delete a game from the data"""
        # Confirm deletion
        confirm_dialog = QDialog(self)
        confirm_dialog.setWindowTitle("Confirm Deletion")
        confirm_layout = QVBoxLayout(confirm_dialog)
        confirm_layout.addWidget(QLabel(f"Are you sure you want to delete {game_name}?"))

        buttons_layout = QHBoxLayout()
        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(confirm_dialog.reject)
        delete_btn = QPushButton("Delete")
        delete_btn.clicked.connect(confirm_dialog.accept)
        delete_btn.setStyleSheet("background-color: #FF5555;")

        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(delete_btn)
        confirm_layout.addLayout(buttons_layout)

        if confirm_dialog.exec():
            # Update game_data_cache and save to file
            local_data = self.app.load_local_data_file()
            if game_name in local_data:
                del local_data[game_name]
            self.app.save_local_data_file(local_data)
            game_data_cache.update(local_data)

    def load_icon_for_label(self, icon_url, label):
        """Load an icon from a URL and set it to a label - highly optimized version"""
        if not icon_url or not label:
            return

        try:
            # Fast path: Check if we have this icon in cache first
            cached_pixmap = ICON_CACHE.get(icon_url)
            if cached_pixmap:
                # Scale the pixmap to fit the label using fast transformation
                label_size = min(label.width(), label.height())
                if label_size <= 0:  # Ensure valid size
                    label_size = dp(50)

                # Only scale if needed - avoid unnecessary operations
                if cached_pixmap.width() > label_size or cached_pixmap.height() > label_size:
                    scaled_pixmap = cached_pixmap.scaled(
                        label_size, label_size,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.FastTransformation
                    )
                    label.setPixmap(scaled_pixmap)
                else:
                    # Use original if it's already small enough
                    label.setPixmap(cached_pixmap)
                return

            # If not in cache, set a simple placeholder (minimal operations)
            # Use a pre-generated placeholder if possible
            first_letter = "?"
            if icon_url and len(icon_url) > 0:
                # Extract first letter from URL domain or path
                try:
                    parsed = urlparse(icon_url)
                    if parsed.netloc:
                        domain_parts = parsed.netloc.split('.')
                        if domain_parts and domain_parts[0]:
                            first_letter = domain_parts[0][0].upper()
                    elif parsed.path:
                        path_parts = parsed.path.split('/')
                        for part in path_parts:
                            if part:
                                first_letter = part[0].upper()
                                break
                except:
                    pass

            # Create a simple placeholder with minimal operations
            placeholder_size = min(label.width(), label.height())
            if placeholder_size <= 0:
                placeholder_size = dp(50)

            placeholder = QPixmap(placeholder_size, placeholder_size)
            placeholder.fill(QColor(COLOR_VISTA_BLUE))

            # Only draw text if needed
            if first_letter and first_letter != "?":
                painter = QPainter(placeholder)
                painter.setPen(Qt.GlobalColor.white)
                painter.setFont(QFont("Arial", int(placeholder_size/2.5), QFont.Weight.Bold))
                painter.drawText(placeholder.rect(), Qt.AlignmentFlag.AlignCenter, first_letter)
                painter.end()

            # Set the placeholder
            label.setPixmap(placeholder)

            # Only load from URL if it's a valid URL
            if not icon_url or not icon_url.startswith('http'):
                return

            # Create a worker with minimal overhead - no need to pass width/height
            worker = Worker(self._load_icon_optimized, icon_url)

            # Use weak references to prevent memory leaks
            worker.use_weak_references()

            # Connect with minimal lambda overhead
            worker.result.connect(lambda result: self._set_icon_if_valid(label, result))

            # Submit to thread pool
            worker.submit_to_pool()

        except Exception as e:
            # Minimal error handling to avoid crashes
            print(f"Icon loading error: {str(e)}")

    def _load_icon_optimized(self, url):
        """Highly optimized icon loading function"""
        try:
            # Check if already in cache first (thread-safe)
            cached = ICON_CACHE.get(url)
            if cached:
                return cached

            # Try to get a higher resolution icon by modifying the URL
            high_res_url = url
            if '=w' in high_res_url:
                # Replace width and height parameters for higher resolution
                high_res_url = re.sub(r'=w\d+-h\d+', '=w256-h256', high_res_url)
            elif '=s' in high_res_url:
                # Replace size parameter for higher resolution
                high_res_url = re.sub(r'=s\d+', '=s256', high_res_url)

            # Use optimized image loading with shorter timeout
            pixmap = load_image_from_url(high_res_url, timeout=3)

            if pixmap and not pixmap.isNull():
                # Cache the original pixmap
                ICON_CACHE.put(url, pixmap)
                return pixmap

        except Exception as e:
            # Minimal error handling
            print(f"Icon load error ({url}): {str(e)}")

        return None

    def _set_icon_if_valid(self, label, pixmap):
        """Set icon to label if valid with minimal operations"""
        if not label or not pixmap or pixmap.isNull():
            return

        try:
            # Get label size
            label_size = min(label.width(), label.height())
            if label_size <= 0:
                label_size = dp(50)

            # Only scale if needed
            if pixmap.width() > label_size or pixmap.height() > label_size:
                scaled = pixmap.scaled(
                    label_size, label_size,
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.FastTransformation
                )
                label.setPixmap(scaled)
            else:
                label.setPixmap(pixmap)
        except Exception as e:
            # Minimal error handling
            print(f"Set icon error: {str(e)}")

    def scrape_playstore_icons(self):
        """Scrape Play Store URLs for icons for games that have a Play Store URL but no icon URL"""
        games_to_update = {}

        # Find games that have a Play Store URL but no icon URL
        for game_name, game_data in self.current_games_data.items():
            playstore_url = game_data.get('playstore_url', '')
            icon_url = game_data.get('icon_url', '')

            if playstore_url and (not icon_url or icon_url == 'Icon_NotFound'):
                games_to_update[game_name] = game_data

        if not games_to_update:
            print("No games need icon updates")
            if hasattr(self.app, 'show_info_popup'):
                self.app.show_info_popup("Icon Update", "No games need icon updates")
            return

        print(f"Found {len(games_to_update)} games that need icon updates")

        # Create a loading dialog
        if hasattr(self.app, 'show_loading_dialog'):
            self.app.show_loading_dialog(f"Scraping icons for {len(games_to_update)} games...")

        # Create a worker to process all games in a background thread
        worker = Worker(self._scrape_icons_task, games_to_update)
        worker.result.connect(self._handle_scrape_results)
        worker.progress.connect(lambda msg: self.app.update_loading_message(msg) if hasattr(self.app, 'update_loading_message') else None)
        worker.error.connect(lambda err: print(f"Error scraping icons: {err}"))
        worker.finished.connect(lambda: self.app.hide_loading_dialog() if hasattr(self.app, 'hide_loading_dialog') else None)

        # Submit to thread pool for managed execution
        worker.submit_to_pool()

    def _scrape_icons_task(self, games_to_update):
        """Background task to scrape icons for multiple games"""
        updated_games = {}
        total_games = len(games_to_update)
        processed = 0

        for game_name, game_data in games_to_update.items():
            processed += 1
            # Use the current thread's progress signal
            Worker.progress.emit(self, f"Scraping icon for {game_name}... ({processed}/{total_games})")

            try:
                playstore_url = game_data.get('playstore_url', '')
                if not playstore_url:
                    continue

                # Use cached_request for better performance
                response = cached_request(
                    playstore_url,
                    headers={'User-Agent': 'Mozilla/5.0', 'Accept-Language': 'en-US,en;q=0.5'},
                    timeout=15
                )

                if not response or response.status_code >= 400:
                    print(f"Error fetching Play Store page for {game_name}: HTTP {response.status_code if response else 'No response'}")
                    continue

                html_content = response.text

                # Extract the icon URL using the same patterns as in TokenExtractorScreen
                icon_url_patterns = [
                    r'<img.*?itemprop="image".*?src="(.*?)"',
                    r'<meta property="og:image" content="(.*?)">',
                    r'<img.*?class="T75of.*?src="(.*?)"'
                ]

                icon_found = False
                for pattern in icon_url_patterns:
                    icon_url_match = re.search(pattern, html_content, re.IGNORECASE)
                    if icon_url_match:
                        icon_url = icon_url_match.group(1)
                        if not icon_url.startswith('http'):
                            icon_url = urlparse(playstore_url).scheme + "://" + urlparse(playstore_url).netloc + icon_url

                        # Update the game data with the icon URL
                        game_data['icon_url'] = icon_url
                        updated_games[game_name] = game_data
                        icon_found = True
                        print(f"Found icon for {game_name}: {icon_url[:60]}...")
                        break

                if not icon_found:
                    print(f"Could not find icon for {game_name}")

            except Exception as e:
                print(f"Error scraping icon for {game_name}: {e}")

        return updated_games

    def _handle_scrape_results(self, updated_games):
        """Handle the results of the icon scraping task"""
        # Update the loading message
        if hasattr(self.app, 'update_loading_message'):
            self.app.update_loading_message("Saving updated icon data...")

        # Update the game data cache with the new icon URLs
        if updated_games:
            try:
                # Load the current local data
                local_data = self.app.load_local_data_file()

                # Update the local data with the new icon URLs
                for game_name, game_data in updated_games.items():
                    local_data[game_name] = game_data

                # Save the updated local data
                self.app.save_local_data_file(local_data)

                # Update the game data cache
                game_data_cache.update(local_data)

                # Now upload the updated data to the API
                if hasattr(self.app, 'update_loading_message'):
                    self.app.update_loading_message("Uploading updated icon data to API...")

                # Create a worker for API upload to avoid blocking the UI
                worker = Worker(self._upload_icons_to_api, local_data)
                worker.result.connect(self._handle_api_upload_result)
                worker.error.connect(lambda err: self._handle_api_upload_error(err, len(updated_games)))

                # Submit to thread pool for managed execution
                worker.submit_to_pool()

                # Update the UI to reflect the changes
                self.refresh_data()

            except Exception as e:
                print(f"Error handling scrape results: {e}")
                if hasattr(self.app, 'hide_loading_dialog'):
                    self.app.hide_loading_dialog()
                if hasattr(self.app, 'show_info_popup'):
                    self.app.show_info_popup(
                        "Icon Update Error",
                        f"Error updating icons: {e}"
                    )
        else:
            # Hide the loading dialog
            if hasattr(self.app, 'hide_loading_dialog'):
                self.app.hide_loading_dialog()

            # Show a message
            if hasattr(self.app, 'show_info_popup'):
                self.app.show_info_popup("Icon Update", "No icons were updated")

            print("No icons were updated")

    def _upload_icons_to_api(self, local_data):
        """Upload the updated icon data to the API"""
        try:
            # Get the upload URL from settings
            upload_url = self.app.settings.get('upload_url', '')
            if not upload_url:
                return {
                    'success': False,
                    'message': "No upload URL configured in settings"
                }

            # Prepare the data for upload
            upload_data = json.dumps(local_data)

            # Upload the data to the API using cached_request
            response = requests_session.post(
                upload_url,
                data=upload_data,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            response.raise_for_status()

            return {
                'success': True,
                'message': "Data uploaded to API successfully"
            }
        except Exception as e:
            print(f"Error uploading data to API: {e}")
            return {
                'success': False,
                'message': f"Error uploading to API: {e}"
            }

    def _handle_api_upload_result(self, result):
        """Handle the result of the API upload"""
        # Hide the loading dialog
        if hasattr(self.app, 'hide_loading_dialog'):
            self.app.hide_loading_dialog()

        # Get the number of updated games
        updated_count = sum(1 for _, game_data in self.current_games_data.items()
                           if game_data.get('icon_url') and game_data.get('icon_url') != 'Icon_NotFound')

        # Show a success message
        if hasattr(self.app, 'show_info_popup'):
            if result['success']:
                self.app.show_info_popup(
                    "Icon Update",
                    f"Updated icons for {updated_count} games and uploaded to API"
                )
            else:
                self.app.show_info_popup(
                    "Icon Update",
                    f"Updated icons for {updated_count} games locally.\n\n{result['message']}"
                )

    def _handle_api_upload_error(self, error, updated_count):
        """Handle an error during API upload"""
        # Hide the loading dialog
        if hasattr(self.app, 'hide_loading_dialog'):
            self.app.hide_loading_dialog()

        # Show an error message
        if hasattr(self.app, 'show_info_popup'):
            self.app.show_info_popup(
                "Icon Update",
                f"Updated icons for {updated_count} games locally, but failed to upload to API.\n\nError: {error}"
            )

    def create_game_edit_dialog(self, game_data=None, game_name=None):
        """Create a dialog for adding or editing a game"""
        is_edit_mode = bool(game_name)
        dialog = QDialog(self)
        dialog.setWindowTitle("Add New Game" if not is_edit_mode else f"Edit Game: {game_name}")
        dialog.setMinimumWidth(dp(500))
        dialog.setMinimumHeight(dp(400))

        layout = QVBoxLayout(dialog)

        # Form layout for game details
        form_layout = QFormLayout()

        # Game Name
        game_name_input = QLineEdit()
        form_layout.addRow("Game Name:", game_name_input)

        # App Token
        app_token_input = QLineEdit()
        form_layout.addRow("App Token:", app_token_input)

        # Package Name
        package_name_input = QLineEdit()
        form_layout.addRow("Package Name:", package_name_input)

        # Play Store URL
        playstore_url_input = QLineEdit()
        form_layout.addRow("Play Store URL:", playstore_url_input)

        # Icon URL
        icon_url_input = QLineEdit()
        form_layout.addRow("Icon URL:", icon_url_input)

        layout.addLayout(form_layout)

        # Events section
        layout.addWidget(QLabel("Events:"))

        # Events editor (simple text area for now)
        events_editor = QTextEdit()
        events_editor.setPlaceholderText("Enter events in JSON format, e.g.:\n{\n  \"Event Name 1\": \"abc123\",\n  \"Event Name 2\": \"def456\"\n}")
        layout.addWidget(events_editor)

        # Buttons
        buttons_layout = QHBoxLayout()

        # Only show bulk import button in add mode, not edit mode
        if not is_edit_mode:
            bulk_import_button = QPushButton("Bulk Import")
            bulk_import_button.setToolTip("Import multiple games from JSON file or text")
            bulk_import_button.clicked.connect(lambda: self.show_bulk_import_dialog(dialog))
            bulk_import_button.setStyleSheet(f"""
                background-color: {COLOR_CERULEAN}55;
                color: white;
                border: 1px solid {COLOR_CERULEAN};
                border-radius: {dp(5)}px;
                padding: {dp(5)}px {dp(10)}px;
                font-weight: bold;
            """)
            buttons_layout.addWidget(bulk_import_button)

        save_button = QPushButton("Save")
        save_button.clicked.connect(dialog.accept)
        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(dialog.reject)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(save_button)
        layout.addLayout(buttons_layout)

        # Populate fields if editing
        if is_edit_mode and game_data:
            game_name_input.setText(game_name)
            app_token_input.setText(game_data.get("app_token", ""))
            package_name_input.setText(game_data.get("package_name", ""))
            playstore_url_input.setText(game_data.get("playstore_url", ""))
            icon_url_input.setText(game_data.get("icon_url", ""))

            # Format events as JSON for the text editor
            import json
            events = game_data.get("events", {})
            if events:
                events_editor.setText(json.dumps(events, indent=2))

        # Add method to get data from dialog
        def get_game_data():
            game_name = game_name_input.text().strip()

            # Parse events from JSON text
            import json
            events = {}
            try:
                events_text = events_editor.toPlainText().strip()
                if events_text:
                    events = json.loads(events_text)
            except json.JSONDecodeError:
                # Handle invalid JSON
                self.app.show_info_popup("Error", "Invalid JSON format in events field")
                return None

            # Add timestamp for "Recently Added" sorting
            from datetime import datetime
            current_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            return {
                "game_name": game_name,
                "data": {
                    "app_token": app_token_input.text().strip(),
                    "package_name": package_name_input.text().strip(),
                    "playstore_url": playstore_url_input.text().strip(),
                    "icon_url": icon_url_input.text().strip(),
                    "events": events,
                    "added_at": current_timestamp,
                    "last_updated": current_timestamp
                }
            }

        dialog.get_game_data = get_game_data
        return dialog

    def show_bulk_import_dialog(self, parent_dialog):
        """Show dialog for bulk importing games from JSON file or text"""
        # Create the bulk import dialog
        import_dialog = QDialog(self)
        import_dialog.setWindowTitle("Bulk Import Games")
        import_dialog.setMinimumWidth(dp(600))
        import_dialog.setMinimumHeight(dp(500))

        layout = QVBoxLayout(import_dialog)

        # Tab widget for file/text options
        tab_widget = QTabWidget()

        # File import tab
        file_tab = QWidget()
        file_layout = QVBoxLayout(file_tab)

        file_label = QLabel("Select a JSON file containing game data:")
        file_layout.addWidget(file_label)

        file_input_layout = QHBoxLayout()
        file_path_input = QLineEdit()
        file_path_input.setReadOnly(True)
        file_path_input.setPlaceholderText("No file selected")

        browse_button = QPushButton("Browse...")
        browse_button.clicked.connect(lambda: self._browse_for_json_file(file_path_input))

        file_input_layout.addWidget(file_path_input, 3)
        file_input_layout.addWidget(browse_button, 1)
        file_layout.addLayout(file_input_layout)

        file_layout.addWidget(QLabel("Preview:"))
        file_preview = QTextEdit()
        file_preview.setReadOnly(True)
        file_preview.setPlaceholderText("File preview will appear here")
        file_layout.addWidget(file_preview)

        # Text import tab
        text_tab = QWidget()
        text_layout = QVBoxLayout(text_tab)

        text_label = QLabel("Paste JSON data containing game information:")
        text_layout.addWidget(text_label)

        text_input = QTextEdit()
        text_input.setPlaceholderText('Paste JSON data here, e.g.:\n{\n  "Zoo 2": {\n    "app_token": "ychj2wyvu3uo",\n    "events": {\n      "Unknown 1": "7gj5kk",\n      "Unknown 2": "v50ff6"\n    },\n    "package_name": "com.upjers.zoo2animalpark"\n  }\n}')
        text_layout.addWidget(text_input)

        # Add tabs to tab widget
        tab_widget.addTab(file_tab, "Import from File")
        tab_widget.addTab(text_tab, "Import from Text")

        layout.addWidget(tab_widget)

        # Status area
        status_label = QLabel("Ready to import")
        layout.addWidget(status_label)

        # Buttons
        buttons_layout = QHBoxLayout()
        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(import_dialog.reject)

        import_button = QPushButton("Import")
        import_button.clicked.connect(lambda: self._process_bulk_import(
            import_dialog,
            tab_widget.currentIndex(),
            file_path_input.text(),
            text_input.toPlainText(),
            status_label,
            parent_dialog
        ))

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(import_button)
        layout.addLayout(buttons_layout)

        # Connect file path changes to preview update
        file_path_input.textChanged.connect(lambda path: self._update_file_preview(path, file_preview, status_label))

        # Show the dialog
        import_dialog.exec()

    def _browse_for_json_file(self, file_path_input):
        """Open file dialog to select a JSON file"""
        from PyQt6.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select JSON File",
            "",
            "JSON Files (*.json);;All Files (*)"
        )

        if file_path:
            file_path_input.setText(file_path)

    def _update_file_preview(self, file_path, preview_widget, status_label):
        """Update the preview of the selected JSON file"""
        if not file_path:
            preview_widget.clear()
            return

        try:
            with open(file_path, 'r') as f:
                data = json.load(f)

            # Format the data for preview
            formatted_json = json.dumps(data, indent=2)

            # Limit preview length
            max_preview_length = 5000
            if len(formatted_json) > max_preview_length:
                preview_text = formatted_json[:max_preview_length] + "...\n\n(Preview truncated, file is too large)"
            else:
                preview_text = formatted_json

            preview_widget.setText(preview_text)

            # Update status with game count
            game_count = len(data) if isinstance(data, dict) else 0
            status_label.setText(f"Found {game_count} games in the file")

        except Exception as e:
            preview_widget.setText(f"Error loading file: {str(e)}")
            status_label.setText("Error: Invalid JSON file")

    def _process_bulk_import(self, dialog, tab_index, file_path, text_data, status_label, parent_dialog):
        """Process the bulk import from either file or text"""
        try:
            # Get data based on selected tab
            if tab_index == 0:  # File tab
                if not file_path:
                    status_label.setText("Error: No file selected")
                    return

                with open(file_path, 'r') as f:
                    import_data = json.load(f)
            else:  # Text tab
                if not text_data.strip():
                    status_label.setText("Error: No JSON data provided")
                    return

                import_data = json.loads(text_data)

            # Validate data structure
            if not isinstance(import_data, dict):
                status_label.setText("Error: JSON data must be a dictionary/object")
                return

            # Count games
            game_count = len(import_data)
            if game_count == 0:
                status_label.setText("Error: No games found in the data")
                return

            # Show confirmation dialog
            confirm_dialog = QDialog(dialog)
            confirm_dialog.setWindowTitle("Confirm Import")
            confirm_layout = QVBoxLayout(confirm_dialog)

            confirm_label = QLabel(f"Are you sure you want to import {game_count} games?\n\nThis will add or update the following games:")
            confirm_layout.addWidget(confirm_label)

            # Show list of games to be imported
            games_list = QTextEdit()
            games_list.setReadOnly(True)

            games_text = ""
            for i, (game_name, game_data) in enumerate(import_data.items(), 1):
                if i <= 20:  # Limit to first 20 games in the preview
                    app_token = game_data.get("app_token", "No app token")
                    event_count = len(game_data.get("events", {}))
                    games_text += f"{i}. {game_name} (App Token: {app_token}, Events: {event_count})\n"
                else:
                    games_text += f"... and {game_count - 20} more games"
                    break

            games_list.setText(games_text)
            games_list.setMaximumHeight(dp(200))
            confirm_layout.addWidget(games_list)

            buttons_layout = QHBoxLayout()
            cancel_btn = QPushButton("Cancel")
            cancel_btn.clicked.connect(confirm_dialog.reject)

            import_btn = QPushButton(f"Import {game_count} Games")
            import_btn.clicked.connect(confirm_dialog.accept)

            buttons_layout.addWidget(cancel_btn)
            buttons_layout.addWidget(import_btn)
            confirm_layout.addLayout(buttons_layout)

            if confirm_dialog.exec():
                # Process the import
                self._import_games(import_data, status_label, dialog, parent_dialog)

        except json.JSONDecodeError:
            status_label.setText("Error: Invalid JSON format")
        except Exception as e:
            status_label.setText(f"Error: {str(e)}")

    def _import_games(self, games_data, status_label, dialog, parent_dialog):
        """Import multiple games from the provided data"""
        try:
            # Show loading dialog
            self.app.show_loading_dialog("Importing games...")

            # Get current data
            local_data = self.app.load_local_data_file()

            # Track statistics
            added_count = 0
            updated_count = 0
            error_count = 0
            errors = []

            # Process each game
            for game_name, game_data in games_data.items():
                try:
                    if not game_name or not isinstance(game_data, dict):
                        error_count += 1
                        errors.append(f"Invalid data for game: {game_name}")
                        continue

                    # Ensure required fields exist
                    if "app_token" not in game_data:
                        game_data["app_token"] = ""

                    if "events" not in game_data or not isinstance(game_data["events"], dict):
                        game_data["events"] = {}

                    # Add timestamps
                    from datetime import datetime
                    current_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                    # If game exists, update it
                    if game_name in local_data:
                        # Preserve existing timestamps if available
                        if "added_at" in local_data[game_name]:
                            game_data["added_at"] = local_data[game_name]["added_at"]
                        else:
                            game_data["added_at"] = current_timestamp

                        game_data["last_updated"] = current_timestamp
                        updated_count += 1
                    else:
                        # New game
                        game_data["added_at"] = current_timestamp
                        game_data["last_updated"] = current_timestamp
                        added_count += 1

                    # Update the data
                    local_data[game_name] = game_data

                except Exception as e:
                    error_count += 1
                    errors.append(f"Error processing {game_name}: {str(e)}")

            # Save the updated data
            self.app.save_local_data_file(local_data)

            # Update the cache
            game_data_cache.update(local_data)

            # Hide loading dialog
            self.app.hide_loading_dialog()

            # Show results
            result_message = f"Import complete:\n- {added_count} games added\n- {updated_count} games updated"

            if error_count > 0:
                result_message += f"\n- {error_count} errors"
                if errors:
                    result_message += "\n\nErrors:"
                    for i, error in enumerate(errors[:10], 1):  # Show first 10 errors
                        result_message += f"\n{i}. {error}"
                    if len(errors) > 10:
                        result_message += f"\n... and {len(errors) - 10} more errors"

            self.app.show_info_popup("Import Results", result_message)

            # Close dialogs
            dialog.accept()
            parent_dialog.reject()  # Close the parent dialog too

        except Exception as e:
            self.app.hide_loading_dialog()
            self.app.show_info_popup("Import Error", f"An error occurred during import: {str(e)}")
            status_label.setText(f"Error: {str(e)}")

    def open_url(self, url):
        """Open a URL in the default web browser"""
        if not url:
            return

        try:
            import webbrowser
            webbrowser.open(url)
            print(f"Opening URL: {url}")
        except Exception as e:
            print(f"Error opening URL: {e}")
            if hasattr(self.app, 'show_info_popup'):
                self.app.show_info_popup("Error", f"Could not open URL: {e}")

    def set_background(self):
        """Called when the screen becomes active to set new backgrounds"""
        # Set random backgrounds for all stat cards
        for card in [self.total_games_card, self.total_events_card, self.avg_events_card, self.last_updated_card]:
            if isinstance(card, (RotatingBackgroundBox, ImageBackgroundBox)):
                card.set_random_background()

        # Set a new background for the table
        if hasattr(self, 'games_table') and isinstance(self.games_table, BackgroundTableWidget):
            self.games_table.set_random_background()

class SettingsScreen(BaseScreenWidget):
    screen_name_attr = "settings_screen"
    def __init__(self, parent_app=None, parent=None):
        super().__init__(parent)
        self.app = parent_app
        self.worker_thread = None # Add worker thread reference
        self._build_ui()
        self.load_urls_from_settings()

    def _build_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(dp(20), dp(20), dp(20), dp(20))
        main_layout.setSpacing(dp(15))

        title = QLabel("Settings")
        title.setObjectName("ScreenTitleLabel")
        main_layout.addWidget(title)

        # Create a scroll area for settings
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)

        # Container widget for all settings
        settings_container = QWidget()
        settings_layout = QVBoxLayout(settings_container)
        settings_layout.setContentsMargins(0, 0, 0, 0)
        settings_layout.setSpacing(dp(20))

        # API URLs Section
        api_group = QGroupBox("API URLs")
        api_layout = QVBoxLayout(api_group)
        api_layout.setSpacing(dp(10))

        receive_box = QHBoxLayout()
        receive_box.addWidget(QLabel("Fetch URL:"))
        self.receive_url_input = QLineEdit()
        receive_box.addWidget(self.receive_url_input, 1)
        api_layout.addLayout(receive_box)

        upload_box = QHBoxLayout()
        upload_box.addWidget(QLabel("Upload URL:"))
        self.upload_url_input = QLineEdit()
        upload_box.addWidget(self.upload_url_input, 1)
        api_layout.addLayout(upload_box)

        api_actions = QHBoxLayout()
        btn_save_urls = QPushButton("Save API URLs")
        btn_save_urls.clicked.connect(self.save_urls_to_settings)
        api_actions.addWidget(btn_save_urls)

        btn_fetch = QPushButton("Fetch from API")
        btn_fetch.clicked.connect(self.fetch_data_action)
        api_actions.addWidget(btn_fetch)

        btn_upload = QPushButton("Upload to API")
        btn_upload.clicked.connect(self.upload_data_action)
        api_actions.addWidget(btn_upload)

        api_layout.addLayout(api_actions)
        settings_layout.addWidget(api_group)

        # Proxy Settings Section
        proxy_group = QGroupBox("Proxy Settings")
        proxy_layout = QVBoxLayout(proxy_group)
        proxy_layout.setSpacing(dp(10))

        self.enable_proxy_checkbox = QCheckBox("Enable Proxy")
        proxy_layout.addWidget(self.enable_proxy_checkbox)

        proxy_url_box = QHBoxLayout()
        proxy_url_box.addWidget(QLabel("Proxy URL:"))
        self.proxy_url_input = QLineEdit()
        self.proxy_url_input.setPlaceholderText("Enter proxy URL (e.g., http://proxy.example.com)")
        proxy_url_box.addWidget(self.proxy_url_input, 1)
        proxy_layout.addLayout(proxy_url_box)

        proxy_port_box = QHBoxLayout()
        proxy_port_box.addWidget(QLabel("Proxy Port:"))
        self.proxy_port_input = QLineEdit()
        self.proxy_port_input.setPlaceholderText("Enter proxy port (e.g., 8080)")
        proxy_port_box.addWidget(self.proxy_port_input, 1)
        proxy_layout.addLayout(proxy_port_box)

        proxy_save_btn = QPushButton("Save Proxy Settings")
        proxy_save_btn.clicked.connect(self.save_proxy_settings)
        proxy_layout.addWidget(proxy_save_btn)

        settings_layout.addWidget(proxy_group)

        # User Agent Settings Section
        ua_group = QGroupBox("User Agent Settings")
        ua_layout = QVBoxLayout(ua_group)
        ua_layout.setSpacing(dp(10))

        ua_type_box = QHBoxLayout()
        ua_type_box.addWidget(QLabel("User Agent Type:"))
        self.ua_type_combo = QComboBox()
        self.ua_type_combo.addItems(["Default", "Android", "iOS", "Custom"])
        self.ua_type_combo.currentTextChanged.connect(self.on_ua_type_changed)
        ua_type_box.addWidget(self.ua_type_combo, 1)
        ua_layout.addLayout(ua_type_box)

        custom_ua_box = QHBoxLayout()
        custom_ua_box.addWidget(QLabel("Custom User Agent:"))
        self.custom_ua_input = QLineEdit()
        self.custom_ua_input.setPlaceholderText("Enter custom user agent string")
        self.custom_ua_input.setEnabled(False)  # Initially disabled
        custom_ua_box.addWidget(self.custom_ua_input, 1)
        ua_layout.addLayout(custom_ua_box)

        ua_save_btn = QPushButton("Save User Agent Settings")
        ua_save_btn.clicked.connect(self.save_ua_settings)
        ua_layout.addWidget(ua_save_btn)

        settings_layout.addWidget(ua_group)

        # Logging Options Section
        log_group = QGroupBox("Logging Options")
        log_layout = QVBoxLayout(log_group)
        log_layout.setSpacing(dp(10))

        self.enable_logging_checkbox = QCheckBox("Enable Logging")
        log_layout.addWidget(self.enable_logging_checkbox)

        log_level_box = QHBoxLayout()
        log_level_box.addWidget(QLabel("Log Level:"))
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["Debug", "Info", "Warning", "Error"])
        log_level_box.addWidget(self.log_level_combo, 1)
        log_layout.addLayout(log_level_box)

        log_path_box = QHBoxLayout()
        log_path_box.addWidget(QLabel("Log File Path:"))
        self.log_path_input = QLineEdit()
        self.log_path_input.setPlaceholderText("Enter log file path")
        log_path_box.addWidget(self.log_path_input, 1)
        log_layout.addLayout(log_path_box)

        log_save_btn = QPushButton("Save Logging Settings")
        log_save_btn.clicked.connect(self.save_logging_settings)
        log_layout.addWidget(log_save_btn)

        settings_layout.addWidget(log_group)

        # Add the settings container to the scroll area
        scroll_area.setWidget(settings_container)
        main_layout.addWidget(scroll_area)

        # Status label at the bottom
        self.status_label = QLabel("Status: Idle")
        self.status_label.setFixedHeight(dp(30))
        main_layout.addWidget(self.status_label)

    def set_background(self):
        # This screen doesn't need a rotating background
        pass

    # --- Settings methods ---
    def update_status(self, message):
        self.status_label.setText(f"Status: {message}")

    def load_urls_from_settings(self):
        self.receive_url_input.setText(self.app.settings.get('receive_url', DEFAULT_RECEIVE_URL))
        self.upload_url_input.setText(self.app.settings.get('upload_url', DEFAULT_UPLOAD_URL))

        # Load proxy settings if they exist
        self.enable_proxy_checkbox.setChecked(self.app.settings.get('enable_proxy', False))
        self.proxy_url_input.setText(self.app.settings.get('proxy_url', ''))
        self.proxy_port_input.setText(self.app.settings.get('proxy_port', ''))

        # Load user agent settings if they exist
        ua_type = self.app.settings.get('ua_type', 'Default')
        self.ua_type_combo.setCurrentText(ua_type)
        self.custom_ua_input.setText(self.app.settings.get('custom_ua', ''))
        self.custom_ua_input.setEnabled(ua_type == 'Custom')

        # Load logging settings if they exist
        self.enable_logging_checkbox.setChecked(self.app.settings.get('enable_logging', False))
        self.log_level_combo.setCurrentText(self.app.settings.get('log_level', 'Info'))
        self.log_path_input.setText(self.app.settings.get('log_path', ''))

        self.update_status("Settings loaded.")

    def save_urls_to_settings(self):
        self.app.settings['receive_url'] = self.receive_url_input.text().strip()
        self.app.settings['upload_url'] = self.upload_url_input.text().strip()
        self.app.save_app_settings()
        self.update_status("API URLs saved.")
        self.app.show_info_popup("Settings Saved", "API URLs have been saved.")

    def save_proxy_settings(self):
        self.app.settings['enable_proxy'] = self.enable_proxy_checkbox.isChecked()
        self.app.settings['proxy_url'] = self.proxy_url_input.text().strip()
        self.app.settings['proxy_port'] = self.proxy_port_input.text().strip()
        self.app.save_app_settings()

        # Update the global requests session with the new proxy settings
        update_requests_session(self.app.settings)

        # Show status message
        proxy_enabled = self.enable_proxy_checkbox.isChecked()
        if proxy_enabled:
            proxy_url = self.proxy_url_input.text().strip()
            proxy_port = self.proxy_port_input.text().strip()
            self.update_status(f"Proxy settings saved and applied: {proxy_url}:{proxy_port}")
            self.app.show_info_popup("Settings Saved", f"Proxy settings have been saved and applied: {proxy_url}:{proxy_port}")
        else:
            self.update_status("Proxy settings saved and disabled.")
            self.app.show_info_popup("Settings Saved", "Proxy settings have been saved and disabled.")

    def on_ua_type_changed(self, ua_type):
        self.custom_ua_input.setEnabled(ua_type == 'Custom')

    def save_ua_settings(self):
        ua_type = self.ua_type_combo.currentText()
        self.app.settings['ua_type'] = ua_type
        self.app.settings['custom_ua'] = self.custom_ua_input.text().strip() if ua_type == 'Custom' else ''
        self.app.save_app_settings()
        self.update_status("User agent settings saved.")
        self.app.show_info_popup("Settings Saved", "User agent settings have been saved.")

    def save_logging_settings(self):
        self.app.settings['enable_logging'] = self.enable_logging_checkbox.isChecked()
        self.app.settings['log_level'] = self.log_level_combo.currentText()
        self.app.settings['log_path'] = self.log_path_input.text().strip()
        self.app.save_app_settings()
        self.update_status("Logging settings saved.")
        self.app.show_info_popup("Settings Saved", "Logging settings have been saved.")

    def _fetch_data_task(self):
        """Background task to fetch data from the API"""
        # Cannot use Worker's progress signal directly, so we'll just return a message
        # The progress updates will be handled by the worker thread

        receive_url = self.app.settings.get('receive_url', DEFAULT_RECEIVE_URL)
        if not receive_url:
            return "Error: Fetch URL is not set."

        try:
            # Use cached_request for better performance
            response = cached_request(receive_url, timeout=20)
            if not response:
                return "Error: No response from API."

            if response.status_code >= 400:
                return f"Error: HTTP {response.status_code} from API."

            api_data = response.json()
            processed_message = self.app.process_fetched_data_and_save(api_data)
            return f"Fetch successful. {processed_message}"
        except Exception as e:
            return f"Error during fetch: {e}"

    def fetch_data_action(self):
        """Fetch data from the API in a background thread"""
        self.app.show_loading_dialog("Fetching Data...")

        # Create a worker for the fetch task
        worker = Worker(self._fetch_data_task)
        worker.result.connect(lambda res: (self.update_status(res), self.app.show_info_popup("Fetch Result", res)))
        worker.finished.connect(self.app.hide_loading_dialog)
        worker.error.connect(lambda err: (self.update_status(f"Error: {err}"), self.app.show_info_popup("Fetch Error", str(err))))
        worker.progress.connect(self.app.update_loading_message)

        # Submit to thread pool for managed execution
        worker.submit_to_pool()

    def _upload_data_task(self):
        """Background task to upload data to the API"""
        # Cannot use Worker's progress signal directly, so we'll just return a message
        # The progress updates will be handled by the worker thread

        upload_url = self.app.settings.get('upload_url', DEFAULT_UPLOAD_URL)
        if not upload_url:
            return "Error: Upload URL is not set."

        local_data = self.app.load_local_data_file()
        if not local_data:
            return "No local data to upload."

        try:
            # Use requests_session for better performance
            response = requests_session.post(
                upload_url,
                json=local_data,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            response.raise_for_status()

            # Limit the response text to avoid memory issues
            response_text = response.text[:100] if response.text else ""
            return f"Upload successful. Server response: {response.status_code} - {response_text}"
        except Exception as e:
            return f"Error during upload: {e}"

    def upload_data_action(self):
        """Upload data to the API in a background thread"""
        self.app.show_loading_dialog("Uploading Data...")

        # Create a worker for the upload task
        worker = Worker(self._upload_data_task)
        worker.result.connect(lambda res: (self.update_status(res), self.app.show_info_popup("Upload Result", res)))
        worker.finished.connect(self.app.hide_loading_dialog)
        worker.error.connect(lambda err: (self.update_status(f"Error: {err}"), self.app.show_info_popup("Upload Error", str(err))))
        worker.progress.connect(self.app.update_loading_message)

        # Submit to thread pool for managed execution
        worker.submit_to_pool()


# --- Main UI with Navigation Drawer ---
class NavMenuItem(QPushButton):
    def __init__(self, text, screen_name, emoji="", parent=None):
        super().__init__(parent)
        self.screen_name = screen_name
        self.is_active = False
        self.base_text = text
        self.emoji = emoji
        self.setText(f"{emoji} {text}" if emoji else text)

        self.setCheckable(True) # Allows it to stay "pressed" visually if active
        self.setFixedHeight(dp(50))
        self.setObjectName("NavMenuItem") # For QSS
        # Styling is primarily handled by QSS in MainWindow

    def set_active(self, active):
        self.is_active = active
        self.setChecked(active) # Visually mark as active/inactive
        # Apply styling based on active state (QSS handles :checked)

    def update_text_for_nav_state(self, expanded):
        if expanded:
            self.setText(f"{self.emoji} {self.base_text}" if self.emoji else self.base_text)
            self.setToolTip("") # Clear tooltip when expanded
            self.setStyleSheet(self.styleSheet() + " text-align: left; padding-left: 20px;") # Ensure left alignment
        else:
            # When collapsed, just show emoji, centered
            self.setText(self.emoji)
            self.setToolTip(self.base_text) # Show full text on hover when collapsed
            self.setStyleSheet(self.styleSheet() + " text-align: center; padding-left: 0px;") # Center emoji


class MainWindow(QMainWindow):
    # Add parent_app reference to pass to screens if needed
    def __init__(self, parent_app=None):
        super().__init__()
        self.parent_app = parent_app # Store reference to AladdinAppPyQt
        self.setWindowTitle("Aladdin v3.0 (PyQt6)")

        # Set a more reasonable initial size
        self.setGeometry(100, 100, dp(800), dp(700)) # Reduced width

        # Set minimum width to prevent excessive width when not fullscreen
        self.setMinimumWidth(dp(600))

        # Create and set up status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.setStyleSheet(f"""
            QStatusBar {{
                background-color: {COLOR_GUNMETAL};
                color: {COLOR_TEXT_LIGHT};
                border-top: 1px solid {COLOR_GRADIENT_LIGHT};
                padding: {dp(2)}px;
                font-size: {dp(12)}px;
            }}
        """)
        self.status_bar.showMessage("Ready")

        self.nav_items = {}
        self.screen_name_to_index = {} # Map screen names to indices
        self.is_nav_expanded = True
        self.collapsed_width = dp(70) # Slightly wider for emojis
        self.expanded_width = dp(220)


        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QHBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(0,0,0,0)
        self.main_layout.setSpacing(0)

        # --- Navigation Drawer ---
        self.nav_drawer = QFrame()
        self.nav_drawer.setObjectName("NavDrawer")
        self.nav_drawer.setFixedWidth(self.expanded_width) # Initial expanded width
        nav_drawer_layout = QVBoxLayout(self.nav_drawer)
        nav_drawer_layout.setContentsMargins(0,0,0,0)
        nav_drawer_layout.setSpacing(0)

        # Title Box
        title_box = QFrame()
        title_box.setObjectName("NavTitleBox")
        title_box.setFixedHeight(dp(100))
        title_box_layout = QHBoxLayout(title_box)
        title_box_layout.setContentsMargins(dp(10), dp(10), dp(10), dp(10))

        self.expand_button = QPushButton("≡")
        self.expand_button.setObjectName("NavExpandButton")
        self.expand_button.setFixedSize(dp(30), dp(30))
        self.expand_button.clicked.connect(self.toggle_nav_drawer)
        title_box_layout.addWidget(self.expand_button, alignment=Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignLeft) # Align top-left

        title_content_layout = QVBoxLayout()
        title_label = QLabel("Aladdin")
        title_label.setObjectName("NavAppTitle")
        version_subtitle = QLabel("v3.0")
        version_subtitle.setObjectName("NavAppVersion")
        title_content_layout.addWidget(title_label)
        title_content_layout.addWidget(version_subtitle)
        title_box_layout.addLayout(title_content_layout)


        nav_drawer_layout.addWidget(title_box)

        # Nav Menu
        self.nav_menu_widget = QWidget() # Container for nav items
        self.nav_menu_layout = QVBoxLayout(self.nav_menu_widget)
        self.nav_menu_layout.setContentsMargins(0, dp(10), 0, 0)
        self.nav_menu_layout.setSpacing(dp(2))

        menu_items_data = [
            ("S2S", "s2s_screen", "🔄"),
            ("Web Conv", "conversion_screen", "🌐"),
            ("Extractor", "extractor_screen", "📥"),
            ("Attribution", "attribution_screen", "👥"),
            ("Token Storage", "token_storage_screen", "🗄️"),
            ("History", "history_screen", "📜"),
            ("Settings", "settings_screen", "⚙️")
        ]

        for text, screen_name, emoji in menu_items_data:
            item = NavMenuItem(text, screen_name, emoji)
            item.clicked.connect(self.switch_screen_action)
            self.nav_menu_layout.addWidget(item)
            self.nav_items[screen_name] = item

        self.nav_menu_layout.addStretch()
        nav_drawer_layout.addWidget(self.nav_menu_widget)

        # Footer
        footer_label = QLabel("© 2024 Aladdin Tools")
        footer_label.setObjectName("NavFooter")
        footer_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        footer_label.setFixedHeight(dp(40))
        nav_drawer_layout.addWidget(footer_label)

        # Add the nav drawer directly to the main layout (no animated bar)
        self.main_layout.addWidget(self.nav_drawer)

        # --- Screen Manager (Content Area) ---
        self.screen_manager = QStackedWidget()
        # Add screens and map names to indices
        self._add_screen(S2SScreen(parent_app=self.parent_app), "s2s_screen")
        self._add_screen(ConversionScreen(parent_app=self.parent_app), "conversion_screen")
        self._add_screen(TokenExtractorScreen(parent_app=self.parent_app), "extractor_screen")
        self._add_screen(AttributionScreen(parent_app=self.parent_app), "attribution_screen") # Pass app ref
        self._add_screen(TokenStorageScreen(parent_app=self.parent_app), "token_storage_screen")
        self._add_screen(HistoryScreen(parent_app=self.parent_app), "history_screen")
        self._add_screen(SettingsScreen(parent_app=self.parent_app), "settings_screen")


        self.main_layout.addWidget(self.screen_manager)

        self.apply_stylesheet()
        self.switch_screen('s2s_screen') # Set initial screen


    def _add_screen(self, widget, name):
        """Adds a screen widget to the stack and maps its name to index."""
        index = self.screen_manager.addWidget(widget)
        self.screen_name_to_index[name] = index
        # Optionally set the screen_name_attr on the widget if it doesn't have it
        if not hasattr(widget, 'screen_name_attr'):
             widget.screen_name_attr = name


    def toggle_nav_drawer(self):
        start_width = self.nav_drawer.width()
        if self.is_nav_expanded:
            target_width = self.collapsed_width # Collapse
            self.expand_button.setText("»")
        else:
            target_width = self.expanded_width # Expand
            self.expand_button.setText("≡")
        self.is_nav_expanded = not self.is_nav_expanded

        # Update text before animation for smoother look
        for item in self.nav_items.values():
            item.update_text_for_nav_state(self.is_nav_expanded)

        # Animate width
        self.animation = QPropertyAnimation(self.nav_drawer, b"minimumWidth")
        self.animation.setDuration(250) # ms
        self.animation.setStartValue(start_width)
        self.animation.setEndValue(target_width)
        self.animation.setEasingCurve(QEasingCurve.Type.InOutQuad)

        # Also animate maximumWidth to prevent issues during animation
        self.animation_max = QPropertyAnimation(self.nav_drawer, b"maximumWidth")
        self.animation_max.setDuration(250)
        self.animation_max.setStartValue(start_width)
        self.animation_max.setEndValue(target_width)
        self.animation_max.setEasingCurve(QEasingCurve.Type.InOutQuad)

        self.animation.start()
        self.animation_max.start()


    def switch_screen_action(self):
        sender = self.sender()
        if isinstance(sender, NavMenuItem):
            self.switch_screen(sender.screen_name)

    def switch_screen(self, screen_name):
        if screen_name in self.screen_name_to_index:
            index = self.screen_name_to_index[screen_name]
            current_widget = self.screen_manager.widget(index)
            self.screen_manager.setCurrentIndex(index)
            self._set_active_nav_item(screen_name)

            # Set background for screens that have the method
            if hasattr(current_widget, 'set_background'):
                current_widget.set_background()
        else:
            print(f"Error: Screen '{screen_name}' not found.")


    def _set_active_nav_item(self, screen_name):
        for name, item in self.nav_items.items():
            item.set_active(name == screen_name)

    def apply_stylesheet(self):
        # dp values for QSS (use the dp function)
        # Only define the values that are actually used in the stylesheet
        dp_1 = dp(1)
        dp_4 = dp(4)
        dp_5 = dp(5)
        dp_8 = dp(8)
        dp_10 = dp(10)
        dp_12 = dp(12)
        dp_15 = dp(15)
        dp_18 = dp(18) # For larger response log text
        dp_20 = dp(20)
        dp_22 = dp(22)

        self.setStyleSheet(f"""
            QMainWindow, QWidget {{
                font-family: Arial, sans-serif; /* Or a more modern font if available */
                font-size: {dp_12}px; /* Base font size */
            }}
            QFrame#NavDrawer {{
                background-color: {COLOR_GUNMETAL};
            }}
            QFrame#NavTitleBox {{
                background-color: {COLOR_ACCENT_GLOW};
                border-bottom: {dp_1}px solid {QColor(COLOR_ACCENT_GLOW).darker(120).name()};
            }}
            QPushButton#NavExpandButton {{
                background-color: transparent;
                color: white;
                border: none;
                font-size: {dp_20}px;
                font-weight: bold;
                padding: 0px; /* Remove padding */
                margin: 0px;
            }}
            QLabel#NavAppTitle {{
                color: white;
                font-size: {dp_22}px;
                font-weight: bold;
                padding-left: {dp_10}px;
            }}
            QLabel#NavAppVersion {{
                color: #dddddd;
                font-size: {dp_10}px;
                padding-left: {dp_10}px;
            }}
            QPushButton#NavMenuItem {{ /* Default state */
                background-color: transparent;
                color: {COLOR_TEXT_LIGHT};
                border: none; /* Remove all borders by default */
                border-left: {dp_4}px solid transparent; /* Transparent border for alignment */
                text-align: left; /* Default alignment */
                padding: {dp_10}px; /* Base padding */
                padding-left: {dp_20}px; /* Specific left padding when expanded */
                font-size: {dp_15}px;
            }}
            QPushButton#NavMenuItem:hover {{
                background-color: {COLOR_HOVER_BG};
            }}
            QPushButton#NavMenuItem:checked {{ /* Active state */
                background-color: {COLOR_HOVER_BG};
                border-left: {dp_4}px solid {COLOR_ACCENT_GLOW}; /* Show border only when active */
            }}
            /* Style adjustments might be needed in NavMenuItem.update_text_for_nav_state */

            QLabel#NavFooter {{
                background-color: {COLOR_GUNMETAL_2};
                color: #aaaaaa;
                font-size: {dp_10}px;
                border-top: {dp_1}px solid {COLOR_GRADIENT_DARK};
            }}

            /* General Widget Styling */
            QPushButton {{
                background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {COLOR_BLUE_VIOLET}, stop:1 {QColor(COLOR_BLUE_VIOLET).darker(120).name()});
                color: white;
                border: {dp_1}px solid {QColor(COLOR_BLUE_VIOLET).darker(130).name()};
                border-radius: {dp_5}px;
                padding: {dp_8}px {dp_15}px;
                font-size: {dp_15}px;
                font-weight: bold;
                min-height: {dp(30)}px;
            }}
            QPushButton:hover {{
                background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {QColor(COLOR_BLUE_VIOLET).lighter(110).name()}, stop:1 {COLOR_BLUE_VIOLET});
            }}
            QPushButton:pressed {{
                background-color: {QColor(COLOR_BLUE_VIOLET).darker(120).name()};
                padding-top: {dp(10)}px; /* 3D press effect */
                padding-bottom: {dp(6)}px;
            }}

            QLineEdit, QTextEdit {{
                background-color: {COLOR_GUNMETAL_2};
                color: {COLOR_TEXT_LIGHT};
                border: {dp_1}px solid {COLOR_GRADIENT_LIGHT};
                border-radius: {dp_5}px;
                padding: {dp_8}px;
                font-size: {dp_12}px;
            }}
            QLineEdit:focus, QTextEdit:focus {{
                border: {dp_1}px solid {COLOR_VIVID_SKY_BLUE};
                background-color: {COLOR_GRADIENT_LIGHT};
            }}
            QTextEdit {{
                 selection-background-color: {COLOR_VIVID_SKY_BLUE};
                 selection-color: {COLOR_TEXT_DARK};
            }}

            QComboBox {{
                background-color: {COLOR_GUNMETAL_2};
                color: {COLOR_TEXT_LIGHT};
                border: {dp_1}px solid {COLOR_GRADIENT_LIGHT};
                border-radius: {dp_5}px;
                padding: {dp_1}px {dp_15}px {dp_1}px {dp_5}px; /* Use dp_1 */
                font-size: {dp_15}px; /* Bigger text */
                font-weight: bold;   /* Bolder text */
                min-height: {dp(30)}px;
            }}
            QComboBox:hover {{
                border: {dp_1}px solid {COLOR_VIVID_SKY_BLUE};
            }}
            QComboBox::drop-down {{
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: {dp_20}px;
                border-left-width: {dp_1}px;
                border-left-color: {COLOR_GRADIENT_LIGHT};
                border-left-style: solid;
                border-top-right-radius: {dp_5}px;
                border-bottom-right-radius: {dp_5}px;
            }}
            QComboBox::down-arrow {{
                 /* Use a unicode character for the arrow */
                 /* This part is tricky and might need platform-specific adjustments */
                 /* Or use an image: image: url(:/icons/down_arrow.png); */
                 /* For simplicity, let Qt handle the default arrow for now */
            }}
            QComboBox QAbstractItemView {{ /* Dropdown list */
                background-color: {COLOR_GUNMETAL_2};
                color: {COLOR_TEXT_LIGHT};
                border: {dp_1}px solid {COLOR_VIVID_SKY_BLUE};
                selection-background-color: {COLOR_VIVID_SKY_BLUE};
                selection-color: {COLOR_TEXT_DARK};
                padding: {dp_5}px;
                font-size: {dp_15}px; /* Bigger text */
                font-weight: bold;   /* Bolder text */
            }}

            QLabel {{
                color: {COLOR_TEXT_LIGHT};
                font-size: {dp_12}px;
                background-color: transparent; /* Ensure labels don't block background */
            }}
            QLabel#ScreenTitleLabel {{ /* Specific selector for screen titles */
                font-size: {dp_20}px;
                font-weight: bold;
                color: {COLOR_ACCENT_GLOW};
                padding-bottom: {dp_10}px;
                border-bottom: {dp_1}px solid {COLOR_GRADIENT_LIGHT};
                margin-bottom: {dp_10}px; /* Add margin below title */
            }}
            QScrollArea {{
                border: none;
                background-color: transparent;
            }}
             /* Ensure scroll area viewport is also transparent */
            QScrollArea > QWidget > QWidget {{
                background-color: transparent;
            }}

            QProgressBar {{
                border: {dp_1}px solid {COLOR_GRADIENT_LIGHT};
                border-radius: {dp_5}px;
                background-color: {COLOR_GUNMETAL_2};
                text-align: center;
                color: {COLOR_TEXT_LIGHT};
            }}
            QProgressBar::chunk {{
                background-color: {COLOR_VIVID_SKY_BLUE};
                border-radius: {dp_4}px;
                margin: {dp(1)}px; /* Adjusted margin */
            }}
            QFrame.CardFrame {{ /* Example for card styling if needed */
                background-color: {COLOR_CARD_BG}; /* Use the defined card color */
                border-radius: {dp_10}px;
            }}
             /* Style for the response log text area */
            QTextEdit#ResponseLog {{
                background-color: {QColor(COLOR_GUNMETAL_2).darker(300).name()}f8; /* Even darker background with higher opacity (98%) */
                border-radius: {dp(10)}px; /* Slightly more rounded corners */
                border: 2px solid {QColor(COLOR_VISTA_BLUE).darker(150).name()};
                padding: {dp(15)}px; /* More padding for text */
                margin: {dp(5)}px; /* Add margin around the text area */
                font-family: Consolas, monaco, monospace;
                font-size: {dp_18}px; /* Larger font size for better readability */
                font-weight: bold; /* Make text bolder */
                line-height: 1.5; /* Improved line spacing */
                color: {COLOR_TEXT_LIGHT}; /* Ensure text is light colored for better contrast */
            }}
        """)

# --- Screen Implementations (PyQt6) ---

# S2S Screen
class S2SScreen(BaseScreenWidget):
    screen_name_attr = "s2s_screen" # For matching in MainUI

    def __init__(self, parent_app, parent=None):
        super().__init__(parent)
        self.app = parent_app # Reference to MainWindow or AladdinApp instance
        self.worker_thread = None # For managing worker threads
        self.game_names_list = []
        self.event_names_list = []
        self.current_game = ""
        self.current_event = ""
        self.request_type_options = ["POST REQUEST", "GET REQUEST"] # Simplified
        self.current_request_type = self.request_type_options[0]
        self.platform_options = ["Android", "Apple"] # Platform options
        self.current_platform = self.platform_options[0] # Default to Android

        self._build_ui()
        game_data_cache.data_updated.connect(self.refresh_spinners_from_cache)
        self.refresh_spinners_from_cache() # Initial population

    def _build_ui(self):
        # Use a vertical layout for the main screen (removed animated bar)
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(dp(20), dp(20), dp(20), dp(20))
        main_layout.setSpacing(dp(15))

        # Content area - main vertical layout for everything
        content_area = QWidget()
        content_layout = QVBoxLayout(content_area)
        content_layout.setContentsMargins(0, 0, 0, 0)  # No margins needed
        content_layout.setSpacing(dp(15))

        # Title
        title = QLabel("S2S Event Crediting")
        title.setObjectName("ScreenTitleLabel")
        content_layout.addWidget(title)

        # Create a container widget for the form part that might need scrolling
        form_container_widget = QWidget()
        form_layout = QVBoxLayout(form_container_widget)
        form_layout.setContentsMargins(0, 0, 0, 0) # No margins for the inner layout
        form_layout.setSpacing(dp(15))

        # --- Add form elements to form_layout ---
        # Game Spinner
        game_box = QHBoxLayout()
        game_box.addWidget(QLabel("Game:"))
        self.game_spinner = QComboBox()
        self.game_spinner.currentTextChanged.connect(self.on_game_selected)
        game_box.addWidget(self.game_spinner, 1)
        form_layout.addLayout(game_box)

        # Event Spinner
        event_box = QHBoxLayout()
        event_box.addWidget(QLabel("Event:"))
        self.event_spinner = QComboBox()
        event_box.addWidget(self.event_spinner, 1)
        form_layout.addLayout(event_box)

        # Request Type Spinner
        req_type_box = QHBoxLayout()
        req_type_box.addWidget(QLabel("Request Type:"))
        self.request_type_spinner = QComboBox()
        self.request_type_spinner.addItems(self.request_type_options)
        self.request_type_spinner.currentTextChanged.connect(self.on_request_type_selected)
        req_type_box.addWidget(self.request_type_spinner, 1)
        form_layout.addLayout(req_type_box)

        # Platform Spinner (Android/Apple)
        platform_box = QHBoxLayout()
        platform_box.addWidget(QLabel("Platform:"))
        self.platform_spinner = QComboBox()
        self.platform_spinner.addItems(self.platform_options)
        self.platform_spinner.currentTextChanged.connect(self.on_platform_selected)
        platform_box.addWidget(self.platform_spinner, 1)
        form_layout.addLayout(platform_box)

        # Device ID Input (GPS ADID or IDFA)
        adid_box = QHBoxLayout()
        self.device_id_label = QLabel("GPS ADID:")  # Will be updated based on platform selection
        adid_box.addWidget(self.device_id_label)
        self.gps_adid_input = QLineEdit()
        self.gps_adid_input.setPlaceholderText("Enter GPS ADID")
        adid_box.addWidget(self.gps_adid_input, 1)
        form_layout.addLayout(adid_box)

        # Action Buttons - in a horizontal layout with consistent styling
        buttons_box = QHBoxLayout()
        buttons_box.setSpacing(dp(10))  # Add consistent spacing

        # Create buttons with enhanced styling
        btn_credit_event = QPushButton("Credit Event")
        btn_credit_event.clicked.connect(self.credit_event)
        btn_credit_event.setMinimumHeight(dp(40))
        btn_credit_event.setStyleSheet(f"""
            background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {COLOR_BLUE_VIOLET},
                stop:1 {QColor(COLOR_BLUE_VIOLET).darker(120).name()});
            color: white;
            border: 1px solid {QColor(COLOR_BLUE_VIOLET).darker(130).name()};
            border-radius: {dp(5)}px;
            font-size: {dp(15)}px;
            font-weight: bold;
        """)

        btn_credit_all = QPushButton("Credit All")
        btn_credit_all.clicked.connect(self.credit_all_events)
        btn_credit_all.setMinimumHeight(dp(40))
        btn_credit_all.setStyleSheet(f"""
            background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {COLOR_VISTA_BLUE},
                stop:1 {QColor(COLOR_VISTA_BLUE).darker(120).name()});
            color: white;
            border: 1px solid {QColor(COLOR_VISTA_BLUE).darker(130).name()};
            border-radius: {dp(5)}px;
            font-size: {dp(15)}px;
            font-weight: bold;
        """)

        btn_paste_adid = QPushButton("Paste ADID")
        btn_paste_adid.clicked.connect(self.paste_adid)
        btn_paste_adid.setMinimumHeight(dp(40))
        btn_paste_adid.setStyleSheet(f"""
            background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {COLOR_CERULEAN},
                stop:1 {QColor(COLOR_CERULEAN).darker(120).name()});
            color: white;
            border: 1px solid {QColor(COLOR_CERULEAN).darker(130).name()};
            border-radius: {dp(5)}px;
            font-size: {dp(15)}px;
            font-weight: bold;
        """)

        btn_clear = QPushButton("Clear")
        btn_clear.clicked.connect(self.clear_fields)
        btn_clear.setMinimumHeight(dp(40))
        btn_clear.setStyleSheet(f"""
            background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {COLOR_MIDNIGHT_GREEN},
                stop:1 {QColor(COLOR_MIDNIGHT_GREEN).darker(120).name()});
            color: white;
            border: 1px solid {QColor(COLOR_MIDNIGHT_GREEN).darker(130).name()};
            border-radius: {dp(5)}px;
            font-size: {dp(15)}px;
            font-weight: bold;
        """)

        # Add buttons to layout with equal spacing
        buttons_box.addWidget(btn_credit_event)
        buttons_box.addWidget(btn_credit_all)
        buttons_box.addWidget(btn_paste_adid)
        buttons_box.addWidget(btn_clear)

        form_layout.addLayout(buttons_box)
        # --- End of form elements ---

        # Add the form container directly to the content layout without a scroll area
        content_layout.addWidget(form_container_widget, 1)

        # Response Log Area (outside the scroll area)
        self.response_log_container = RotatingBackgroundBox()
        response_area_layout = QVBoxLayout(self.response_log_container)
        response_area_layout.setContentsMargins(dp(10),dp(10),dp(10),dp(10))

        response_title = QLabel("Response Log")
        response_title.setFont(QFont("Arial", dp(14), QFont.Weight.Bold))
        response_area_layout.addWidget(response_title)

        self.response_text = QTextEdit()
        self.response_text.setReadOnly(True)
        self.response_text.setObjectName("ResponseLog")
        self.response_text.setPlaceholderText("Responses will appear here...")
        self.response_text.setAcceptRichText(True)  # Enable rich text interpretation
        self.response_text.setTextInteractionFlags(Qt.TextInteractionFlag.TextBrowserInteraction)  # Make links clickable
        response_area_layout.addWidget(self.response_text)

        # Add the response log container to the content layout
        # Give it a larger stretch factor to take up remaining space
        content_layout.addWidget(self.response_log_container, 3)

        # Add the content area to the main layout
        main_layout.addWidget(content_area, 1)  # Stretch factor 1 to take up remaining space

    def set_background(self):
        """Called when the screen becomes active to set a new background."""
        if hasattr(self, 'response_log_container') and isinstance(self.response_log_container, (RotatingBackgroundBox, ImageBackgroundBox)):
            self.response_log_container.set_random_background()

    # --- Rest of S2SScreen methods (refresh_spinners, on_game_selected, etc.) remain the same ---
    # ... (Methods from previous version) ...
    def refresh_spinners_from_cache(self, _=None): # Unused parameter for signal compatibility
        print("S2SScreen: Refreshing spinners.")
        self.game_names_list = game_data_cache.get_all_app_names()

        current_game_selection = self.game_spinner.currentText()
        self.game_spinner.blockSignals(True)
        self.game_spinner.clear()
        if self.game_names_list:
            self.game_spinner.addItems(self.game_names_list)
            # Try to restore previous selection
            if current_game_selection and current_game_selection in self.game_names_list:
                self.game_spinner.setCurrentText(current_game_selection)
            # If no previous selection or it's invalid, set to first item or stored current_game
            elif self.current_game and self.current_game in self.game_names_list:
                 self.game_spinner.setCurrentText(self.current_game)
            else:
                 # Ensure list is not empty before accessing index 0
                 if self.game_names_list:
                     self.current_game = self.game_names_list[0]
                     self.game_spinner.setCurrentIndex(0) # Set to first item
                 else:
                      self.current_game = "" # Handle empty list case
        else:
            self.current_game = ""
        self.game_spinner.blockSignals(False)

        # Trigger event update based on the final game selection
        self.on_game_selected(self.game_spinner.currentText())


    def on_game_selected(self, text):
        self.current_game = text
        # Store current event selection before clearing
        current_event_selection = self.event_spinner.currentText()

        self.event_spinner.blockSignals(True)
        self.event_spinner.clear()
        self.event_names_list = [] # Reset event list

        if self.current_game:
            events_dict = game_data_cache.get_event_tokens_for_app(self.current_game)
            self.event_names_list = sorted(list(events_dict.keys()))
            if self.event_names_list:
                self.event_spinner.addItems(self.event_names_list)
                # Try to restore previous event selection if it's valid for the new game
                if current_event_selection and current_event_selection in self.event_names_list:
                    self.event_spinner.setCurrentText(current_event_selection)
                    self.current_event = current_event_selection
                else:
                    # Default to the first event if previous selection is invalid or none
                    self.current_event = self.event_names_list[0]
                    self.event_spinner.setCurrentIndex(0)
            else:
                self.current_event = "" # No events for this game
        else:
            self.current_event = "" # No game selected

        self.event_spinner.blockSignals(False)
        print(f"S2SScreen: Game '{text}' selected. Events: {self.event_names_list}. Current Event: {self.current_event}")


    def on_request_type_selected(self, text):
        self.current_request_type = text
        print(f"S2SScreen: Request type selected: {self.current_request_type}")

    def on_platform_selected(self, text):
        self.current_platform = text
        print(f"S2SScreen: Platform selected: {self.current_platform}")

        # Update the label and placeholder text based on platform
        if text == "Apple":
            self.device_id_label.setText("IDFA:")
            self.gps_adid_input.setPlaceholderText("Enter IDFA")
        else:  # Android
            self.device_id_label.setText("GPS ADID:")
            self.gps_adid_input.setPlaceholderText("Enter GPS ADID")

    def validate_inputs(self):
        if not self.gps_adid_input.text().strip():
            device_id_type = "IDFA" if self.current_platform == "Apple" else "GPS ADID"
            self.response_text.setHtml(f"Please enter a {device_id_type}.")
            self.app.show_info_popup("Input Error", f"{device_id_type} cannot be empty.")
            return False
        if not self.current_game:
            self.response_text.setHtml("Please select a game.")
            self.app.show_info_popup("Input Error", "No game selected.")
            return False
        return True

    def _perform_s2s_request(self, device_id, event_token, app_token):
        request_type_to_use = self.current_request_type
        response_summary = ""
        success = False  # Track if the request was successful
        verify_ssl = True  # Enable SSL verification

        # Determine which device ID parameter to use based on platform
        device_id_param = 'idfa' if self.current_platform == "Apple" else 'gps_adid'

        if "GET" in request_type_to_use:
            url = "https://s2s.adjust.com/event"
            params = {'s2s': '1', 'app_token': app_token, 'event_token': event_token, device_id_param: device_id}

            # Set appropriate user-agent based on platform
            if self.current_platform == "Apple":
                headers = {'client-sdk': 'ios5.1.0', 'user-agent': 'CFNetwork/1220.1 Darwin/20.3.0'}
            else:
                headers = {'client-sdk': 'android5.1.0', 'user-agent': 'Dalvik/2.1.0 (Linux; U; Android 10; K) Build/MMB29M'}
            try:
                response = requests.get(url, headers=headers, params=params, timeout=15, verify=verify_ssl)
                # Don't use raise_for_status() here so we can handle non-200 responses more gracefully

                # Check if the response was successful (200-299)
                if 200 <= response.status_code < 300:
                    success = True
                else:
                    success = False

                # Just return the response body for display in the response log
                response_summary = response.text
            except requests.exceptions.RequestException as e:
                success = False  # Explicitly mark as failed

                # Just return the error response body if available
                if hasattr(e, 'response') and e.response:
                    response_obj = e.response
                    response_summary = response_obj.text
                else:
                    # If no response object, just return the error message
                    response_summary = f"Error: {e}"
        elif "POST" in request_type_to_use:
            url = "https://app.adjust.com/event"

            # Set appropriate headers based on platform
            if self.current_platform == "Apple":
                headers = {'client-sdk': 'ios5.1.0', 'content-type': 'application/x-www-form-urlencoded', 'user-agent': 'CFNetwork/1220.1 Darwin/20.3.0'}
                # Simplified payload for iOS
                payload = {
                    'idfa': device_id, 'environment': 'production',
                    'idfv': str(uuid.uuid4()), 'event_token': event_token, 'app_token': app_token
                }
            else:  # Android
                headers = {'client-sdk': 'android5.1.0', 'content-type': 'application/x-www-form-urlencoded', 'user-agent': 'Dalvik/2.1.0 (Linux; U; Android 10; K) Build/MMB29M'}
                # Simplified payload for Android
                payload = {
                    'google_app_set_id': str(uuid.uuid4()), 'gps_adid': device_id, 'environment': 'production',
                    'android_uuid': str(uuid.uuid4()), 'event_token': event_token, 'app_token': app_token
                }
            try:
                response = requests.post(url, headers=headers, data=payload, timeout=15, verify=verify_ssl)
                # Don't use raise_for_status() here so we can handle non-200 responses more gracefully

                # Check if the response was successful (200-299)
                if 200 <= response.status_code < 300:
                    success = True
                else:
                    success = False

                # Just return the response body for display in the response log
                response_summary = response.text
            except requests.exceptions.RequestException as e:
                success = False  # Explicitly mark as failed

                # Just return the error response body if available
                if hasattr(e, 'response') and e.response:
                    response_obj = e.response
                    response_summary = response_obj.text
                else:
                    # If no response object, just return the error message
                    response_summary = f"Error: {e}"
        else:
            response_summary = f"Error: Unknown request type '{request_type_to_use}' selected."

        # Return both the success status and the response text
        return success, response_summary

    def _credit_event_task(self, selected_event_name):
        gps_adid = self.gps_adid_input.text().strip()
        current_game_for_thread = self.current_game

        # Cannot use progress.emit directly, we'll handle progress updates differently

        app_details = game_data_cache.get_app_details(current_game_for_thread)
        if not app_details: return "Error: Could not find app details."

        app_token = app_details.get("app_token")
        event_token = app_details.get("events", {}).get(selected_event_name)
        if not app_token or not event_token: return f"Error: Missing app_token or event_token for {current_game_for_thread} - {selected_event_name}"

        success, response_text = self._perform_s2s_request(gps_adid, event_token, app_token)

        # Format the header with success/failure status
        status_text = "SUCCESS" if success else "FAIL"
        status_color = "#00CC00" if success else "#FF0000" # Brighter colors for better visibility

        # Enhanced header with larger, bolder text for status and proper HTML line breaks
        header = f"=============================================<br>Event: {selected_event_name} (Token: {event_token}) - <span style='color:{status_color}; font-size:120%; font-weight:900;'>{status_text}</span><br>============================================="

        # If the event was successfully credited, add it to the history
        if success:
            history_data_cache.add_event(
                game_name=current_game_for_thread,
                event_name=selected_event_name,
                gps_adid=gps_adid
            )
            print(f"Added event to history: {current_game_for_thread} - {selected_event_name} - {gps_adid}")

        # Display the raw response text without any additional formatting
        # This ensures the exact response from the request is displayed
        return f"{header}<br><br>Type: {self.current_request_type}<br>{response_text}"

    def credit_event(self):
        if not self.validate_inputs(): return
        selected_event = self.event_spinner.currentText()
        if not selected_event or selected_event not in self.event_names_list:
            self.response_text.setHtml("Please select a valid event.")
            self.app.show_info_popup("Input Error", "No event selected or event is invalid.")
            return

        self.app.show_loading_dialog("Processing...")
        self.worker_thread = Worker(self._credit_event_task, selected_event)
        self.worker_thread.result.connect(self.update_response_on_main_thread)
        self.worker_thread.finished.connect(self.app.hide_loading_dialog)
        self.worker_thread.error.connect(lambda err: self.update_response_on_main_thread(f"Error: {err}"))
        self.worker_thread.progress.connect(self.app.update_loading_message)
        thread = threading.Thread(target=self.worker_thread.run)
        thread.start()


    def _credit_all_events_task(self):
        gps_adid = self.gps_adid_input.text().strip()
        current_game_for_thread = self.current_game

        # Cannot use progress.emit directly, we'll handle progress updates differently

        app_details = game_data_cache.get_app_details(current_game_for_thread)
        if not app_details: return "Error: Could not find app details."

        app_token = app_details.get("app_token")
        events_to_credit = app_details.get("events", {})
        if not app_token or not events_to_credit: return f"Error: Missing app_token or events for {current_game_for_thread}"

        all_responses = [f"<b>Batch Type: {self.current_request_type}</b>"]
        total_events = len(events_to_credit)
        successful_events = 0

        # Process each event
        for event_name, event_token in events_to_credit.items():

            # Get the response for this event
            success, response_text = self._perform_s2s_request(gps_adid, event_token, app_token)

            # Track successful events
            if success:
                successful_events += 1
                # Add to history
                history_data_cache.add_event(
                    game_name=current_game_for_thread,
                    event_name=event_name,
                    gps_adid=gps_adid
                )
                print(f"Added event to history: {current_game_for_thread} - {event_name} - {gps_adid}")

            # Format the header with success/failure status
            status_text = "SUCCESS" if success else "FAIL"
            status_color = "#00CC00" if success else "#FF0000" # Brighter colors for better visibility

            # Add a clear separator for each event with status - enhanced formatting with HTML
            event_header = f"<br><br>=============================================<br>Event: {event_name} (Token: {event_token}) - <span style='color:{status_color}; font-size:120%; font-weight:900;'>{status_text}</span><br>=============================================<br>"
            all_responses.append(event_header)

            # Display the raw response text without any additional formatting
            # This ensures the exact response from the request is displayed
            all_responses.append(response_text)

            time.sleep(0.1)

        # Calculate success rate
        success_rate = (successful_events / total_events) * 100 if total_events > 0 else 0

        # Add summary section at the end with enhanced formatting
        summary_text = "CRITICAL SUCCESS" if success_rate == 100 else "PARTIAL SUCCESS" if success_rate > 0 else "CRITICAL FAILURE"

        # Simple summary with clear information
        summary = f"""<br><br>
==================================================
{summary_text}! {success_rate:.1f}% SUCCESS IN CREDITING
{current_game_for_thread}
{successful_events} of {total_events} events successfully credited
=================================================="""
        all_responses.append(summary)

        # Join all responses and return as HTML
        return "".join(all_responses)

    def credit_all_events(self):
        if not self.validate_inputs(): return
        self.app.show_loading_dialog("Processing All Events...")
        self.worker_thread = Worker(self._credit_all_events_task)
        self.worker_thread.result.connect(self.update_response_on_main_thread)
        self.worker_thread.finished.connect(self.app.hide_loading_dialog)
        self.worker_thread.error.connect(lambda err: self.update_response_on_main_thread(f"Error: {err}"))
        self.worker_thread.progress.connect(self.app.update_loading_message)
        thread = threading.Thread(target=self.worker_thread.run)
        thread.start()


    def update_response_on_main_thread(self, text):
        # Use setHtml to render HTML content for headers and summaries
        # while preserving the raw response text
        self.response_text.setHtml(text)

    def paste_adid(self):
        self.gps_adid_input.setText(QApplication.clipboard().text())

    def clear_fields(self):
        self.gps_adid_input.clear()
        self.response_text.clear()
        if self.game_names_list:
            self.game_spinner.setCurrentIndex(0)
        self.request_type_spinner.setCurrentIndex(0)
        self.platform_spinner.setCurrentIndex(0)  # Reset to Android

        # Reset the label and placeholder text to Android defaults
        self.device_id_label.setText("GPS ADID:")
        self.gps_adid_input.setPlaceholderText("Enter GPS ADID")

# Conversion Screen
class ConversionScreen(BaseScreenWidget):
    screen_name_attr = "conversion_screen"
    def __init__(self, parent_app, parent=None):
        super().__init__(parent)
        self.app = parent_app
        self.worker_thread = None # Add worker thread reference
        self.conv_worker = None # Add worker for conversion step
        self.current_mode = "standard"  # "standard" or "redirect_chain"

        # Initialize conversion method templates from thegrail file
        self.conversion_templates = [
            {"name": "Default (tp88trk)", "url": "https://www.tp88trk.com/sdk/conversion?effp={transaction_id}&sec_ch_ua_platform=Android&transaction_id={transaction_id}&oid=878&aid=611&event_source_url={source_url}"},
            {"name": "p.ashx Format", "url": "http://{domain}/p.ashx?o={offer_id}&t={transaction_id}"},
            {"name": "aff_lsr Format", "url": "http://{domain}/aff_lsr?transaction_id={transaction_id}"},
            {"name": "pixel Format", "url": "http://{domain}/pixel?cid={offer_id}&refid={transaction_id}"},
            {"name": "postback Format", "url": "https://{domain}/postback?clickid={transaction_id}"},
            {"name": "conv Format", "url": "https://{domain}/conv?clickid={transaction_id}"},
            {"name": "acquisition Format", "url": "https://postback.exmox.net/acquisition?click_id={transaction_id}&security_token={security_token}&goal_value={goal_value}"},
            {"name": "Adcash", "url": "https://track.adcash.com/postback?aid={offer_id}&transaction_id={transaction_id}"},
            {"name": "Adgatemedia", "url": "https://adgatemedia.com/api/v1/earn/convert?transaction_id={transaction_id}&offer_id={offer_id}"},
            {"name": "Adscend", "url": "https://adscendmedia.com/postback.php?transaction_id={transaction_id}&offer_id={offer_id}"},
            {"name": "Adworkmedia", "url": "https://adworkmedia.com/postback?transaction_id={transaction_id}&offer_id={offer_id}"},
            {"name": "Ayetstudios", "url": "https://www.ayetstudios.com/offers/s2s/conversion?transaction_id={transaction_id}&offer_id={offer_id}"},
            {"name": "CPAlead", "url": "https://cpalead.com/postback?transaction_id={transaction_id}&offer_id={offer_id}"},
            {"name": "Fyber", "url": "https://api.fyber.com/postback?transaction_id={transaction_id}&offer_id={offer_id}"},
            {"name": "Ironsource", "url": "https://track.ironsrc.com/postback?transaction_id={transaction_id}&offer_id={offer_id}"},
            {"name": "Kiwiwall", "url": "https://www.kiwiwall.com/postback?transaction_id={transaction_id}&offer_id={offer_id}"},
            {"name": "Monlix", "url": "https://offers.monlix.com/api/v1/conversion?transaction_id={transaction_id}&offer_id={offer_id}"},
            {"name": "Offertoro", "url": "https://www.offertoro.com/api/postback?transaction_id={transaction_id}&offer_id={offer_id}"},
            {"name": "Personaly", "url": "https://api.persona.ly/v1/postback?transaction_id={transaction_id}&offer_id={offer_id}"},
            {"name": "Pollfish", "url": "https://www.pollfish.com/api/v1/conversion?transaction_id={transaction_id}&offer_id={offer_id}"},
            {"name": "Tapjoy", "url": "https://tapjoy.com/postback?transaction_id={transaction_id}&offer_id={offer_id}"},
            {"name": "Wannads", "url": "https://wannads.com/postback?transaction_id={transaction_id}&offer_id={offer_id}"},
            {"name": "Custom Format", "url": "https://{domain}/{transaction_id}"}
        ]

        self._build_ui()

    def _build_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(dp(20), dp(20), dp(20), dp(20))
        main_layout.setSpacing(dp(15))

        title = QLabel("Web Conversion")
        title.setObjectName("ScreenTitleLabel")
        main_layout.addWidget(title)

        # Mode selection buttons
        mode_box = QHBoxLayout()
        self.standard_mode_btn = QRadioButton("Standard Mode")
        self.standard_mode_btn.setChecked(True)
        self.standard_mode_btn.toggled.connect(self.on_mode_changed)

        self.redirect_chain_mode_btn = QRadioButton("Redirect Chain Mode")
        self.redirect_chain_mode_btn.toggled.connect(self.on_mode_changed)

        mode_box.addWidget(self.standard_mode_btn)
        mode_box.addWidget(self.redirect_chain_mode_btn)
        mode_box.addStretch()
        main_layout.addLayout(mode_box)

        # Create a scroll area for the form
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)

        # Container widget for the form
        form_container = QWidget()
        form_layout = QVBoxLayout(form_container)
        form_layout.setSpacing(dp(10))

        # Standard mode container
        self.standard_mode_container = QWidget()
        standard_layout = QVBoxLayout(self.standard_mode_container)
        standard_layout.setContentsMargins(0, 0, 0, 0)
        standard_layout.setSpacing(dp(10))

        # URL Input for standard mode
        url_box = QHBoxLayout()
        url_box.addWidget(QLabel("Offer URL:"))
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("Enter offer URL")
        url_box.addWidget(self.url_input, 1)
        standard_layout.addLayout(url_box)

        # Redirect chain mode container
        self.redirect_chain_container = QWidget()
        redirect_chain_layout = QVBoxLayout(self.redirect_chain_container)
        redirect_chain_layout.setContentsMargins(0, 0, 0, 0)
        redirect_chain_layout.setSpacing(dp(10))

        # Redirect chain input
        redirect_chain_box = QVBoxLayout()
        redirect_chain_label_box = QHBoxLayout()
        redirect_chain_label_box.addWidget(QLabel("Paste Redirect Chain:"))

        # Add paste button for redirect chain
        btn_paste_chain = QPushButton("Paste")
        btn_paste_chain.clicked.connect(self.paste_url)
        redirect_chain_label_box.addWidget(btn_paste_chain)
        redirect_chain_label_box.addStretch()

        redirect_chain_box.addLayout(redirect_chain_label_box)

        self.redirect_chain_input = QTextEdit()
        self.redirect_chain_input.setPlaceholderText("Paste redirect chain data with columns: Status Code, URL, IP, Page Type, Redirect Type, Redirect URL")
        self.redirect_chain_input.setMinimumHeight(dp(150))
        redirect_chain_box.addWidget(self.redirect_chain_input)

        # Add parse button
        btn_parse_chain = QPushButton("Parse Redirect Chain")
        btn_parse_chain.clicked.connect(self.parse_redirect_chain)
        btn_parse_chain.setStyleSheet(f"""
            background-color: {COLOR_BLUE_VIOLET};
            color: white;
            font-weight: bold;
            font-size: {dp(14)}px;
            padding: {dp(10)}px {dp(15)}px;
            border-radius: {dp(8)}px;
        """)
        btn_parse_chain.setMinimumHeight(dp(45))
        redirect_chain_box.addWidget(btn_parse_chain)

        redirect_chain_layout.addLayout(redirect_chain_box)

        # Add both mode containers to the form layout
        form_layout.addWidget(self.standard_mode_container)
        form_layout.addWidget(self.redirect_chain_container)
        self.redirect_chain_container.setVisible(False)  # Initially hidden

        # Conversion Method Dropdown
        method_box = QHBoxLayout()
        method_box.addWidget(QLabel("Conversion Method:"))
        self.method_dropdown = QComboBox()
        for template in self.conversion_templates:
            self.method_dropdown.addItem(template["name"])
        self.method_dropdown.currentIndexChanged.connect(self.on_method_changed)
        method_box.addWidget(self.method_dropdown, 1)
        form_layout.addLayout(method_box)

        # Transaction ID Input (for manual override)
        transaction_box = QHBoxLayout()
        transaction_box.addWidget(QLabel("Transaction ID:"))
        self.transaction_input = QLineEdit()
        self.transaction_input.setPlaceholderText("Auto-extracted or enter manually")
        transaction_box.addWidget(self.transaction_input, 1)
        form_layout.addLayout(transaction_box)

        # Domain Input (for templates that need it)
        domain_box = QHBoxLayout()
        domain_box.addWidget(QLabel("Domain:"))
        self.domain_input = QLineEdit()
        self.domain_input.setPlaceholderText("Domain for conversion URL (e.g., usmtrk.com)")
        domain_box.addWidget(self.domain_input, 1)
        form_layout.addLayout(domain_box)

        # Offer ID Input (for templates that need it)
        offer_box = QHBoxLayout()
        offer_box.addWidget(QLabel("Offer ID:"))
        self.offer_input = QLineEdit()
        self.offer_input.setPlaceholderText("Offer ID (e.g., 13175)")
        offer_box.addWidget(self.offer_input, 1)
        form_layout.addLayout(offer_box)

        # Security Token Input (for templates that need it)
        security_box = QHBoxLayout()
        security_box.addWidget(QLabel("Security Token:"))
        self.security_input = QLineEdit()
        self.security_input.setPlaceholderText("Security token (if required)")
        security_box.addWidget(self.security_input, 1)
        form_layout.addLayout(security_box)

        # Goal Value Input (for templates that need it)
        goal_box = QHBoxLayout()
        goal_box.addWidget(QLabel("Goal Value:"))
        self.goal_input = QLineEdit()
        self.goal_input.setPlaceholderText("Goal value (if required)")
        goal_box.addWidget(self.goal_input, 1)
        form_layout.addLayout(goal_box)

        # Action Buttons
        buttons_box = QHBoxLayout()
        btn_process = QPushButton("Process URL")
        btn_process.clicked.connect(self.process_urls)
        btn_process.setStyleSheet(f"""
            background-color: {COLOR_BLUE_VIOLET};
            color: white;
            font-weight: bold;
            font-size: {dp(14)}px;
            padding: {dp(10)}px {dp(15)}px;
            border-radius: {dp(8)}px;
        """)
        btn_process.setMinimumHeight(dp(45))

        btn_paste = QPushButton("Paste")
        btn_paste.clicked.connect(self.paste_url)

        btn_clear = QPushButton("Clear")
        btn_clear.clicked.connect(self.clear_url)

        buttons_box.addWidget(btn_process)
        buttons_box.addWidget(btn_paste)
        buttons_box.addWidget(btn_clear)
        form_layout.addLayout(buttons_box)

        # Add the form container to the scroll area
        scroll_area.setWidget(form_container)
        main_layout.addWidget(scroll_area)

        # Response Log Area
        self.response_log_container = RotatingBackgroundBox()
        response_layout = QVBoxLayout(self.response_log_container)
        response_layout.setContentsMargins(dp(10), dp(10), dp(10), dp(10))

        response_header = QLabel("Response Log")
        response_header.setFont(QFont("Arial", dp(14), QFont.Weight.Bold))
        response_layout.addWidget(response_header)

        self.response_text = QTextEdit()
        self.response_text.setReadOnly(True)
        self.response_text.setObjectName("ResponseLog")
        self.response_text.setPlaceholderText("Response will appear here...")
        response_layout.addWidget(self.response_text)

        main_layout.addWidget(self.response_log_container, 1) # Response log takes expanding space

        # Initialize field visibility based on selected method
        self.on_method_changed(0)

    def set_background(self):
        """Called when the screen becomes active to set a new background."""
        if hasattr(self, 'response_log_container') and isinstance(self.response_log_container, (RotatingBackgroundBox, ImageBackgroundBox)):
            self.response_log_container.set_random_background()

    # --- Rest of ConversionScreen methods ---
    # ... (Methods _follow_redirect_task, _process_urls_after_redirect, process_urls, etc. remain the same) ...
    def on_method_changed(self, index):
        """Update field visibility based on selected conversion method"""
        try:
            template = self.conversion_templates[index]
            template_url = template["url"]
            print(f"Selected conversion method: {template['name']}")
            print(f"Template URL: {template_url}")

            # Show/hide fields based on template parameters
            show_domain = '{domain}' in template_url
            show_offer = '{offer_id}' in template_url
            show_security = '{security_token}' in template_url
            show_goal = '{goal_value}' in template_url

            print(f"Showing fields - Domain: {show_domain}, Offer: {show_offer}, Security: {show_security}, Goal: {show_goal}")

            # Set visibility for the input fields
            self.domain_input.setVisible(show_domain)
            self.offer_input.setVisible(show_offer)
            self.security_input.setVisible(show_security)
            self.goal_input.setVisible(show_goal)

            # Get the parent layout for each input field and try to get the label widgets
            try:
                domain_layout = self.domain_input.parentWidget().layout()
                if domain_layout and domain_layout.itemAt(0) and domain_layout.itemAt(0).widget():
                    domain_label = domain_layout.itemAt(0).widget()
                    domain_label.setVisible(show_domain)
            except Exception as e:
                print(f"Error setting domain label visibility: {e}")

            try:
                offer_layout = self.offer_input.parentWidget().layout()
                if offer_layout and offer_layout.itemAt(0) and offer_layout.itemAt(0).widget():
                    offer_label = offer_layout.itemAt(0).widget()
                    offer_label.setVisible(show_offer)
            except Exception as e:
                print(f"Error setting offer label visibility: {e}")

            try:
                security_layout = self.security_input.parentWidget().layout()
                if security_layout and security_layout.itemAt(0) and security_layout.itemAt(0).widget():
                    security_label = security_layout.itemAt(0).widget()
                    security_label.setVisible(show_security)
            except Exception as e:
                print(f"Error setting security label visibility: {e}")

            try:
                goal_layout = self.goal_input.parentWidget().layout()
                if goal_layout and goal_layout.itemAt(0) and goal_layout.itemAt(0).widget():
                    goal_label = goal_layout.itemAt(0).widget()
                    goal_label.setVisible(show_goal)
            except Exception as e:
                print(f"Error setting goal label visibility: {e}")

            print("Field visibility updated successfully")
        except Exception as e:
            print(f"Error in on_method_changed: {e}")
            import traceback
            traceback.print_exc()

    def _follow_redirect_task(self, url):
        final_url, error_message = None, None
        verify_ssl = True # Enable SSL verification
        try:
            headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}
            response = requests.get(url, allow_redirects=True, verify=verify_ssl, timeout=20, headers=headers)
            response.raise_for_status()
            final_url = response.url
        except requests.exceptions.RequestException as e:
            error_message = f"Error following redirect: {e}"
        except Exception as e:
             error_message = f"Unexpected error during redirect: {e}"
        return final_url, error_message

    def _process_urls_after_redirect(self, result_tuple):
        final_redirect_url, error_message = result_tuple

        if error_message:
            self.response_text.setHtml(error_message)
            self.app.hide_loading_dialog()
            return
        if not final_redirect_url:
            self.response_text.setHtml("Error: Could not get final URL after redirects.")
            self.app.hide_loading_dialog()
            return

        original_url = self.url_input.text().strip()
        parsed_url = urlparse(final_redirect_url)
        query_params = parse_qs(parsed_url.query)

        # Check for manually entered transaction ID first
        transaction_id = self.transaction_input.text().strip()

        # If no manual transaction ID, try to extract it from the URL
        if not transaction_id:
            possible_ids = ['transaction_id', 'click_id', 'subid', 'aff_sub', 'session_id', 'track_id', 'clickid', 'orderid', 'transactionid', 'utm_term', 'sub1', 'sub2', 'sub3']
            transaction_id = next((query_params.get(pid, [None])[0] for pid in possible_ids if query_params.get(pid, [None])[0]), None)

            # Also check for transaction_id in the cid parameter (common in some affiliate networks)
            if not transaction_id and 'cid' in query_params:
                cid_value = query_params['cid'][0]
                # Look for patterns like "transactionid:XXXXXX" in the cid parameter
                cid_match = re.search(r'transactionid:([a-zA-Z0-9_-]+)', cid_value)
                if cid_match:
                    transaction_id = cid_match.group(1)

            if not transaction_id: # Fallback regex
                match = re.search(r'[?&](?:transaction_id|click_id|afid|tid|pid|subid|aff_sub|id|tracking_id|session_id|track_id|clickid|orderid|transactionid)=([^&]+)', final_redirect_url, re.IGNORECASE)
                if match:
                    transaction_id = match.group(1)
                else:
                    # Look for transactionid: pattern in the URL (as seen in your example)
                    match_transaction = re.search(r'transactionid:([a-zA-Z0-9_-]+)', final_redirect_url)
                    if match_transaction:
                        transaction_id = match_transaction.group(1)
                    else:
                        match_generic = re.search(r'[?&]([^=]+)=([a-zA-Z0-9_-]{16,})', final_redirect_url)
                        if match_generic:
                            transaction_id = match_generic.group(2)

            # Update the transaction ID field with what we found
            if transaction_id:
                self.transaction_input.setText(transaction_id)

        if not transaction_id:
            self.response_text.setHtml(f"Error: No transaction_id or similar tracking ID found in redirected URL:<br>{final_redirect_url}<br><br>Please enter a transaction ID manually.")
            self.app.hide_loading_dialog()
            return

        # Get the selected conversion method template
        selected_index = self.method_dropdown.currentIndex()
        template = self.conversion_templates[selected_index]
        template_url = template["url"]

        # Get values for template parameters
        domain = self.domain_input.text().strip() or parsed_url.netloc
        offer_id = self.offer_input.text().strip() or "0"
        security_token = self.security_input.text().strip() or ""
        goal_value = self.goal_input.text().strip() or ""
        source_url = parsed_url.netloc

        # Build the conversion URL using the template
        try:
            final_conversion_url = template_url.format(
                transaction_id=transaction_id,
                domain=domain,
                offer_id=offer_id,
                security_token=security_token,
                goal_value=goal_value,
                source_url=source_url
            )
        except KeyError as e:
            self.response_text.setHtml(f"Error: Missing required parameter for template: {e}")
            self.app.hide_loading_dialog()
            return

        def _conversion_request_task():
            verify_ssl = True # Enable SSL verification
            try:
                conv_response = requests.get(final_conversion_url, timeout=15, verify=verify_ssl)
                conv_response.raise_for_status()
                # Format response with HTML line breaks for proper display
                response_text = conv_response.text.replace('\n', '<br>')
                return (True,
                        f"<b>Original URL:</b> {original_url}<br><br>"
                        f"<b>Redirected To:</b> {final_redirect_url}<br><br>"
                        f"<b>Extracted Transaction ID:</b> {transaction_id}<br><br>"
                        f"<b>Conversion Method:</b> {template['name']}<br><br>"
                        f"<b>Final Conversion URL Sent:</b><br>{final_conversion_url}<br><br>"
                        f"<b>Conversion Response Status:</b> {conv_response.status_code}<br><br>"
                        f"<b>Conversion Response Text:</b><br>{response_text}"
                       )
            except requests.exceptions.RequestException as e:
                return (False, f"<span style='color:#FF0000; font-weight:bold;'>Error during final conversion request:</span> {e}<br>URL Tried: {final_conversion_url}")
            except Exception as e:
                 return (False, f"<span style='color:#FF0000; font-weight:bold;'>Unexpected error during conversion request:</span> {e}")

        def _handle_conversion_result(conv_result_tuple):
            _, message = conv_result_tuple  # Using _ to indicate unused variable
            # Use setHtml instead of setText to properly render HTML content
            self.response_text.setHtml(message)
            self.app.hide_loading_dialog()

        self.app.update_loading_message("Sending Conversion Request...")
        self.conv_worker = Worker(_conversion_request_task)
        self.conv_worker.result.connect(_handle_conversion_result)
        self.conv_worker.error.connect(lambda err: (self.response_text.setHtml(f"Conversion Worker Error: {err}"), self.app.hide_loading_dialog()))
        self.conv_worker.finished.connect(self.app.hide_loading_dialog)
        thread = threading.Thread(target=self.conv_worker.run)
        thread.start()


    def process_urls(self):
        # Check if we're in redirect chain mode
        if self.current_mode == "redirect_chain":
            self.parse_redirect_chain()
            return

        # For standard mode, check if the URL might actually be a redirect chain
        url_1 = self.url_input.text().strip()
        if url_1:
            input_type = self.detect_input_type(url_1)
            if input_type == "redirect_chain":
                # Switch to redirect chain mode
                self.redirect_chain_mode_btn.setChecked(True)
                self.redirect_chain_input.setText(url_1)
                self.parse_redirect_chain()
                return

        # Standard mode processing

        # If we have a transaction ID but no URL, we can skip the redirect step
        if self.transaction_input.text().strip() and not url_1:
            # Check if we have all required fields for the selected template
            selected_index = self.method_dropdown.currentIndex()
            template = self.conversion_templates[selected_index]
            template_url = template["url"]

            missing_fields = []
            if '{domain}' in template_url and not self.domain_input.text().strip():
                missing_fields.append("Domain")
            if '{offer_id}' in template_url and not self.offer_input.text().strip():
                missing_fields.append("Offer ID")
            if '{security_token}' in template_url and not self.security_input.text().strip():
                missing_fields.append("Security Token")
            if '{goal_value}' in template_url and not self.goal_input.text().strip():
                missing_fields.append("Goal Value")

            if missing_fields:
                self.response_text.setHtml(f"Error: Missing required fields: {', '.join(missing_fields)}")
                self.app.show_info_popup("Input Error", f"Please fill in all required fields: {', '.join(missing_fields)}")
                return

            # Create a dummy redirect result with just the transaction ID
            self._process_urls_after_redirect((f"https://dummy.com/?transaction_id={self.transaction_input.text().strip()}", None))
            return

        # Normal flow - follow redirects first
        if not url_1:
            self.response_text.setHtml("Please enter the offer URL or a transaction ID.")
            self.app.show_info_popup("Input Error", "Offer URL cannot be empty when no transaction ID is provided.")
            return

        self.app.show_loading_dialog("Following Redirects...")
        self.worker_thread = Worker(self._follow_redirect_task, url_1)
        self.worker_thread.result.connect(self._process_urls_after_redirect)
        self.worker_thread.error.connect(lambda err: (self.response_text.setHtml(f"Redirect Worker Error: {err}"), self.app.hide_loading_dialog()))
        thread = threading.Thread(target=self.worker_thread.run)
        thread.start()


    def on_mode_changed(self, checked):
        """Handle mode change between standard and redirect chain mode"""
        if not checked:  # Only process when a button is checked, not when unchecked
            return

        if self.sender() == self.standard_mode_btn and checked:
            self.current_mode = "standard"
            self.standard_mode_container.setVisible(True)
            self.redirect_chain_container.setVisible(False)
        elif self.sender() == self.redirect_chain_mode_btn and checked:
            self.current_mode = "redirect_chain"
            self.standard_mode_container.setVisible(False)
            self.redirect_chain_container.setVisible(True)

    def parse_redirect_chain(self):
        """Parse the redirect chain data from the text input"""
        if self.current_mode != "redirect_chain":
            return

        redirect_chain_text = self.redirect_chain_input.toPlainText().strip()
        if not redirect_chain_text:
            self.response_text.setHtml("Please paste a redirect chain to parse.")
            return

        # Split the text into lines
        lines = redirect_chain_text.split('\n')
        redirect_chain = []

        # Check if this is a simple list of URLs (like "1ST LINK" followed by URL)
        simple_url_pattern = False
        urls_only = []

        for i, line in enumerate(lines):
            if not line.strip():
                continue

            # Check for patterns like "1ST LINK" or "2ND LINK" followed by a URL
            if i < len(lines) - 1 and ('LINK' in line.upper() or 'URL' in line.upper()):
                next_line = lines[i+1].strip()
                if next_line.startswith('http'):
                    simple_url_pattern = True
                    urls_only.append(next_line)

        # If we detected a simple list of URLs, process them
        if simple_url_pattern and urls_only:
            for i, url in enumerate(urls_only):
                if i < len(urls_only) - 1:
                    # For all but the last URL, the redirect URL is the next URL
                    redirect_chain.append({
                        'url': url,
                        'status_code': '302',  # Assume 302 redirect
                        'type': 'Redirect',
                        'ip': 'N/A',
                        'page_type': 'N/A',
                        'redirect_url': urls_only[i+1]
                    })
                else:
                    # For the last URL, the redirect URL is itself
                    redirect_chain.append({
                        'url': url,
                        'status_code': '200',  # Assume 200 OK
                        'type': 'Final',
                        'ip': 'N/A',
                        'page_type': 'N/A',
                        'redirect_url': url
                    })
        else:
            # Process each line in the standard format
            for line in lines:
                # Skip empty lines
                if not line.strip():
                    continue

                # Check if this is just a URL
                if line.strip().startswith('http'):
                    # If we only have one URL per line, add it to the chain
                    redirect_chain.append({
                        'url': line.strip(),
                        'status_code': 'N/A',
                        'type': 'Unknown',
                        'ip': 'N/A',
                        'page_type': 'N/A',
                        'redirect_url': line.strip()  # Same URL as redirect for now
                    })
                    continue

                # Split the line by tabs or multiple spaces
                parts = re.split(r'\t+|\s{2,}', line.strip())

                # Check if we have enough parts
                if len(parts) >= 6:
                    status_code = parts[0]
                    url = parts[1]
                    ip = parts[2]
                    page_type = parts[3]
                    redirect_type = parts[4]
                    redirect_url = parts[5]

                    # Add to redirect chain
                    redirect_chain.append({
                        'url': url,
                        'status_code': status_code,
                        'type': redirect_type,
                        'ip': ip,
                        'page_type': page_type,
                        'redirect_url': redirect_url
                    })

        if not redirect_chain:
            self.response_text.setHtml("Error: Could not parse redirect chain. Please check the format.")
            return

        # Display the parsed redirect chain
        response_text = ["<b>Parsed Redirect Chain:</b><br>"]

        for i, redirect in enumerate(redirect_chain):
            response_text.append(f"<b>{i+1}.</b> {redirect['url']}")
            response_text.append(f"   <b>Status:</b> {redirect['status_code']}")
            response_text.append(f"   <b>Type:</b> {redirect['type']}")
            response_text.append(f"   <b>IP:</b> {redirect['ip']}")
            response_text.append(f"   <b>Page Type:</b> {redirect['page_type']}")
            response_text.append(f"   <b>Redirect URL:</b> {redirect['redirect_url']}<br>")

        # Extract transaction ID from the last URL in the chain
        final_url = redirect_chain[-1]['redirect_url']
        parsed_url = urlparse(final_url)
        query_params = parse_qs(parsed_url.query)

        # Try to extract transaction ID
        transaction_id = None
        possible_ids = ['transaction_id', 'click_id', 'subid', 'aff_sub', 'session_id', 'track_id', 'clickid', 'orderid', 'transactionid', 'utm_term', 'sub1', 'sub2', 'sub3']
        transaction_id = next((query_params.get(pid, [None])[0] for pid in possible_ids if query_params.get(pid, [None])[0]), None)

        # Also check for transaction_id in the cid parameter (common in some affiliate networks)
        if not transaction_id and 'cid' in query_params:
            cid_value = query_params['cid'][0]
            # Look for patterns like "transactionid:XXXXXX" in the cid parameter
            cid_match = re.search(r'transactionid:([a-zA-Z0-9_-]+)', cid_value)
            if cid_match:
                transaction_id = cid_match.group(1)

        if not transaction_id:  # Fallback regex
            match = re.search(r'[?&](?:transaction_id|click_id|afid|tid|pid|subid|aff_sub|id|tracking_id|session_id|track_id|clickid|orderid|transactionid)=([^&]+)', final_url, re.IGNORECASE)
            if match:
                transaction_id = match.group(1)
            else:
                # Look for transactionid: pattern in the URL (as seen in your example)
                match_transaction = re.search(r'transactionid:([a-zA-Z0-9_-]+)', final_url)
                if match_transaction:
                    transaction_id = match_transaction.group(1)
                else:
                    match_generic = re.search(r'[?&]([^=]+)=([a-zA-Z0-9_-]{16,})', final_url)
                    if match_generic:
                        transaction_id = match_generic.group(2)

        # Update the transaction ID field with what we found
        if transaction_id:
            self.transaction_input.setText(transaction_id)
            response_text.append(f"<b>Extracted Transaction ID:</b> {transaction_id}")
        else:
            response_text.append("<b>No transaction ID found in the redirect chain.</b>")

        # Display the response
        self.response_text.setHtml("<br>".join(response_text))

        # Set the URL input to the first URL in the chain for compatibility with existing code
        if redirect_chain:
            self.url_input.setText(redirect_chain[0]['url'])

    def detect_input_type(self, text):
        """Detect if the input is a standard URL or a redirect chain"""
        if not text.strip():
            return "unknown"

        # Check if it looks like a redirect chain (multiple lines with status codes)
        lines = text.strip().split('\n')
        if len(lines) > 1:
            # Look for patterns that indicate a redirect chain
            # Check if first line has status code pattern (3 digits) and URL
            first_line = lines[0].strip()
            status_code_match = re.search(r'^\d{3}\s+https?://', first_line)
            if status_code_match:
                return "redirect_chain"

            # Check if it has tab-separated or multiple-space-separated columns
            parts = re.split(r'\t+|\s{2,}', first_line)
            if len(parts) >= 6 and parts[0].isdigit():
                return "redirect_chain"

        # If it's a single line and looks like a URL, it's probably a standard URL
        if len(lines) == 1 and ('http://' in text or 'https://' in text):
            return "standard"

        # If we have multiple lines but they don't match redirect chain format,
        # check if any line looks like a URL
        for line in lines:
            if 'http://' in line or 'https://' in line:
                return "standard"

        return "unknown"

    def paste_url(self):
        """Paste clipboard content and automatically detect the input type"""
        clipboard_text = QApplication.clipboard().text()
        if not clipboard_text.strip():
            return

        # Detect input type
        input_type = self.detect_input_type(clipboard_text)

        if input_type == "redirect_chain":
            # Switch to redirect chain mode if needed
            if self.current_mode != "redirect_chain":
                self.redirect_chain_mode_btn.setChecked(True)
            self.redirect_chain_input.setText(clipboard_text)
            # Automatically parse the redirect chain
            self.parse_redirect_chain()
        elif input_type == "standard":
            # Switch to standard mode if needed
            if self.current_mode != "standard":
                self.standard_mode_btn.setChecked(True)
            self.url_input.setText(clipboard_text)
        else:
            # If we can't determine the type, use the current mode
            if self.current_mode == "standard":
                self.url_input.setText(clipboard_text)
            else:
                self.redirect_chain_input.setText(clipboard_text)

    def clear_url(self):
        self.url_input.clear()
        self.transaction_input.clear()
        self.domain_input.clear()
        self.offer_input.clear()
        self.security_input.clear()
        self.goal_input.clear()
        self.redirect_chain_input.clear()
        self.response_text.clear()
        self.method_dropdown.setCurrentIndex(0)

# Token Extractor Screen
class TokenExtractorScreen(BaseScreenWidget):
    screen_name_attr = "extractor_screen"

    def __init__(self, parent_app, parent=None):
        super().__init__(parent)
        self.app = parent_app
        self.extracted_data_for_json = {} # Instance variable
        self.worker_thread = None # Add worker thread reference
        self._build_ui()

    def _build_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(dp(20), dp(20), dp(20), dp(20))
        main_layout.setSpacing(dp(15))

        title = QLabel("Token Extractor")
        title.setObjectName("ScreenTitleLabel")
        main_layout.addWidget(title)

        # Add subtitle to clarify iOS support
        subtitle = QLabel("Extract tokens from Android & iOS apps")
        subtitle.setStyleSheet(f"color: {COLOR_TEXT_LIGHT}; font-size: {dp(12)}px;")
        main_layout.addWidget(subtitle)

        # Form elements (no scroll needed for this simple form)
        form_layout = QVBoxLayout()
        form_layout.setSpacing(dp(10))

        url_box = QHBoxLayout()
        url_box.addWidget(QLabel("Adjust/App URL:"))
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("Enter Adjust URL, Play Store URL, or App Store URL")
        url_box.addWidget(self.url_input, 1)
        form_layout.addLayout(url_box)

        # Add optional app_token input field
        app_token_box = QHBoxLayout()
        app_token_box.addWidget(QLabel("App Token (Optional):"))
        self.app_token_input = QLineEdit()
        self.app_token_input.setPlaceholderText("Enter app_token manually (optional)")
        app_token_box.addWidget(self.app_token_input, 1)
        btn_paste_token = QPushButton("Paste")
        btn_paste_token.setFixedWidth(dp(80))
        btn_paste_token.clicked.connect(self.paste_token_action)
        app_token_box.addWidget(btn_paste_token)
        form_layout.addLayout(app_token_box)

        buttons_box1 = QHBoxLayout()
        btn_parse = QPushButton("Parse URL"); btn_parse.clicked.connect(self.parse_url_action)
        btn_paste = QPushButton("Paste"); btn_paste.clicked.connect(self.paste_url_action)
        btn_clear = QPushButton("Clear"); btn_clear.clicked.connect(self.clear_fields_action)
        buttons_box1.addWidget(btn_parse); buttons_box1.addWidget(btn_paste); buttons_box1.addWidget(btn_clear)
        form_layout.addLayout(buttons_box1)

        buttons_box2 = QHBoxLayout()
        btn_add_local = QPushButton("Add to Local Data"); btn_add_local.clicked.connect(self.add_to_local_data_action)
        btn_copy_json = QPushButton("Copy JSON"); btn_copy_json.clicked.connect(self.copy_json_action)
        buttons_box2.addWidget(btn_add_local); buttons_box2.addWidget(btn_copy_json)
        form_layout.addLayout(buttons_box2)

        main_layout.addLayout(form_layout) # Add form layout directly

        # App Info Display Area
        self.app_info_container = QFrame()
        self.app_info_container.setObjectName("AppInfoContainer")
        self.app_info_container.setStyleSheet(f"""
            QFrame#AppInfoContainer {{
                background-color: {COLOR_CARD_BG};
                border-radius: {dp(10)}px;
                border: 1px solid {QColor(COLOR_VISTA_BLUE).darker(150).name()};
                padding: {dp(10)}px;
                margin-bottom: {dp(10)}px;
            }}
        """)
        self.app_info_container.setFixedHeight(dp(100))  # Fixed height for app info
        self.app_info_container.setVisible(False)  # Initially hidden

        app_info_layout = QHBoxLayout(self.app_info_container)
        app_info_layout.setContentsMargins(dp(10), dp(10), dp(10), dp(10))

        # App Icon
        self.app_icon_label = QLabel()
        self.app_icon_label.setFixedSize(dp(80), dp(80))
        self.app_icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.app_icon_label.setStyleSheet(f"""
            background-color: transparent;
            border-radius: {dp(5)}px;
        """)
        app_info_layout.addWidget(self.app_icon_label)

        # App Details
        app_details_layout = QVBoxLayout()

        self.app_name_label = QLabel("App Name")
        self.app_name_label.setFont(QFont("Arial", dp(14), QFont.Weight.Bold))
        self.app_name_label.setStyleSheet(f"color: {COLOR_TEXT_LIGHT};")

        self.package_name_label = QLabel("Package Name")
        self.package_name_label.setFont(QFont("Arial", dp(10)))
        self.package_name_label.setStyleSheet(f"color: {COLOR_TEXT_LIGHT};")

        self.app_token_label = QLabel("App Token")
        self.app_token_label.setFont(QFont("Arial", dp(10)))
        self.app_token_label.setStyleSheet(f"color: {COLOR_TEXT_LIGHT};")

        app_details_layout.addWidget(self.app_name_label)
        app_details_layout.addWidget(self.package_name_label)
        app_details_layout.addWidget(self.app_token_label)

        app_info_layout.addLayout(app_details_layout, 1)  # Give it stretch factor

        main_layout.addWidget(self.app_info_container)

        # Response Log Area
        self.response_log_container = RotatingBackgroundBox()
        response_layout = QVBoxLayout(self.response_log_container)
        response_layout.setContentsMargins(dp(10), dp(10), dp(10), dp(10))

        results_header = QLabel("Extracted Data")
        results_header.setFont(QFont("Arial", dp(14), QFont.Weight.Bold))
        response_layout.addWidget(results_header)

        self.results_display = QTextEdit()
        self.results_display.setReadOnly(True)
        self.results_display.setObjectName("ResponseLog")
        self.results_display.setPlaceholderText("Extracted data will appear here...")
        response_layout.addWidget(self.results_display)

        main_layout.addWidget(self.response_log_container, 1) # Response log takes expanding space

    def set_background(self):
        """Called when the screen becomes active to set a new background."""
        if hasattr(self, 'response_log_container') and isinstance(self.response_log_container, (RotatingBackgroundBox, ImageBackgroundBox)):
            self.response_log_container.set_random_background()

    # --- Rest of TokenExtractorScreen methods ---
    def update_results_on_main(self, text_to_append=None, new_text=None):
        if new_text is not None: self.results_display.setText(new_text)
        elif text_to_append is not None: self.results_display.append(text_to_append)

    def _extract_event_tokens_from_url(self, url, task_extracted_data, final_log):
        """Enhanced event token extraction from URLs, especially adjust.com URLs"""
        try:
            parsed_url = urlparse(url)
            query_params = parse_qs(parsed_url.query)

            # Extract app token from adjust URLs
            if "app.adjust.com" in parsed_url.netloc or "s2s.adjust.com" in parsed_url.netloc:
                path_segments = parsed_url.path.strip('/').split('/')
                if path_segments and path_segments[0] and len(path_segments[0]) >= 8:
                    if task_extracted_data['app_token'] == 'apptoken_needed':
                        task_extracted_data['app_token'] = path_segments[0]
                        final_log.append(f"🎯 Extracted Adjust App Token: {path_segments[0]}")

            # Enhanced event token extraction
            event_tokens_found = 0

            # Look for event_callback parameters (like event_callback_nel2hu, event_callback_nnck2j, etc.)
            for param_name, param_values in query_params.items():
                if param_name.startswith('event_callback_') and param_values:
                    # Extract the token (6-character alphanumeric after event_callback_)
                    token = param_name.replace('event_callback_', '')

                    # Parse the callback URL to extract goal_id
                    callback_url = param_values[0]
                    goal_id = None

                    # Look for goal_id in the callback URL
                    goal_match = re.search(r'goal_id[=%](\d+)', callback_url)
                    if goal_match:
                        goal_id = goal_match.group(1)
                        event_name = f"goal_{goal_id}"
                        task_extracted_data['events'][event_name] = token
                        final_log.append(f"🎯 Found Event Token: {token} → Goal ID: {goal_id}")
                    else:
                        # If no goal_id, use the token as event name
                        event_name = f"event_{token}"
                        task_extracted_data['events'][event_name] = token
                        final_log.append(f"🎯 Found Event Token: {token}")

                    event_tokens_found += 1

            # Look for standard event tokens (6-character alphanumeric values)
            for param_name, param_values in query_params.items():
                if param_values and len(param_values[0]) == 6 and param_values[0].isalnum():
                    if not param_name.startswith('event_callback_'):  # Avoid duplicates
                        event_name = f"event_{param_name}"
                        task_extracted_data['events'][event_name] = param_values[0]
                        final_log.append(f"🎯 Found Standard Event Token: {param_values[0]} ({param_name})")
                        event_tokens_found += 1

            # Look for install_callback
            if 'install_callback' in query_params and query_params['install_callback']:
                callback_url = query_params['install_callback'][0]
                # Extract transaction_id from install callback
                transaction_match = re.search(r'transaction_id[=%]([^&]+)', callback_url)
                if transaction_match:
                    transaction_id = transaction_match.group(1)
                    task_extracted_data['events']['install'] = 'install'
                    final_log.append(f"📱 Found Install Callback with Transaction ID: {transaction_id}")

            if event_tokens_found > 0:
                final_log.append(f"✅ Total Event Tokens Found: {event_tokens_found}")
            else:
                final_log.append("ℹ️ No event tokens found in URL")

        except Exception as e:
            final_log.append(f"⚠️ Error extracting event tokens: {str(e)}")

    def _convert_market_to_play_store_url(self, market_url):
        """Convert market:// URL to Play Store URL with better handling"""
        try:
            # Parse the market URL
            parsed = urlparse(market_url)
            query_params = parse_qs(parsed.query)

            # Handle different market URL formats
            if parsed.path.startswith('/details'):
                # market://details?id=package.name
                package_id = query_params.get('id', [''])[0]
                if package_id:
                    play_store_url = f"https://play.google.com/store/apps/details?id={package_id}"
                    # Preserve referrer if present
                    if 'referrer' in query_params:
                        play_store_url += f"&referrer={query_params['referrer'][0]}"
                    return play_store_url

            elif parsed.path.startswith('/launch'):
                # market://launch?id=package.name&referrer=...
                package_id = query_params.get('id', [''])[0]
                if package_id:
                    play_store_url = f"https://play.google.com/store/apps/details?id={package_id}"
                    # Preserve referrer if present
                    if 'referrer' in query_params:
                        play_store_url += f"&referrer={query_params['referrer'][0]}"
                    return play_store_url

            # Fallback: try to extract package ID from anywhere in the URL
            package_match = re.search(r'id=([^&]+)', market_url)
            if package_match:
                package_id = package_match.group(1)
                play_store_url = f"https://play.google.com/store/apps/details?id={package_id}"

                # Try to preserve referrer
                referrer_match = re.search(r'referrer=([^&]+)', market_url)
                if referrer_match:
                    play_store_url += f"&referrer={referrer_match.group(1)}"

                return play_store_url

            # If all else fails, return the original URL
            return market_url

        except Exception as e:
            print(f"Error converting market URL: {e}")
            return market_url

    def _parse_url_task(self, url, manual_app_token=None):
        self.worker_thread.progress.emit(f"Starting to parse URL: {url[:100]}...")
        task_extracted_data = {
            'app_name': 'Unknown App',
            'package_name': 'PackageName_NotFound',
            'app_token': 'apptoken_needed',
            'playstore_url': '',
            'appstore_url': '',
            'app_id': '',  # For iOS apps
            'platform': 'Unknown',  # Android or iOS
            'icon_url': 'Icon_NotFound',
            'events': {}
        }
        final_log = []
        verify_ssl = True

        # Use manually entered app_token if provided
        if manual_app_token and manual_app_token.strip():
            task_extracted_data['app_token'] = manual_app_token.strip()
            final_log.append(f"✅ Using manually entered App Token: {manual_app_token.strip()}")

        # Enhanced event token extraction from the original URL first
        self._extract_event_tokens_from_url(url, task_extracted_data, final_log)

        try:
            # Check if the URL is a special protocol URL (iOS or Android market)
            if url.startswith('itms-apps://') or url.startswith('itms-appss://'):
                # Convert itms-apps:// or itms-appss:// to https://
                fixed_url = url.replace('itms-apps://', 'https://')
                fixed_url = fixed_url.replace('itms-appss://', 'https://')

                # Fix any escaped characters in the URL
                if '\\u003D' in fixed_url:
                    fixed_url = fixed_url.replace('\\u003D', '=')

                url = fixed_url
                final_log.append(f"Converted iOS special protocol URL to: {fixed_url}")
            elif url.startswith('market://'):
                # Enhanced market:// URL handling
                final_log.append(f"🤖 Processing Android market:// URL")

                # Extract referrer parameter first (often contains event tokens)
                referrer_match = re.search(r'referrer=([^&]+)', url)
                if referrer_match:
                    referrer_value = referrer_match.group(1)
                    try:
                        # URL decode the referrer value (multiple levels if needed)
                        decoded_referrer = html.unescape(referrer_value)
                        if '%' in decoded_referrer:
                            from urllib.parse import unquote
                            decoded_referrer = unquote(decoded_referrer)
                            # Sometimes needs double decoding
                            if '%' in decoded_referrer:
                                decoded_referrer = unquote(decoded_referrer)

                        final_log.append(f"📋 Extracted referrer: {decoded_referrer}")

                        # Look for adjust_external_click_id in the referrer
                        click_id_match = re.search(r'adjust_external_click_id[=%3D]([a-zA-Z0-9]+)', decoded_referrer)
                        if click_id_match:
                            click_id = click_id_match.group(1)
                            task_extracted_data['events']['external_click'] = click_id
                            final_log.append(f"🎯 Found external_click_id: {click_id}")

                        # Look for event tokens in the referrer
                        event_token_matches = re.findall(r'event_token[=%3D]([a-zA-Z0-9]{6})', decoded_referrer)
                        for token in event_token_matches:
                            task_extracted_data['events'][f'referrer_event_{token}'] = token
                            final_log.append(f"🎯 Found event token in referrer: {token}")

                        # Look for campaign info
                        utm_source_match = re.search(r'utm_source[=%3D]([^&%]+)', decoded_referrer)
                        utm_campaign_match = re.search(r'utm_campaign[=%3D]([^&%]+)', decoded_referrer)

                        if utm_source_match or utm_campaign_match:
                            source_info = []
                            if utm_source_match:
                                source = utm_source_match.group(1)
                                source_info.append(f"source: {source}")
                                task_extracted_data['utm_source'] = source

                            if utm_campaign_match:
                                campaign = utm_campaign_match.group(1)
                                source_info.append(f"campaign: {campaign}")
                                task_extracted_data['utm_campaign'] = campaign

                            final_log.append(f"📊 Campaign info: {', '.join(source_info)}")
                    except Exception as e:
                        final_log.append(f"⚠️ Error processing referrer: {e}")

                # Convert market:// to Play Store URL
                fixed_url = self._convert_market_to_play_store_url(url)
                url = fixed_url
                final_log.append(f"🔗 Converted to Play Store URL: {fixed_url}")

            # Determine if this is an App Store URL
            is_app_store = ('apps.apple.com' in url or
                           'itunes.apple.com' in url or
                           url.startswith('itms-apps://') or
                           url.startswith('itms-appss://'))

            if is_app_store:
                task_extracted_data['platform'] = 'iOS'
                final_log.append("Detected iOS App Store URL")
            elif 'play.google.com' in url:
                task_extracted_data['platform'] = 'Android'
                final_log.append("Detected Google Play Store URL")

            current_url = url
            if "play.google.com" not in url and "apps.apple.com" not in url and "itunes.apple.com" not in url:
                self.worker_thread.progress.emit("🔄 Following redirects...")
                # Use appropriate user agent based on detected platform
                if task_extracted_data['platform'] == 'iOS':
                    headers = {'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1 Mobile/15E148 Safari/604.1'}
                else:
                    headers = {'User-Agent': 'Mozilla/5.0 (Linux; Android 10; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.162 Mobile Safari/537.36'}

                # Enhanced redirect following with better tracking
                session = requests.Session()
                session.headers.update(headers)

                redirect_response = session.get(url, allow_redirects=True, timeout=15, verify=verify_ssl)
                redirect_response.raise_for_status()
                current_url = redirect_response.url

                # Track redirect chain
                if hasattr(redirect_response, 'history') and redirect_response.history:
                    final_log.append(f"🔄 Followed {len(redirect_response.history)} redirects:")
                    for i, resp in enumerate(redirect_response.history):
                        final_log.append(f"  {i+1}. {resp.status_code} → {resp.url}")
                    final_log.append(f"  Final: {current_url}")
                else:
                    final_log.append(f"🔗 Final URL: {current_url}")

                # Re-check platform after redirect
                if 'apps.apple.com' in current_url or 'itunes.apple.com' in current_url:
                    task_extracted_data['platform'] = 'iOS'
                    final_log.append("Redirected to iOS App Store URL")
                elif 'play.google.com' in current_url:
                    task_extracted_data['platform'] = 'Android'
                    final_log.append("Redirected to Google Play Store URL")

            # Check if there's a market:// URL in the query parameters of the current URL
            # This happens when a URL redirects to a market URL
            market_url_in_query = None
            parsed_current = urlparse(current_url)
            query_params = parse_qs(parsed_current.query)

            for param_name, param_values in query_params.items():
                for param_value in param_values:
                    if param_value.startswith('market://'):
                        market_url_in_query = param_value
                        final_log.append(f"Found market:// URL in query parameter: {param_name}")
                        break
                if market_url_in_query:
                    break

            # If we found a market URL in the query, add it to the URLs to check
            urls_to_check = [url, current_url]
            if market_url_in_query:
                urls_to_check.append(market_url_in_query)

            for check_url in urls_to_check:
                parsed_url = urlparse(check_url)

                # Check for app_token in path (only if not manually provided)
                if task_extracted_data['app_token'] == 'apptoken_needed': # Updated condition
                    if "app.adjust.com" in parsed_url.netloc or "s2s.adjust.com" in parsed_url.netloc:
                        path_segments = parsed_url.path.strip('/').split('/')
                        # The app token is typically the first segment in the path for Adjust tracking links
                        if path_segments and path_segments[0]: # Check if first segment exists
                            task_extracted_data['app_token'] = path_segments[0]
                            final_log.append(f"Extracted Adjust App Token from URL: {path_segments[0]}")

                # Look for event tokens in query parameters
                query_params = parse_qs(parsed_url.query)
                found_events = {}

                # Check for tracker_limit in query parameters (special handling)
                if 'tracker_limit' in query_params and query_params['tracker_limit'][0]:
                    event_key_name = f"Event: tracker_limit ({query_params['tracker_limit'][0]})"
                    found_events[event_key_name] = query_params['tracker_limit'][0]

                # Check for other event tokens (standard 6-character tokens)
                for key, value in query_params.items():
                    if key != 'tracker_limit' and value and value[0]:
                        # Check if it's a 6-character token (standard Adjust event token)
                        if len(value[0]) == 6:
                            event_key_name = f"Event: {key} ({value[0]})"
                            found_events[event_key_name] = value[0]

                        # Handle event_callback parameters
                        elif key.startswith('event_callback_'):
                            # Extract the callback identifier (e.g., 'glbruk' from 'event_callback_glbruk')
                            callback_id = key.replace('event_callback_', '')

                            # Check for repeated patterns like 'glbruk__glbruk__glbruk'
                            if '__' in callback_id:
                                # Just use the first part before '__'
                                callback_id = callback_id.split('__')[0]

                            # Try to extract goal_id from callback URL
                            goal_match = re.search(r'goal_id%3D(\d+)', value[0])
                            if goal_match:
                                goal_id = goal_match.group(1)
                                event_key_name = f"Event: {callback_id} (goal_id: {goal_id})"
                                found_events[event_key_name] = goal_id
                            else:
                                # If no goal_id found, use the callback_id as the event name
                                event_key_name = f"Event: {callback_id}"
                                found_events[event_key_name] = callback_id

                # Add found events to the extracted data
                if found_events:
                    # Merge with existing events rather than replacing
                    task_extracted_data['events'].update(found_events)
                    final_log.append(f"Found potential event tokens in {parsed_url.netloc}: {str(found_events)}")

            # Handle Google Play Store URLs
            if "play.google.com" in current_url:
                task_extracted_data['playstore_url'] = current_url
                package_name_match = re.search(r'id=([^&]+)', current_url)
                if package_name_match:
                    task_extracted_data['package_name'] = package_name_match.group(1)
                    final_log.append(f"Extracted Package Name: {task_extracted_data['package_name']}")
                    self.worker_thread.progress.emit("📱 Fetching Play Store page...")
                    try:
                        play_headers = {
                            'User-Agent': 'Mozilla/5.0 (Linux; Android 10; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.162 Mobile Safari/537.36',
                            'Accept-Language': 'en-US,en;q=0.5',
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                        }
                        play_response = requests.get(current_url, headers=play_headers, timeout=15, verify=verify_ssl)
                        play_response.raise_for_status()
                        html_content = play_response.text
                        final_log.append(f"✅ Successfully fetched Play Store page ({len(html_content)} chars)")

                        app_name_patterns = [
                             r'<h1.*?itemprop="name".*?>(.*?)<\/h1>', r'class="AHFaub".*?<span>(.*?)<\/span>',
                             r'<meta property="og:title" content="(.*?)">', r'<title.*?id="main-title".*?>(.*?)\s-\sApps on Google Play<\/title>',
                             r'<title.*?>(.*?)\s-\sApps on Google Play<\/title>'
                        ]
                        app_name_found = False
                        for pattern in app_name_patterns:
                            app_name_match = re.search(pattern, html_content, re.IGNORECASE | re.DOTALL)
                            if app_name_match:
                                app_name_raw = app_name_match.group(1).strip()
                                task_extracted_data['app_name'] = html.unescape(app_name_raw)
                                final_log.append(f"Extracted App Name: {task_extracted_data['app_name']}")
                                app_name_found = True; break
                        if not app_name_found: final_log.append("Could not extract app name.")

                        icon_url_patterns = [
                            r'<img.*?itemprop="image".*?src="(.*?)"', r'<meta property="og:image" content="(.*?)">',
                            r'<img.*?class="T75of.*?src="(.*?)"'
                        ]
                        icon_found = False
                        for pattern in icon_url_patterns:
                             icon_url_match = re.search(pattern, html_content, re.IGNORECASE)
                             if icon_url_match:
                                 icon_url = icon_url_match.group(1)
                                 if not icon_url.startswith('http'): icon_url = urlparse(current_url).scheme + "://" + urlparse(current_url).netloc + icon_url
                                 task_extracted_data['icon_url'] = icon_url
                                 final_log.append(f"Extracted Icon URL: {task_extracted_data['icon_url'][:60]}...")
                                 icon_found = True; break
                        if not icon_found: final_log.append("Could not extract icon URL.")

                    except requests.exceptions.RequestException as play_err:
                        final_log.append(f"Error fetching Play Store page: {play_err}")
                    except Exception as e_scrape:
                         final_log.append(f"Error scraping Play Store page: {e_scrape}")
                else:
                    final_log.append("Could not extract package name from Play Store URL.")

            # Handle iOS App Store URLs
            elif "apps.apple.com" in current_url or "itunes.apple.com" in current_url:
                task_extracted_data['appstore_url'] = current_url
                parsed_url = urlparse(current_url)
                query_params = parse_qs(parsed_url.query)

                # Extract App ID from URL
                app_id = None

                # Check if ID is in query parameters
                if 'id' in query_params:
                    app_id = query_params['id'][0]
                    final_log.append(f"Extracted App ID from query: {app_id}")
                else:
                    # Try to extract from path
                    path_parts = parsed_url.path.strip('/').split('/')
                    if len(path_parts) > 0:
                        potential_app_id = path_parts[-1]
                        if potential_app_id.isdigit():
                            app_id = potential_app_id
                            final_log.append(f"Extracted App ID from path: {app_id}")
                        # Sometimes the ID is in the form "id1354260888"
                        elif potential_app_id.startswith('id') and potential_app_id[2:].isdigit():
                            app_id = potential_app_id[2:]  # Remove the "id" prefix
                            final_log.append(f"Extracted App ID from path (id format): {app_id}")

                if app_id:
                    task_extracted_data['app_id'] = app_id

                    # Fetch App Store page to extract more info
                    self.worker_thread.progress.emit("🍎 Fetching App Store page...")
                    try:
                        # Use iOS user agent with additional headers
                        ios_headers = {
                            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1 Mobile/15E148 Safari/604.1',
                            'Accept-Language': 'en-US,en;q=0.5',
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                        }
                        app_response = requests.get(current_url, headers=ios_headers, timeout=15, verify=verify_ssl)
                        app_response.raise_for_status()
                        html_content = app_response.text
                        final_log.append(f"✅ Successfully fetched App Store page ({len(html_content)} chars)")

                        # Extract app name
                        app_name_patterns = [
                            r'<h1 class="product-header__title app-header__title">([^<]+)</h1>',
                            r'<meta property="og:title" content="([^"]+)"',
                            r'<title>([^<]+) on the App Store</title>'
                        ]

                        app_name_found = False
                        for pattern in app_name_patterns:
                            app_name_match = re.search(pattern, html_content, re.IGNORECASE | re.DOTALL)
                            if app_name_match:
                                app_name_raw = app_name_match.group(1).strip()
                                # Remove " on the App Store" suffix if present
                                app_name_raw = re.sub(r'\s*on the App Store$', '', app_name_raw)
                                task_extracted_data['app_name'] = html.unescape(app_name_raw)
                                final_log.append(f"Extracted App Name: {task_extracted_data['app_name']}")
                                app_name_found = True
                                break

                        if not app_name_found:
                            final_log.append("Could not extract app name from App Store page.")

                        # Extract app icon URL
                        icon_url_patterns = [
                            r'<meta property="og:image" content="([^"]+)"',
                            r'<link rel="apple-touch-icon" href="([^"]+)"',
                            r'<img class="product-hero__artwork" src="([^"]+)"'
                        ]

                        icon_found = False
                        for pattern in icon_url_patterns:
                            icon_url_match = re.search(pattern, html_content, re.IGNORECASE)
                            if icon_url_match:
                                icon_url = icon_url_match.group(1)
                                if not icon_url.startswith('http'):
                                    icon_url = urlparse(current_url).scheme + "://" + urlparse(current_url).netloc + icon_url
                                task_extracted_data['icon_url'] = icon_url
                                final_log.append(f"Extracted Icon URL: {task_extracted_data['icon_url'][:60]}...")
                                icon_found = True
                                break

                        if not icon_found:
                            final_log.append("Could not extract icon URL from App Store page.")

                        # Extract developer name
                        dev_patterns = [
                            r'<h2 class="product-header__identity app-header__identity">.*?<a.*?>(.*?)</a>',
                            r'<a class="link" href="/developer/[^"]+">(.*?)</a>'
                        ]

                        dev_found = False
                        for pattern in dev_patterns:
                            dev_match = re.search(pattern, html_content, re.IGNORECASE | re.DOTALL)
                            if dev_match:
                                dev_name = dev_match.group(1).strip()
                                task_extracted_data['developer'] = html.unescape(dev_name)
                                final_log.append(f"Extracted Developer: {task_extracted_data['developer']}")
                                dev_found = True
                                break

                        if not dev_found:
                            final_log.append("Could not extract developer name from App Store page.")

                        # Extract bundle ID if possible (not always available in HTML)
                        bundle_id_match = re.search(r'bundle_id=([^&"]+)', html_content)
                        if bundle_id_match:
                            bundle_id = bundle_id_match.group(1)
                            task_extracted_data['package_name'] = bundle_id
                            final_log.append(f"Extracted Bundle ID: {bundle_id}")

                    except requests.exceptions.RequestException as app_err:
                        final_log.append(f"Error fetching App Store page: {app_err}")
                    except Exception as e_scrape:
                        final_log.append(f"Error scraping App Store page: {e_scrape}")
                else:
                    final_log.append("Could not extract App ID from App Store URL.")
            else:
                final_log.append("Final URL is not a Play Store or App Store link. Some details might be missing.")

            # Determine the app key name for JSON output
            app_key_name = task_extracted_data.get('app_name', 'UnknownApp')
            pkg_name = task_extracted_data.get('package_name', 'PackageName_NotFound')
            app_id = task_extracted_data.get('app_id', '')

            if app_key_name == 'Unknown App' and pkg_name != 'PackageName_NotFound':
                app_key_name = pkg_name
            elif app_key_name == 'Unknown App' and app_id:
                app_key_name = f"iOSApp_{app_id}"
            elif app_key_name == 'Unknown App':
                app_key_name = "ExtractedApp_" + str(uuid.uuid4())[:6]

            final_json_output = {app_key_name: task_extracted_data}
            json_str = json.dumps(final_json_output, indent=4)

            full_result_text = "\n".join(final_log) + "\n\n----- FORMATTED JSON RESULT -----\n" + json_str + "\n----- END OF RESULT -----"
            return (full_result_text, task_extracted_data)

        except Exception as e:
            error_message = f"An unexpected error occurred: {e}"
            final_log.append(error_message)
            print(error_message)
            import traceback
            traceback.print_exc() # Print full traceback for debugging
            return ("\n".join(final_log), {})


    def handle_parse_result(self, result_tuple):
        display_text, extracted_data = result_tuple
        self.update_results_on_main(new_text=display_text)
        if extracted_data:
            self.extracted_data_for_json = extracted_data

            # Update app info display
            self.update_app_info_display(extracted_data)
        else:
            self.extracted_data_for_json = {}
            self.app_info_container.setVisible(False)

    def update_app_info_display(self, data):
        """Update the app info display with the extracted data."""
        # Get app data
        app_name = data.get('app_name', 'Unknown App')
        package_name = data.get('package_name', 'Unknown Package')
        app_token = data.get('app_token', 'AppToken_NotFound')
        icon_url = data.get('icon_url', '')
        platform = data.get('platform', 'Unknown')
        app_id = data.get('app_id', '')

        # Update labels
        self.app_name_label.setText(html.unescape(app_name))

        # Show different info based on platform
        if platform == 'iOS':
            # For iOS apps, show App ID and Bundle ID (if available)
            id_text = f"App ID: {app_id}"
            if package_name and package_name != 'Unknown Package' and package_name != 'PackageName_NotFound':
                id_text += f" | Bundle ID: {package_name}"
            self.package_name_label.setText(id_text)

            # Add platform indicator with iOS icon
            self.app_token_label.setText(f"🍎 iOS App | App Token: {app_token}")
        else:
            # For Android apps, show package name
            self.package_name_label.setText(f"Package: {package_name}")

            # Add platform indicator with Android icon
            self.app_token_label.setText(f"🤖 Android App | App Token: {app_token}")

        # Show the app info container
        self.app_info_container.setVisible(True)

        # Load icon if available
        if icon_url and icon_url != 'Icon_NotFound':
            # Use a worker thread to load the icon
            self.worker_thread = Worker(self._load_app_icon, icon_url)
            self.worker_thread.result.connect(self._set_app_icon)
            self.worker_thread.error.connect(lambda err: print(f"Error loading icon: {err}"))
            thread = threading.Thread(target=self.worker_thread.run)
            thread.start()
        else:
            # Set a placeholder icon with platform-specific styling
            if platform == 'iOS':
                self.app_icon_label.setText("🍎")
                self.app_icon_label.setStyleSheet(f"""
                    background-color: #1A1A1A;
                    color: #FFFFFF;
                    font-size: {dp(24)}px;
                    border-radius: {dp(5)}px;
                    border: 1px solid #444444;
                """)
            else:
                self.app_icon_label.setText("🤖")
                self.app_icon_label.setStyleSheet(f"""
                    background-color: {COLOR_GUNMETAL_2};
                    color: {COLOR_TEXT_LIGHT};
                    font-size: {dp(24)}px;
                    border-radius: {dp(5)}px;
                    border: 1px solid {COLOR_GRADIENT_LIGHT};
                """)

    def _load_app_icon(self, icon_url):
        """Load the app icon from the URL."""
        try:
            response = requests.get(icon_url, timeout=10, verify=False)
            response.raise_for_status()

            # Create a QImage from the response data
            image = QImage()
            image.loadFromData(response.content)

            if not image.isNull():
                # Scale the image to fit the label
                scaled_image = image.scaled(dp(80), dp(80), Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                return QPixmap.fromImage(scaled_image)
            else:
                return None
        except Exception as e:
            print(f"Error loading icon: {e}")
            return None

    def _set_app_icon(self, pixmap):
        """Set the app icon pixmap."""
        if pixmap:
            self.app_icon_label.setPixmap(pixmap)
            self.app_icon_label.setStyleSheet(f"""
                background-color: transparent;
                border-radius: {dp(5)}px;
            """)
        else:
            # Set a placeholder icon
            self.app_icon_label.setText("No Icon")
            self.app_icon_label.setStyleSheet(f"""
                background-color: {COLOR_GUNMETAL_2};
                color: {COLOR_TEXT_LIGHT};
                border-radius: {dp(5)}px;
                border: 1px solid {COLOR_GRADIENT_LIGHT};
            """)


    def parse_url_action(self):
        url_to_parse = self.url_input.text().strip()
        if not url_to_parse:
            self.app.show_info_popup("Input Error", "URL cannot be empty.")
            return
        self.results_display.clear()
        self.extracted_data_for_json = {}

        # Get the manual app_token if provided
        manual_app_token = self.app_token_input.text().strip() if hasattr(self, 'app_token_input') else None

        self.app.show_loading_dialog("Parsing URL...")
        self.worker_thread = Worker(self._parse_url_task, url_to_parse, manual_app_token)
        self.worker_thread.result.connect(self.handle_parse_result)
        self.worker_thread.finished.connect(self.app.hide_loading_dialog)
        self.worker_thread.error.connect(lambda err: (self.update_results_on_main(new_text=f"Worker Error: {err}"), self.app.hide_loading_dialog()))
        self.worker_thread.progress.connect(lambda msg: self.update_results_on_main(text_to_append=msg))
        thread = threading.Thread(target=self.worker_thread.run)
        thread.start()


    def add_to_local_data_action(self):
        if not self.extracted_data_for_json:
            self.app.show_info_popup("No Data", "No valid data extracted. Parse a URL first.")
            return

        # Check if we have either a package name (Android) or app ID (iOS)
        pkg_name = self.extracted_data_for_json.get('package_name', 'PackageName_NotFound')
        app_id = self.extracted_data_for_json.get('app_id', '')
        platform = self.extracted_data_for_json.get('platform', 'Unknown')

        if pkg_name == 'PackageName_NotFound' and not app_id:
            self.app.show_info_popup("No Data", "No valid package name or app ID found. Parse a URL first.")
            return

        app_name = self.extracted_data_for_json.get('app_name', 'UnknownApp')

        # Determine app key based on available data and platform
        if platform == 'iOS' and app_id:
            app_key = app_name if app_name != 'UnknownApp' else f"iOSApp_{app_id}"
        else:
            app_key = app_name if app_name != 'UnknownApp' else pkg_name if pkg_name != 'PackageName_NotFound' else None

        if not app_key:
            self.app.show_info_popup("Error", "Cannot add data: App name/package/ID not found.")
            return

        # Show confirmation dialog with editable data
        self._show_confirmation_dialog(app_key, self.extracted_data_for_json)

    def _show_confirmation_dialog(self, app_key, extracted_data):
        """Show confirmation dialog with editable extracted data"""
        dialog = QDialog(self)
        dialog.setWindowTitle("Confirm Extracted Data")
        dialog.setModal(True)
        dialog.resize(600, 500)

        # Apply dark theme styling
        dialog.setStyleSheet("""
            QDialog {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
                font-weight: bold;
            }
            QLineEdit, QTextEdit {
                background-color: #3c3c3c;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 5px;
                border-radius: 3px;
            }
            QPushButton {
                background-color: #4a90e2;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #357abd;
            }
            QPushButton:pressed {
                background-color: #2968a3;
            }
            QPushButton#cancel_btn {
                background-color: #e74c3c;
            }
            QPushButton#cancel_btn:hover {
                background-color: #c0392b;
            }
        """)

        layout = QVBoxLayout(dialog)

        # Title
        title_label = QLabel("📋 Review and Edit Extracted Data")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # Scroll area for form
        scroll = QScrollArea()
        scroll_widget = QWidget()
        form_layout = QFormLayout(scroll_widget)

        # Store field references for later access
        self.dialog_fields = {}

        # App Name
        app_name_edit = QLineEdit(extracted_data.get('app_name', 'Unknown App'))
        form_layout.addRow("🎮 App Name:", app_name_edit)
        self.dialog_fields['app_name'] = app_name_edit

        # Package Name / App ID
        platform = extracted_data.get('platform', 'Unknown')
        if platform == 'iOS':
            app_id_edit = QLineEdit(extracted_data.get('app_id', ''))
            form_layout.addRow("📱 App ID (iOS):", app_id_edit)
            self.dialog_fields['app_id'] = app_id_edit
        else:
            package_edit = QLineEdit(extracted_data.get('package_name', ''))
            form_layout.addRow("📦 Package Name:", package_edit)
            self.dialog_fields['package_name'] = package_edit

        # App Token
        app_token_edit = QLineEdit(extracted_data.get('app_token', ''))
        form_layout.addRow("🔑 App Token:", app_token_edit)
        self.dialog_fields['app_token'] = app_token_edit

        # Platform
        platform_edit = QLineEdit(extracted_data.get('platform', 'Unknown'))
        platform_edit.setReadOnly(True)
        form_layout.addRow("🖥️ Platform:", platform_edit)
        self.dialog_fields['platform'] = platform_edit

        # Store URLs
        if extracted_data.get('playstore_url'):
            playstore_edit = QLineEdit(extracted_data.get('playstore_url', ''))
            form_layout.addRow("🏪 Play Store URL:", playstore_edit)
            self.dialog_fields['playstore_url'] = playstore_edit

        if extracted_data.get('appstore_url'):
            appstore_edit = QLineEdit(extracted_data.get('appstore_url', ''))
            form_layout.addRow("🍎 App Store URL:", appstore_edit)
            self.dialog_fields['appstore_url'] = appstore_edit

        # Icon URL
        icon_edit = QLineEdit(extracted_data.get('icon_url', ''))
        form_layout.addRow("🖼️ Icon URL:", icon_edit)
        self.dialog_fields['icon_url'] = icon_edit

        # Events section
        events_label = QLabel("🎯 Event Tokens:")
        events_label.setStyleSheet("font-size: 14px; font-weight: bold; margin-top: 10px;")
        form_layout.addRow(events_label)

        events_text = QTextEdit()
        events_text.setMaximumHeight(150)

        # Format events for display
        events_display = []
        for event_name, token in extracted_data.get('events', {}).items():
            events_display.append(f"{event_name}: {token}")

        events_text.setPlainText("\n".join(events_display))
        form_layout.addRow("", events_text)
        self.dialog_fields['events'] = events_text

        scroll.setWidget(scroll_widget)
        layout.addWidget(scroll)

        # Buttons
        button_layout = QHBoxLayout()

        cancel_btn = QPushButton("❌ Cancel")
        cancel_btn.setObjectName("cancel_btn")
        cancel_btn.clicked.connect(dialog.reject)

        confirm_btn = QPushButton("✅ Confirm & Add to Database")
        confirm_btn.clicked.connect(lambda: self._confirm_and_add_data(dialog, app_key))

        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(confirm_btn)
        layout.addLayout(button_layout)

        dialog.exec_()

    def _confirm_and_add_data(self, dialog, app_key):
        """Process confirmed data and add to database"""
        try:
            # Get updated data from dialog fields
            updated_data = {}

            # Basic fields
            updated_data['app_name'] = self.dialog_fields['app_name'].text().strip()
            updated_data['app_token'] = self.dialog_fields['app_token'].text().strip()
            updated_data['platform'] = self.dialog_fields['platform'].text().strip()
            updated_data['icon_url'] = self.dialog_fields['icon_url'].text().strip()

            # Platform-specific fields
            if 'package_name' in self.dialog_fields:
                updated_data['package_name'] = self.dialog_fields['package_name'].text().strip()
            if 'app_id' in self.dialog_fields:
                updated_data['app_id'] = self.dialog_fields['app_id'].text().strip()

            # Store URLs
            if 'playstore_url' in self.dialog_fields:
                updated_data['playstore_url'] = self.dialog_fields['playstore_url'].text().strip()
            if 'appstore_url' in self.dialog_fields:
                updated_data['appstore_url'] = self.dialog_fields['appstore_url'].text().strip()

            # Parse events
            events_text = self.dialog_fields['events'].toPlainText().strip()
            events = {}
            if events_text:
                for line in events_text.split('\n'):
                    if ':' in line:
                        event_name, token = line.split(':', 1)
                        events[event_name.strip()] = token.strip()
            updated_data['events'] = events

            # Validate required fields
            if not updated_data['app_name'] or updated_data['app_name'] == 'Unknown App':
                self.app.show_info_popup("Validation Error", "App name is required.")
                return

            if not updated_data['app_token'] or updated_data['app_token'] == 'apptoken_needed':
                self.app.show_info_popup("Validation Error", "App token is required.")
                return

            # Close dialog first
            dialog.accept()

            # Add to database
            self._add_confirmed_data_to_database(app_key, updated_data)

        except Exception as e:
            self.app.show_info_popup("Error", f"Error processing data: {str(e)}")

    def _add_confirmed_data_to_database(self, app_key, data):
        """Add confirmed data to local database and optionally to API"""
        platform = data.get('platform', 'Unknown')

        # Create data structure based on platform
        if platform == 'iOS':
            data_to_add = {
                app_key: {
                    "app_token": data.get('app_token', 'AppToken_NotFound'),
                    "app_name": data.get('app_name', 'Unknown App'),
                    "app_id": data.get('app_id', ''),
                    "appstore_url": data.get('appstore_url', ''),
                    "icon_url": data.get('icon_url', 'Icon_NotFound'),
                    "platform": "iOS",
                    "events": data.get('events', {})
                }
            }
        else:
            data_to_add = {
                app_key: {
                    "app_token": data.get('app_token', 'AppToken_NotFound'),
                    "app_name": data.get('app_name', 'Unknown App'),
                    "package_name": data.get('package_name', 'PackageName_NotFound'),
                    "playstore_url": data.get('playstore_url', ''),
                    "icon_url": data.get('icon_url', 'Icon_NotFound'),
                    "platform": "Android",
                    "events": data.get('events', {})
                }
            }

        # Add to local data
        try:
            self.app.local_data.update(data_to_add)
            self.app.save_local_data()

            # Show success message
            event_count = len(data.get('events', {}))
            success_msg = f"✅ Successfully added {data['app_name']} to local database!\n"
            success_msg += f"📱 Platform: {platform}\n"
            success_msg += f"🎯 Event Tokens: {event_count}\n"
            success_msg += f"🔑 App Token: {data['app_token']}"

            self.app.show_info_popup("Success", success_msg)

            # Update results display
            self.results_display.append(f"\n✅ Added to local database: {data['app_name']}")
            self.results_display.append(f"🎯 Total events: {event_count}")

            # Optionally upload to API
            reply = QMessageBox.question(
                self,
                "Upload to API",
                "Would you like to upload this data to the API as well?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self._upload_to_api(data_to_add)

        except Exception as e:
            self.app.show_info_popup("Error", f"Failed to add data to local database: {str(e)}")

    def _upload_to_api(self, data_to_add):
        """Upload data to API"""
        try:
            # Use the API endpoint from the app
            api_url = getattr(self.app, 'api_url', 'http://localhost:5000/upload')

            response = requests.post(api_url, json=data_to_add, timeout=10)

            if response.status_code == 200:
                self.app.show_info_popup("API Success", "✅ Data successfully uploaded to API!")
                self.results_display.append("✅ Data uploaded to API successfully")
            else:
                self.app.show_info_popup("API Error", f"❌ API upload failed: {response.status_code}")
                self.results_display.append(f"❌ API upload failed: {response.status_code}")

        except Exception as e:
            self.app.show_info_popup("API Error", f"❌ Failed to upload to API: {str(e)}")
            self.results_display.append(f"❌ API upload error: {str(e)}")


    def copy_json_action(self):
        if self.extracted_data_for_json:
            app_name = self.extracted_data_for_json.get('app_name', 'UnknownApp')
            pkg_name = self.extracted_data_for_json.get('package_name', 'PackageName_NotFound')
            app_id = self.extracted_data_for_json.get('app_id', '')
            platform = self.extracted_data_for_json.get('platform', 'Unknown')

            # Determine app key based on available data and platform
            if platform == 'iOS' and app_id:
                app_key = app_name if app_name != 'UnknownApp' else f"iOSApp_{app_id}"
            else:
                app_key = app_name if app_name != 'UnknownApp' else pkg_name if pkg_name != 'PackageName_NotFound' else "ExtractedApp"

            # Create JSON structure based on platform
            if platform == 'iOS':
                json_to_copy_data = {
                    app_key: {
                        "app_token": self.extracted_data_for_json.get('app_token', 'AppToken_NotFound'),
                        "app_name": self.extracted_data_for_json.get('app_name', 'Unknown App'),
                        "package_name": self.extracted_data_for_json.get('package_name', 'PackageName_NotFound'),
                        "app_id": self.extracted_data_for_json.get('app_id', ''),
                        "appstore_url": self.extracted_data_for_json.get('appstore_url', ''),
                        "platform": "iOS",
                        "icon_url": self.extracted_data_for_json.get('icon_url', ''),
                        "developer": self.extracted_data_for_json.get('developer', ''),
                        "events": self.extracted_data_for_json.get('events', {})
                    }
                }
            else:
                json_to_copy_data = {
                    app_key: {
                        "app_token": self.extracted_data_for_json.get('app_token', 'AppToken_NotFound'),
                        "playstore_name": self.extracted_data_for_json.get('app_name', 'Unknown App'),
                        "package_name": self.extracted_data_for_json.get('package_name', 'PackageName_NotFound'),
                        "playstore_url": self.extracted_data_for_json.get('playstore_url', ''),
                        "platform": "Android",
                        "icon_url": self.extracted_data_for_json.get('icon_url', ''),
                        "events": self.extracted_data_for_json.get('events', {})
                    }
                }
            QApplication.clipboard().setText(json.dumps(json_to_copy_data, indent=4))
            self.app.show_info_popup("Copied", "Formatted JSON copied.")
            self.update_results_on_main(text_to_append="\nFormatted JSON copied.")
        else:
            self.app.show_info_popup("No Data", "No JSON data to copy.")

    def paste_url_action(self): self.url_input.setText(QApplication.clipboard().text())
    def paste_token_action(self): self.app_token_input.setText(QApplication.clipboard().text())
    def clear_fields_action(self):
        self.url_input.clear()
        if hasattr(self, 'app_token_input'):
            self.app_token_input.clear()
        self.results_display.clear()
        self.extracted_data_for_json = {}

        # Hide the app info container
        if hasattr(self, 'app_info_container'):
            self.app_info_container.setVisible(False)

# Settings Screen is now implemented above


# SerpAPI test function has been removed to optimize performance

# --- Main App Class (PyQt6) ---
class AladdinAppPyQt(QApplication): # Inherit from QApplication
    def __init__(self, argv):
        """Initialize the application with minimal settings for better stability"""
        # Initialize QApplication with minimal settings
        # No high DPI scaling attributes - PyQt6 handles this automatically

        # Initialize QApplication first - this is the most basic step
        super().__init__(argv)

        # Set application properties for better performance
        self.setQuitOnLastWindowClosed(True)

        # Initialize variables
        self.settings_file_path = None
        self.local_data_file_path = None
        self.settings = {}
        self.main_window = None
        self._loading_dialog = None

        # Defer heavy initialization to improve startup time
        QTimer.singleShot(0, self._deferred_init)

    def _deferred_init(self):
        """Perform heavy initialization tasks after the application has started"""
        # Initialize paths and settings
        self._init_paths_and_settings()

        # Apply proxy settings to the global requests session
        update_requests_session(self.settings)
        print("Applied proxy settings to global requests session")

        # Connect signals using weak references to prevent memory leaks
        game_data_cache.data_updated.connect(self.on_global_data_cache_updated,
                                           type=Qt.ConnectionType.QueuedConnection)

        # Connect HistoryDataCache signal to save history data when updated
        history_data_cache.data_updated.connect(self.on_history_data_updated,
                                              type=Qt.ConnectionType.QueuedConnection)


    def _init_paths_and_settings(self):
        # Simplified path logic from Kivy's get_user_data_path
        # Since we're on Windows, just use the Windows path
        user_dir = os.path.join(os.environ['APPDATA'], 'AladdinAppPyQt')

        # The following code is kept for reference but not used
        # if sys.platform == "win32":
        #     user_dir = os.path.join(os.environ['APPDATA'], 'AladdinAppPyQt')
        # elif sys.platform == "darwin": # macOS
        #     user_dir = os.path.join(os.path.expanduser('~'), 'Library', 'Application Support', 'AladdinAppPyQt')
        # else: # Linux and other UNIX-like
        #     user_dir = os.path.join(os.path.expanduser('~'), '.AladdinAppPyQt')

        if not os.path.exists(user_dir):
            try:
                os.makedirs(user_dir, exist_ok=True)
            except Exception as e:
                print(f"Error creating user data directory {user_dir}: {e}")
                user_dir = os.path.abspath(".") # Fallback to current dir

        self.settings_file_path = os.path.join(user_dir, 'settings_pyqt.json')
        self.local_data_file_path = os.path.join(user_dir, 'data_pyqt.json')
        self.history_data_file_path = os.path.join(user_dir, 'history_pyqt.json')
        self.settings = self.load_app_settings()

        initial_local_data = self.load_local_data_file()
        # Ensure initial_local_data is a dict before updating cache
        if isinstance(initial_local_data, dict):
            game_data_cache.update(initial_local_data) # Populate cache
        else:
            print("Warning: Local data file was not a valid dictionary. Starting with empty cache.")
            game_data_cache.update({}) # Start with empty

        # Load history data
        self.load_history_data()


    def load_app_settings(self):
        if os.path.exists(self.settings_file_path):
            try:
                with open(self.settings_file_path, 'r') as f: return json.load(f)
            except Exception as e: print(f"Error loading settings: {e}")
        return {"receive_url": DEFAULT_RECEIVE_URL, "upload_url": DEFAULT_UPLOAD_URL}

    def save_app_settings(self):
        try:
            with open(self.settings_file_path, 'w') as f: json.dump(self.settings, f, indent=4)
        except Exception as e: print(f"Error saving settings: {e}")

    def load_local_data_file(self):
        if os.path.exists(self.local_data_file_path):
            try:
                with open(self.local_data_file_path, 'r') as f:
                    data = json.load(f)
                    return data if isinstance(data, dict) else {}
            except Exception as e: print(f"Error loading local data: {e}")
        return {}

    def save_local_data_file(self, data_to_save):
        try:
            if not isinstance(data_to_save, dict): return
            # Sort data before saving for consistency
            sorted_data = {k: data_to_save[k] for k in sorted(data_to_save.keys())}
            with open(self.local_data_file_path, 'w') as f: json.dump(sorted_data, f, indent=4)
        except Exception as e: print(f"Error saving local data: {e}")

    def process_fetched_data_and_save(self, api_data):
        # Same logic as Kivy version
        if not isinstance(api_data, dict): return "Error: Invalid data from API."
        local_data = self.load_local_data_file()
        updated_count, new_count = 0, 0
        for app_name, fetched_app_data in api_data.items():
            if not isinstance(fetched_app_data, dict): continue
            if app_name in local_data:
                if not isinstance(local_data.get(app_name), dict): local_data[app_name] = {}
                # Deep merge for events, simple update for others
                for key, value in fetched_app_data.items():
                    if key == 'events' and isinstance(value, dict) and isinstance(local_data[app_name].get('events'), dict):
                        local_data[app_name]['events'].update(value)
                    else:
                        local_data[app_name][key] = value
                updated_count += 1
            else:
                local_data[app_name] = fetched_app_data
                new_count += 1
        self.save_local_data_file(local_data)
        # Update cache *after* saving, ensuring cache reflects saved state
        game_data_cache.update(local_data)
        return f"Data processed: {new_count} new, {updated_count} updated."

    def on_global_data_cache_updated(self, cache_data):
        print(f"App: Global data cache updated signal received. Cache has {len(cache_data)} items.")
        # Screens that need to react to global cache changes (like S2SScreen's spinners)
        # are already connected directly to game_data_cache.data_updated.
        # This is a good place for any app-wide reactions if needed.

    def on_history_data_updated(self, history_data):
        """Handle history data updates by saving to file"""
        print(f"App: History data updated signal received. Saving {len(history_data.get('attributions', []))} attributions and {len(history_data.get('events', []))} events...")
        self.save_history_data()

    def load_history_data(self):
        """Load history data from file and update the history_data_cache"""
        if os.path.exists(self.history_data_file_path):
            try:
                with open(self.history_data_file_path, 'r') as f:
                    history_data = json.load(f)

                # Validate the structure
                if isinstance(history_data, dict) and "attributions" in history_data and "events" in history_data:
                    # Update the history data cache
                    with history_data_cache.data_lock:
                        history_data_cache.data = history_data

                    # Emit signal to update any connected screens
                    history_data_cache.data_updated.emit(history_data_cache.data.copy())
                    print(f"Loaded history data with {len(history_data.get('attributions', []))} attributions and {len(history_data.get('events', []))} events")
                else:
                    print("Warning: History data file has invalid structure. Starting with empty history.")
            except Exception as e:
                print(f"Error loading history data: {e}")
        else:
            print("No history data file found. Starting with empty history.")

    def save_history_data(self):
        """Save history data to file"""
        try:
            with history_data_cache.data_lock:
                with open(self.history_data_file_path, 'w') as f:
                    json.dump(history_data_cache.data, f, indent=4)
            print(f"Saved history data to {self.history_data_file_path}")
            return True
        except Exception as e:
            print(f"Error saving history data: {e}")
            return False

    def show_loading_dialog(self, message="Loading..."):
        # Ensure dialog is created/shown on the main thread
        if threading.current_thread() != threading.main_thread():
             QTimer.singleShot(0, lambda: self._show_loading_dialog_main_thread(message))
        else:
             self._show_loading_dialog_main_thread(message)

    def _show_loading_dialog_main_thread(self, message):
        if self._loading_dialog is None:
            # Ensure main_window exists before creating dialog
            if not self.main_window:
                 print("Error: Main window not available for loading dialog.")
                 return
            self._loading_dialog = QDialog(self.main_window) # Parent to main window
            self._loading_dialog.setWindowTitle("Processing")
            self._loading_dialog.setModal(True)
            # Prevent closing via escape key or close button
            self._loading_dialog.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.CustomizeWindowHint | Qt.WindowType.WindowTitleHint | Qt.WindowType.WindowDoesNotAcceptFocus)

            layout = QVBoxLayout(self._loading_dialog)
            self._loading_label = QLabel(message)
            self._loading_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            pb = QProgressBar()
            pb.setRange(0,0) # Indeterminate
            layout.addWidget(self._loading_label)
            layout.addWidget(pb)
            self._loading_dialog.setFixedSize(dp(300), dp(120))
        else:
             self._loading_label.setText(message)

        # Only show if not already visible
        if not self._loading_dialog.isVisible():
             self._loading_dialog.show()
        self.processEvents() # Ensure dialog shows immediately


    def update_loading_message(self, message): # If needed by worker progress
         if threading.current_thread() != threading.main_thread():
             QTimer.singleShot(0, lambda: self._update_loading_message_main_thread(message))
         else:
             self._update_loading_message_main_thread(message)

    def _update_loading_message_main_thread(self, message):
        if self._loading_dialog and self._loading_label:
            self._loading_label.setText(message)
            self.processEvents()


    def hide_loading_dialog(self):
         if threading.current_thread() != threading.main_thread():
             QTimer.singleShot(0, self._hide_loading_dialog_main_thread)
         else:
              self._hide_loading_dialog_main_thread()

    def _hide_loading_dialog_main_thread(self):
        if self._loading_dialog:
            self._loading_dialog.hide()
            # Optionally destroy it:
            # self._loading_dialog.deleteLater()
            # self._loading_dialog = None
        self.processEvents()


    def show_info_popup(self, title, message):
        # Ensure popup is shown on the main thread
        if threading.current_thread() != threading.main_thread():
            QTimer.singleShot(0, lambda: self._show_info_popup_main_thread(title, message))
        else:
             self._show_info_popup_main_thread(title, message)

    def _show_info_popup_main_thread(self, title, message):
        # A simple popup, can be enhanced like Kivy's create_info_popup
        dialog = QDialog(self.main_window) # Parent to main window if available
        dialog.setWindowTitle(title)
        layout = QVBoxLayout(dialog)
        msg_label = QLabel(message)
        msg_label.setWordWrap(True)
        layout.addWidget(msg_label)
        ok_button = QPushButton("OK")
        ok_button.clicked.connect(dialog.accept)
        layout.addWidget(ok_button, alignment=Qt.AlignmentFlag.AlignRight)
        dialog.setMinimumWidth(dp(300)) # Use minimum width
        dialog.adjustSize() # Adjust height based on content
        dialog.exec() # Modal execution

    def set_status(self, message, timeout=0):
        """Update the status bar message.

        Args:
            message (str): The message to display
            timeout (int): Time in milliseconds before the message is cleared (0 = no timeout)
        """
        if hasattr(self, 'main_window') and self.main_window and hasattr(self.main_window, 'status_bar'):
            # Use QTimer to ensure this runs on the main thread
            if threading.current_thread() != threading.main_thread():
                QTimer.singleShot(0, lambda: self.main_window.status_bar.showMessage(message, timeout))
            else:
                self.main_window.status_bar.showMessage(message, timeout)

    def run(self):
        """Run the application with optimized startup sequence and robust error handling"""
        try:
            print("Creating main window...")
            # Create main window but defer showing it
            self.main_window = MainWindow(parent_app=self)
            print("Main window created successfully")

            # Set up a staged startup sequence for smoother user experience
            print("Setting up staged startup...")
            self._setup_staged_startup()
            print("Staged startup configured")

            # Show the main window immediately
            print("Showing main window...")
            self.main_window.show()
            print("Main window displayed")

            # Start the event loop with error handling
            print("Starting event loop...")
            return self.exec()
        except Exception as e:
            print(f"Error in application run: {e}")
            import traceback
            traceback.print_exc()
            return 1

    def _setup_staged_startup(self):
        """Set up a staged startup sequence to improve perceived performance"""
        # Stage 1: Initial UI display (already done with main_window.show())

        # Stage 2: Defer non-critical UI initialization (100ms after startup)
        QTimer.singleShot(100, self._stage2_init)

        # Stage 3: Load data and perform background tasks (500ms after startup)
        QTimer.singleShot(500, self._stage3_init)

        # Stage 4: Trigger API fetch (2000ms after startup)
        QTimer.singleShot(2000, self._stage4_init)

    def _stage2_init(self):
        """Stage 2: Initialize non-critical UI components"""
        # Update status to show progress
        self.set_status("Initializing application...", 2000)

        # Process events to keep UI responsive
        self.processEvents()

    def _stage3_init(self):
        """Stage 3: Load data and perform background tasks"""
        # Update status
        self.set_status("Loading application data...", 2000)

        # Process events to keep UI responsive
        self.processEvents()

    def _stage4_init(self):
        """Stage 4: Trigger API fetch"""
        # Find SettingsScreen and call its fetch method
        settings_screen = None

        # Use a more efficient approach to find the settings screen
        if self.main_window and self.main_window.screen_manager:
            # Check if we have a direct mapping to the settings screen
            if hasattr(self.main_window, 'screen_name_to_index') and 'settings_screen' in self.main_window.screen_name_to_index:
                index = self.main_window.screen_name_to_index.get('settings_screen')
                if index is not None:
                    settings_screen = self.main_window.screen_manager.widget(index)

            # If not found by name, search by type
            if not settings_screen:
                for i in range(self.main_window.screen_manager.count()):
                    widget = self.main_window.screen_manager.widget(i)
                    if isinstance(widget, SettingsScreen):
                        settings_screen = widget
                        break

        # Trigger data fetch if settings screen was found
        if settings_screen:
            self.set_status("Checking for updates...", 3000)
            print("Triggering initial API fetch via SettingsScreen.")
            # SettingsScreen's fetch_data_action already handles threading
            settings_screen.fetch_data_action()
        else:
            print("Could not find SettingsScreen for initial fetch.")


# Cache for dp function to avoid recalculating common values
# Pre-populate with common values for better performance
_dp_cache = {i: i for i in range(101)}  # For scale_factor = 1.0

def dp(value):
    """
    A simple dp to px conversion for PyQt with optimized caching.

    Args:
        value: The value to convert from dp to pixels

    Returns:
        int: The converted pixel value
    """
    # Fast path for integers (most common case)
    if isinstance(value, int):
        # Check if value is already in cache
        if 0 <= value <= 100:  # Most common range
            return _dp_cache[value]

        # For larger integers, just return the value (scale_factor = 1.0)
        return value

    # For floats or other types
    if value in _dp_cache:
        return _dp_cache[value]

    # Fixed scaling factor for simplicity
    scale_factor = 1.0

    # Calculate the result
    result = int(value * scale_factor)

    # Cache the result for future use
    # Only cache common values to avoid memory bloat
    if isinstance(value, float) and 0 <= value <= 100:
        _dp_cache[value] = result

    return result


def test_market_url_conversion():
    """Test function to verify the market URL conversion logic"""
    # Test with the market URL from the example
    market_url = 'market://launch?id=clubillion.social.slots.casino.friends.free.android&referrer=adjust_external_click_id%3D6e54511b940e4ae9a8075432bcabdef4'

    # Create an instance of AttributionScreen
    screen = AttributionScreen()

    # Convert to Play Store URL
    play_store_url = screen._convert_market_to_play_store(market_url)

    # Parse the original market URL
    parsed = urlparse(market_url)
    query_params = parse_qs(parsed.query)

    print('Original Market URL:', market_url)
    print('Path:', parsed.path)
    print('Package ID:', query_params.get('id', ['Not found'])[0])
    print('Referrer:', query_params.get('referrer', ['Not found'])[0] if 'referrer' in query_params else 'Not found')

    # Parse the Play Store URL
    parsed_play = urlparse(play_store_url)
    query_params_play = parse_qs(parsed_play.query)

    print('\nConverted Play Store URL:', play_store_url)
    print('Path:', parsed_play.path)
    print('Package ID:', query_params_play.get('id', ['Not found'])[0])
    print('Referrer:', query_params_play.get('referrer', ['Not found'])[0] if 'referrer' in query_params_play else 'Not found')

    return play_store_url

a = 1
b = 2

if __name__ == '__main__':
    # Check if we're running in test mode
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        test_market_url_conversion()
    else:
        try:
            # Create the QApplication instance with error handling
            print("Starting Aladdin application...")
            app = AladdinAppPyQt(sys.argv)
            print("Application instance created successfully")
            app.run()
        except Exception as e:
            print(f"Critical error: {e}")
            import traceback
            traceback.print_exc()
