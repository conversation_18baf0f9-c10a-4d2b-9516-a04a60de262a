// qquickwindow.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQuickWindow : public QWindow /ExportDerived/
{
%TypeHeaderCode
#include <qquickwindow.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QQuickImageProvider, &sipType_QQuickImageProvider, 10, 1},
        {sipName_QQuickItem, &sipType_QQuickItem, 11, 2},
        {sipName_QQuickImageResponse, &sipType_QQuickImageResponse, -1, 3},
        {sipName_QQuickItemGrabResult, &sipType_QQuickItemGrabResult, -1, 4},
        {sipName_QQuickRenderControl, &sipType_QQuickRenderControl, -1, 5},
        {sipName_QQuickTextDocument, &sipType_QQuickTextDocument, -1, 6},
        {sipName_QQuickTextureFactory, &sipType_QQuickTextureFactory, -1, 7},
        {sipName_QQuickWindow, &sipType_QQuickWindow, 13, 8},
        {sipName_QSGTexture, &sipType_QSGTexture, 14, 9},
        {sipName_QSGTextureProvider, &sipType_QSGTextureProvider, -1, -1},
        {sipName_QQuickAsyncImageProvider, &sipType_QQuickAsyncImageProvider, -1, -1},
        {sipName_QQuickFramebufferObject, &sipType_QQuickFramebufferObject, -1, 12},
        {sipName_QQuickPaintedItem, &sipType_QQuickPaintedItem, -1, -1},
        {sipName_QQuickView, &sipType_QQuickView, -1, -1},
        {sipName_QSGDynamicTexture, &sipType_QSGDynamicTexture, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    enum CreateTextureOption /BaseType=Flag/
    {
        TextureHasAlphaChannel,
        TextureHasMipmaps,
        TextureOwnsGLTexture,
        TextureCanUseAtlas,
        TextureIsOpaque,
    };

    typedef QFlags<QQuickWindow::CreateTextureOption> CreateTextureOptions;
    explicit QQuickWindow(QWindow *parent /TransferThis/ = 0);
    virtual ~QQuickWindow() /ReleaseGIL/;
    QQuickItem *contentItem() const;
    QQuickItem *activeFocusItem() const;
    virtual QObject *focusObject() const;
    QQuickItem *mouseGrabberItem() const;
    QImage grabWindow() /ReleaseGIL/;
    void setRenderTarget(const QQuickRenderTarget &target);
    QQuickRenderTarget renderTarget() const;
    QQmlIncubationController *incubationController() const;
%If (Qt_6_7_0 -)
    QSGTextNode *createTextNode() const /Factory/;
%End
    QSGTexture *createTextureFromImage(const QImage &image) const /Factory/;
    QSGTexture *createTextureFromImage(const QImage &image, QQuickWindow::CreateTextureOptions options) const /Factory/;
    void setColor(const QColor &color);
    QColor color() const;
    void setPersistentSceneGraph(bool persistent);
    bool isPersistentSceneGraph() const;

signals:
    void frameSwapped();
    void sceneGraphInitialized();
    void sceneGraphInvalidated();
    void beforeSynchronizing();
    void beforeRendering();
    void afterRendering();
    void colorChanged(const QColor &);

public slots:
    void update();
    void releaseResources();

protected:
    virtual void exposeEvent(QExposeEvent *);
    virtual void resizeEvent(QResizeEvent *);
    virtual void showEvent(QShowEvent *);
    virtual void hideEvent(QHideEvent *);
    virtual void focusInEvent(QFocusEvent *);
    virtual void focusOutEvent(QFocusEvent *);
    virtual bool event(QEvent *);
    virtual void keyPressEvent(QKeyEvent *);
    virtual void keyReleaseEvent(QKeyEvent *);
    virtual void mousePressEvent(QMouseEvent *);
    virtual void mouseReleaseEvent(QMouseEvent *);
    virtual void mouseDoubleClickEvent(QMouseEvent *);
    virtual void mouseMoveEvent(QMouseEvent *);
    virtual void wheelEvent(QWheelEvent *);
    virtual void tabletEvent(QTabletEvent *);
    virtual void closeEvent(QCloseEvent *);

public:
    static bool hasDefaultAlphaBuffer();
    static void setDefaultAlphaBuffer(bool useAlpha);

signals:
    void closing(QQuickCloseEvent *close);
    void activeFocusItemChanged();

public:
    enum SceneGraphError
    {
        ContextNotAvailable,
    };

signals:
    void afterSynchronizing();
    void afterAnimating();
    void sceneGraphAboutToStop();
    void sceneGraphError(QQuickWindow::SceneGraphError error, const QString &message);

public:
    enum RenderStage
    {
        BeforeSynchronizingStage,
        AfterSynchronizingStage,
        BeforeRenderingStage,
        AfterRenderingStage,
        AfterSwapStage,
        NoStage,
    };

    void scheduleRenderJob(QRunnable *job /Transfer/, QQuickWindow::RenderStage schedule) /ReleaseGIL/;
    qreal effectiveDevicePixelRatio() const;
    bool isSceneGraphInitialized() const;
    QSGRendererInterface *rendererInterface() const;
    static void setSceneGraphBackend(const QString &backend);
    QSGRectangleNode *createRectangleNode() const /Factory/;
    QSGImageNode *createImageNode() const /Factory/;
    static QString sceneGraphBackend();

    enum TextRenderType
    {
        QtTextRendering,
        NativeTextRendering,
%If (Qt_6_7_0 -)
        CurveTextRendering,
%End
    };

    static QQuickWindow::TextRenderType textRenderType();
    static void setTextRenderType(QQuickWindow::TextRenderType renderType);
    void beginExternalCommands();
    void endExternalCommands();

signals:
    void beforeRenderPassRecording();
    void afterRenderPassRecording();
    void beforeFrameBegin();
    void afterFrameEnd();

public:
    void setPersistentGraphics(bool persistent);
    bool isPersistentGraphics() const;
    static void setGraphicsApi(QSGRendererInterface::GraphicsApi api);
    static QSGRendererInterface::GraphicsApi graphicsApi();
    void setGraphicsDevice(const QQuickGraphicsDevice &device);
    QQuickGraphicsDevice graphicsDevice() const;
    void setGraphicsConfiguration(const QQuickGraphicsConfiguration &config);
    QQuickGraphicsConfiguration graphicsConfiguration() const;
};

class QQuickCloseEvent;

%ModuleHeaderCode
#include "qpyquick_api.h"
%End

%PostInitialisationCode
qpyquick_post_init();
%End
