// qlibrary.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QLibrary : public QObject
{
%TypeHeaderCode
#include <qlibrary.h>
%End

public:
    enum LoadHint /BaseType=Flag/
    {
        ResolveAllSymbolsHint,
        ExportExternalSymbolsHint,
        LoadArchiveMemberHint,
        PreventUnloadHint,
        DeepBindHint,
    };

    typedef QFlags<QLibrary::LoadHint> LoadHints;
    explicit QLibrary(QObject *parent /TransferThis/ = 0);
    QLibrary(const QString &fileName, QObject *parent /TransferThis/ = 0);
    QLibrary(const QString &fileName, int verNum, QObject *parent /TransferThis/ = 0);
    QLibrary(const QString &fileName, const QString &version, QObject *parent /TransferThis/ = 0);
    virtual ~QLibrary();
    QString errorString() const;
    QString fileName() const;
    bool isLoaded() const;
    bool load();
    QLibrary::LoadHints loadHints() const;
    QFunctionPointer resolve(const char *symbol);
    static QFunctionPointer resolve(const QString &fileName, const char *symbol);
    static QFunctionPointer resolve(const QString &fileName, int verNum, const char *symbol);
    static QFunctionPointer resolve(const QString &fileName, const QString &version, const char *symbol);
    bool unload();
    static bool isLibrary(const QString &fileName);
    void setFileName(const QString &fileName);
    void setFileNameAndVersion(const QString &fileName, int verNum);
    void setFileNameAndVersion(const QString &fileName, const QString &version);
    void setLoadHints(QLibrary::LoadHints hints);
};
