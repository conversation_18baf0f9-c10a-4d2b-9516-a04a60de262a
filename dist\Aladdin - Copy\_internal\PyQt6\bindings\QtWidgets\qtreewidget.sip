// qtreewidget.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTreeWidgetItem /Supertype=PyQt6.sip.wrapper/
{
%TypeHeaderCode
#include <qtreewidget.h>
%End

public:
    enum ItemType /BaseType=IntEnum/
    {
        Type,
        UserType,
    };

    QTreeWidgetItem(QTreeWidgetItem *parent /TransferThis/, QTreeWidgetItem *after, int type = QTreeWidgetItem::Type);
    QTreeWidgetItem(QTreeWidgetItem *parent /TransferThis/, const QStringList &strings, int type = QTreeWidgetItem::Type);
    QTreeWidgetItem(QTreeWidgetItem *parent /TransferThis/, int type = QTreeWidgetItem::Type);
    QTreeWidgetItem(QTreeWidget *parent /TransferThis/, QTreeWidgetItem *after, int type = QTreeWidgetItem::Type);
    QTreeWidgetItem(QTreeWidget *parent /TransferThis/, const QStringList &strings, int type = QTreeWidgetItem::Type);
    QTreeWidgetItem(QTreeWidget *parent /TransferThis/, int type = QTreeWidgetItem::Type);
    QTreeWidgetItem(const QStringList &strings, int type = QTreeWidgetItem::Type);
    explicit QTreeWidgetItem(int type = QTreeWidgetItem::Type);
    QTreeWidgetItem(const QTreeWidgetItem &other);
    virtual ~QTreeWidgetItem();
    virtual QTreeWidgetItem *clone() const /Factory/;
    QTreeWidget *treeWidget() const;
    Qt::ItemFlags flags() const;
    QString text(int column) const;
    QIcon icon(int column) const;
    QString statusTip(int column) const;
    QString toolTip(int column) const;
    QString whatsThis(int column) const;
    QFont font(int column) const;
    int textAlignment(int column) const;
%If (Qt_6_4_0 -)
    void setTextAlignment(int column, Qt::Alignment alignment);
%End
    void setTextAlignment(int column, int alignment);
    Qt::CheckState checkState(int column) const;
    void setCheckState(int column, Qt::CheckState state);
    virtual QVariant data(int column, int role) const;
    virtual void setData(int column, int role, const QVariant &value);
    virtual bool operator<(const QTreeWidgetItem &other /NoCopy/) const;
    virtual void read(QDataStream &in) /ReleaseGIL/;
    virtual void write(QDataStream &out) const /ReleaseGIL/;
    QTreeWidgetItem *parent() const;
    QTreeWidgetItem *child(int index) const;
    int childCount() const;
    int columnCount() const;
    void addChild(QTreeWidgetItem *child /Transfer/);
    void insertChild(int index, QTreeWidgetItem *child /Transfer/);
    QTreeWidgetItem *takeChild(int index) /TransferBack/;
    int type() const;
    void setFlags(Qt::ItemFlags aflags);
    void setText(int column, const QString &atext);
    void setIcon(int column, const QIcon &aicon);
    void setStatusTip(int column, const QString &astatusTip);
    void setToolTip(int column, const QString &atoolTip);
    void setWhatsThis(int column, const QString &awhatsThis);
    void setFont(int column, const QFont &afont);
    int indexOfChild(QTreeWidgetItem *achild) const;
    QSize sizeHint(int column) const;
    void setSizeHint(int column, const QSize &size);
    void addChildren(const QList<QTreeWidgetItem *> &children /Transfer/);
    void insertChildren(int index, const QList<QTreeWidgetItem *> &children /Transfer/);
    QList<QTreeWidgetItem *> takeChildren() /TransferBack/;
    QBrush background(int column) const;
    void setBackground(int column, const QBrush &brush);
    QBrush foreground(int column) const;
    void setForeground(int column, const QBrush &brush);
    void sortChildren(int column, Qt::SortOrder order);
    void setSelected(bool aselect);
    bool isSelected() const;
    void setHidden(bool ahide);
    bool isHidden() const;
    void setExpanded(bool aexpand);
    bool isExpanded() const;

    enum ChildIndicatorPolicy
    {
        ShowIndicator,
        DontShowIndicator,
        DontShowIndicatorWhenChildless,
    };

    void setChildIndicatorPolicy(QTreeWidgetItem::ChildIndicatorPolicy policy);
    QTreeWidgetItem::ChildIndicatorPolicy childIndicatorPolicy() const;
    void removeChild(QTreeWidgetItem *child /TransferBack/);
    void setFirstColumnSpanned(bool aspan);
    bool isFirstColumnSpanned() const;
    void setDisabled(bool disabled);
    bool isDisabled() const;

protected:
    void emitDataChanged();

private:
    QTreeWidgetItem &operator=(const QTreeWidgetItem &);
};

QDataStream &operator<<(QDataStream &out, const QTreeWidgetItem &item) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &in, QTreeWidgetItem &item /Constrained/) /ReleaseGIL/;

class QTreeWidget : public QTreeView
{
%TypeHeaderCode
#include <qtreewidget.h>
%End

public:
    explicit QTreeWidget(QWidget *parent /TransferThis/ = 0);
    virtual ~QTreeWidget();
    int columnCount() const;
    void setColumnCount(int columns);
    QTreeWidgetItem *topLevelItem(int index) const;
    int topLevelItemCount() const;
    void insertTopLevelItem(int index, QTreeWidgetItem *item /Transfer/);
    void addTopLevelItem(QTreeWidgetItem *item /Transfer/);
    QTreeWidgetItem *takeTopLevelItem(int index) /TransferBack/;
    int indexOfTopLevelItem(QTreeWidgetItem *item) const;
    void insertTopLevelItems(int index, const QList<QTreeWidgetItem *> &items /Transfer/);
    void addTopLevelItems(const QList<QTreeWidgetItem *> &items /Transfer/);
    QTreeWidgetItem *headerItem() const;
    void setHeaderItem(QTreeWidgetItem *item /Transfer/);
    void setHeaderLabels(const QStringList &labels);
    QTreeWidgetItem *currentItem() const;
    int currentColumn() const;
    void setCurrentItem(QTreeWidgetItem *item);
    void setCurrentItem(QTreeWidgetItem *item, int column);
    void setCurrentItem(QTreeWidgetItem *item, int column, QItemSelectionModel::SelectionFlags command);
    QTreeWidgetItem *itemAt(const QPoint &p) const;
    QTreeWidgetItem *itemAt(int ax, int ay) const;
    QRect visualItemRect(const QTreeWidgetItem *item) const;
    int sortColumn() const;
    void sortItems(int column, Qt::SortOrder order);
    void editItem(QTreeWidgetItem *item, int column = 0);
    void openPersistentEditor(QTreeWidgetItem *item, int column = 0);
    void closePersistentEditor(QTreeWidgetItem *item, int column = 0);
    QWidget *itemWidget(QTreeWidgetItem *item, int column) const;
    void setItemWidget(QTreeWidgetItem *item, int column, QWidget *widget /Transfer/);
%MethodCode
        // We have to break the association with any existing widget.  Note that I'm
        // not sure this is really necessary as it should get tidied up when Qt
        // destroys any current widget, except (possibly) when the widget wasn't
        // created from PyQt.  See also removeItemWidget(), QListWidget and
        // QTableWidget.
        QWidget *w = sipCpp->itemWidget(a0, a1);
        
        if (w)
        {
            PyObject *wo = sipGetPyObject(w, sipType_QWidget);
        
            if (wo)
                sipTransferTo(wo, 0);
        }
        
        Py_BEGIN_ALLOW_THREADS
        sipCpp->setItemWidget(a0, a1, a2);
        Py_END_ALLOW_THREADS
%End

    QList<QTreeWidgetItem *> selectedItems() const;
    QList<QTreeWidgetItem *> findItems(const QString &text, Qt::MatchFlags flags, int column = 0) const;

public slots:
    void scrollToItem(const QTreeWidgetItem *item, QAbstractItemView::ScrollHint hint = QAbstractItemView::EnsureVisible);
    void expandItem(const QTreeWidgetItem *item);
    void collapseItem(const QTreeWidgetItem *item);
    void clear();

signals:
    void itemPressed(QTreeWidgetItem *item, int column);
    void itemClicked(QTreeWidgetItem *item, int column);
    void itemDoubleClicked(QTreeWidgetItem *item, int column);
    void itemActivated(QTreeWidgetItem *item, int column);
    void itemEntered(QTreeWidgetItem *item, int column);
    void itemChanged(QTreeWidgetItem *item, int column);
    void itemExpanded(QTreeWidgetItem *item);
    void itemCollapsed(QTreeWidgetItem *item);
    void currentItemChanged(QTreeWidgetItem *current, QTreeWidgetItem *previous);
    void itemSelectionChanged();

protected:
    virtual QStringList mimeTypes() const;
    virtual QMimeData *mimeData(const QList<QTreeWidgetItem *> &items) const /TransferBack/;
    virtual bool dropMimeData(QTreeWidgetItem *parent, int index, const QMimeData *data, Qt::DropAction action);
    virtual Qt::DropActions supportedDropActions() const;

public:
    QModelIndex indexFromItem(const QTreeWidgetItem *item, int column = 0) const;
    QTreeWidgetItem *itemFromIndex(const QModelIndex &index) const;

protected:
    virtual bool event(QEvent *e);
    virtual void dropEvent(QDropEvent *event);

public:
    QTreeWidgetItem *invisibleRootItem() const /Transfer/;
    void setHeaderLabel(const QString &alabel);
    QTreeWidgetItem *itemAbove(const QTreeWidgetItem *item) const;
    QTreeWidgetItem *itemBelow(const QTreeWidgetItem *item) const;
    void removeItemWidget(QTreeWidgetItem *item, int column);
%MethodCode
        // We have to break the association with any existing widget.
        QWidget *w = sipCpp->itemWidget(a0, a1);
        
        if (w)
        {
            PyObject *wo = sipGetPyObject(w, sipType_QWidget);
        
            if (wo)
                sipTransferTo(wo, 0);
        }
        
        Py_BEGIN_ALLOW_THREADS
        sipCpp->removeItemWidget(a0, a1);
        Py_END_ALLOW_THREADS
%End

    virtual void setSelectionModel(QItemSelectionModel *selectionModel /KeepReference/);
    bool isPersistentEditorOpen(QTreeWidgetItem *item, int column = 0) const;

private:
    virtual void setModel(QAbstractItemModel *model /KeepReference/);
};
