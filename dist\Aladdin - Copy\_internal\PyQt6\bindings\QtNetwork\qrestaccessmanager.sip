// qrestaccessmanager.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_8_0 -)

class QRestAccessManager : public QObject
{
%TypeHeaderCode
#include <qrestaccessmanager.h>
%End

public:
    QRestAccessManager(QNetworkAccessManager *manager, QObject *parent /TransferThis/ = 0);
    virtual ~QRestAccessManager();
    QNetworkAccessManager *networkAccessManager() const;
    QNetworkReply *deleteResource(const QNetworkRequest &request) /Transfer/;
    QNetworkReply *head(const QNetworkRequest &request) /Transfer/;
    QNetworkReply *get(const QNetworkRequest &request) /Transfer/;
    QNetworkReply *get(const QNetworkRequest &request, const QByteArray &data) /Transfer/;
    QNetworkReply *get(const QNetworkRequest &request, const QJsonDocument &data) /Transfer/;
    QNetworkReply *get(const QNetworkRequest &request, QIODevice *data) /Transfer/;
    QNetworkReply *post(const QNetworkRequest &request, const QJsonDocument &data) /Transfer/;
    QNetworkReply *post(const QNetworkRequest &request, const QVariantMap &data) /Transfer/;
    QNetworkReply *post(const QNetworkRequest &request, const QByteArray &data) /Transfer/;
    QNetworkReply *post(const QNetworkRequest &request, QHttpMultiPart *data) /Transfer/;
    QNetworkReply *post(const QNetworkRequest &request, QIODevice *data) /Transfer/;
    QNetworkReply *put(const QNetworkRequest &request, const QJsonDocument &data) /Transfer/;
    QNetworkReply *put(const QNetworkRequest &request, const QVariantMap &data) /Transfer/;
    QNetworkReply *put(const QNetworkRequest &request, const QByteArray &data) /Transfer/;
    QNetworkReply *put(const QNetworkRequest &request, QHttpMultiPart *data) /Transfer/;
    QNetworkReply *put(const QNetworkRequest &request, QIODevice *data) /Transfer/;
    QNetworkReply *patch(const QNetworkRequest &request, const QJsonDocument &data) /Transfer/;
    QNetworkReply *patch(const QNetworkRequest &request, const QVariantMap &data) /Transfer/;
    QNetworkReply *patch(const QNetworkRequest &request, const QByteArray &data) /Transfer/;
    QNetworkReply *patch(const QNetworkRequest &request, QIODevice *data) /Transfer/;
    QNetworkReply *sendCustomRequest(const QNetworkRequest &request, const QByteArray &method, const QByteArray &data) /Transfer/;
    QNetworkReply *sendCustomRequest(const QNetworkRequest &request, const QByteArray &method, QIODevice *data) /Transfer/;
    QNetworkReply *sendCustomRequest(const QNetworkRequest &request, const QByteArray &method, QHttpMultiPart *data) /Transfer/;
};

%End
