// qformlayout.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QFormLayout : public QLayout
{
%TypeHeaderCode
#include <qformlayout.h>
%End

public:
    enum FieldGrowthPolicy
    {
        FieldsStayAtSizeHint,
        ExpandingFieldsGrow,
        AllNonFixedFieldsGrow,
    };

    enum RowWrapPolicy
    {
        DontWrapRows,
        WrapLongRows,
        WrapAllRows,
    };

    enum ItemRole
    {
        LabelRole,
        FieldRole,
        SpanningRole,
    };

    explicit QFormLayout(QWidget *parent /TransferThis/ = 0);
    virtual ~QFormLayout();
    void setFieldGrowthPolicy(QFormLayout::FieldGrowthPolicy policy);
    QFormLayout::FieldGrowthPolicy fieldGrowthPolicy() const;
    void setRowWrapPolicy(QFormLayout::RowWrapPolicy policy);
    QFormLayout::RowWrapPolicy rowWrapPolicy() const;
    void setLabelAlignment(Qt::Alignment alignment);
    Qt::Alignment labelAlignment() const;
    void setFormAlignment(Qt::Alignment alignment);
    Qt::Alignment formAlignment() const;
    void setHorizontalSpacing(int spacing);
    int horizontalSpacing() const;
    void setVerticalSpacing(int spacing);
    int verticalSpacing() const;
    virtual int spacing() const;
    virtual void setSpacing(int);
    void addRow(QWidget *label /Transfer/, QWidget *field /Transfer/);
    void addRow(QWidget *label /Transfer/, QLayout *field /Transfer/);
    void addRow(const QString &labelText, QWidget *field /Transfer/);
    void addRow(const QString &labelText, QLayout *field /Transfer/);
    void addRow(QWidget *widget /Transfer/);
    void addRow(QLayout *layout /Transfer/);
    void insertRow(int row, QWidget *label /Transfer/, QWidget *field /Transfer/);
    void insertRow(int row, QWidget *label /Transfer/, QLayout *field /Transfer/);
    void insertRow(int row, const QString &labelText, QWidget *field /Transfer/);
    void insertRow(int row, const QString &labelText, QLayout *field /Transfer/);
    void insertRow(int row, QWidget *widget /Transfer/);
    void insertRow(int row, QLayout *layout /Transfer/);
    void setItem(int row, QFormLayout::ItemRole role, QLayoutItem *item /Transfer/);
    void setWidget(int row, QFormLayout::ItemRole role, QWidget *widget /Transfer/);
    void setLayout(int row, QFormLayout::ItemRole role, QLayout *layout /Transfer/);
    QLayoutItem *itemAt(int row, QFormLayout::ItemRole role) const;
    void getItemPosition(int index, int *rowPtr, QFormLayout::ItemRole *rolePtr) const;
    void getWidgetPosition(QWidget *widget, int *rowPtr, QFormLayout::ItemRole *rolePtr) const;
    void getLayoutPosition(QLayout *layout, int *rowPtr, QFormLayout::ItemRole *rolePtr) const;
    QWidget *labelForField(QWidget *field) const;
    QWidget *labelForField(QLayout *field) const;
    virtual void addItem(QLayoutItem *item /Transfer/);
    virtual QLayoutItem *itemAt(int index) const;
    virtual QLayoutItem *takeAt(int index) /TransferBack/;
    virtual void setGeometry(const QRect &rect);
    virtual QSize minimumSize() const;
    virtual QSize sizeHint() const;
    virtual void invalidate();
    virtual bool hasHeightForWidth() const;
    virtual int heightForWidth(int width) const;
    virtual Qt::Orientations expandingDirections() const;
    virtual int count() const;
    int rowCount() const;

    struct TakeRowResult
    {
%TypeHeaderCode
#include <qformlayout.h>
%End

        QLayoutItem *labelItem;
        QLayoutItem *fieldItem;
    };

    void removeRow(int row);
    void removeRow(QWidget *widget);
    void removeRow(QLayout *layout);
    QFormLayout::TakeRowResult takeRow(int row);
    QFormLayout::TakeRowResult takeRow(QWidget *widget);
    QFormLayout::TakeRowResult takeRow(QLayout *layout);
%If (Qt_6_4_0 -)
    void setRowVisible(QLayout *layout, bool on);
%End
%If (Qt_6_4_0 -)
    void setRowVisible(QWidget *widget, bool on);
%End
%If (Qt_6_4_0 -)
    void setRowVisible(int row, bool on);
%End
%If (Qt_6_4_0 -)
    bool isRowVisible(QLayout *layout) const;
%End
%If (Qt_6_4_0 -)
    bool isRowVisible(QWidget *widget) const;
%End
%If (Qt_6_4_0 -)
    bool isRowVisible(int row) const;
%End
};
